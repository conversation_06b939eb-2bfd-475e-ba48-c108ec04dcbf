name: Deploy Load Test

on: workflow_dispatch

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Start Deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: load-test
          ref: ${{ github.head_ref }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Add Firebase Key file
        run: |
          cat << EOF > sala-hub-firebase.json
          ${{ secrets.FIREBASE_KEY_FILE }}
          EOF

      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
          tools: composer:v2
          coverage: none

      - name: Get Composer Cache Directory 📂
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer ➕
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies 🤖
        run: composer install --no-dev --no-progress --prefer-dist --no-interaction --no-suggest --optimize-autoloader --quiet

      - name: Cache NPM ➕
        id: cache-npm
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Create Vapor Environment
        continue-on-error: true
        run: |
          php vendor/bin/vapor env load-test
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Deploy Environment
        run: php vendor/bin/vapor deploy load-test --commit="${{ github.sha }}" --manifest=vapor.load-test.yml
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Upload Failure Artifacts 😰
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Logs
          path: ./storage/logs

      - name: Update Backend Deployment Link Status
        if: always()
        uses: bobheadxi/deployments@v1
        with:
          step: finish
          env_url: https://sala-load-test
          token: ${{ secrets.GITHUB_TOKEN }}
          env: ${{ steps.deployment.outputs.env }}
          status: ${{ job.status }}
          logs: https://sala-load-test
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}

  slack:
    runs-on: ubuntu-latest
    needs: [deploy]
    if: "always() && (needs.deploy.outputs.job_status == 'success')"
    steps:
      - name: Slack Notify Deployment Failure 🔔
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.ACTION_MONITORING_SLACK }}
          SLACK_COLOR: 'danger'
          SLACK_TITLE: Laravel Vapor Deployment failed for Load Test
          SLACK_USERNAME: Github
