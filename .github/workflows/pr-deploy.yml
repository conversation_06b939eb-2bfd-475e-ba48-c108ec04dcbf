name: Deploy PR

on: pull_request

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: extract branch name
        id: get_branch
        shell: bash
        env:
          PR_HEAD: ${{ github.head_ref }}
        run: echo "##[set-output name=branch;]$(echo ${PR_HEAD#refs/heads/} | tr / -)"

      - name: start deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: ${{ steps.get_branch.outputs.branch }}
          ref: ${{ github.head_ref }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Write secret to file
        run: |
          cat << EOF > sala-hub-firebase.json
          ${{ secrets.FIREBASE_KEY_FILE }}
          EOF

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
          tools: composer:v2
          coverage: none

      - name: Get Composer Cache Directory 📂
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer ➕
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies 🤖
        run: composer install --no-dev --no-progress --prefer-dist --no-interaction --no-suggest --optimize-autoloader --quiet

      - name: Cache NPM ➕
        id: cache-npm
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Prepare Vapor PR files
        run: |
          sed -i 's/staging:/pr-${{ github.event.pull_request.number }}:/g' vapor.yml
          sed -i 's/salahub.ninja/pr-${{ github.event.pull_request.number }}.salahub.ninja/g' vapor.yml

      - name: Create Vapor Environment
        continue-on-error: true
        run: |
          php vendor/bin/vapor env:clone staging pr-${{ github.event.pull_request.number }}
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Deploy Environment
        run: php vendor/bin/vapor deploy pr-${{ github.event.pull_request.number }} --commit="${{ github.sha }}"
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Upload Failure Artifacts 😰
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Logs
          path: ./storage/logs

      - name: Update Backend Deployment Link Status
        if: always()
        uses: bobheadxi/deployments@v1
        with:
          step: finish
          env_url: https://pr-${{ github.event.pull_request.number }}.salahub.ninja
          token: ${{ secrets.GITHUB_TOKEN }}
          env: ${{ steps.deployment.outputs.env }}
          status: ${{ job.status }}
          logs: https://pr-${{ github.event.pull_request.number }}.salahub.ninja
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}

  # phpcs:
  #   name: PHPCS
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v4
  #     - name: Install dependencies
  #       run: composer install --dev --prefer-dist --no-progress --no-suggest
  #     - name: PHPCS check
  #       uses: chekalsky/phpcs-action@v1
  #       with:
  #         phpcs_bin_path: "./vendor/bin/phpcs"

  slack:
    runs-on: ubuntu-latest
    needs: [deploy]
    if: "always() && (needs.deploy.outputs.job_status == 'success')"
    steps:
      - name: Slack Notify Deployment Failure 🔔
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.ACTION_MONITORING_SLACK }}
          SLACK_COLOR: 'danger'
          SLACK_TITLE: Laravel Vapor Deployment failed for PR ${{ github.event.pull_request.number }}
          SLACK_USERNAME: Github
