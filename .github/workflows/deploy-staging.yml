name: Deploy Staging

on:
  push:
    branches: [develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: start deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: staging

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Add Firebase Key file
        run: |
          cat << EOF > sala-hub-firebase.json
          ${{ secrets.FIREBASE_KEY_FILE }}
          EOF

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
          tools: composer:v2
          coverage: none

      - name: Get Composer Cache Directory 📂
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer ➕
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Cache NPM ➕
        id: cache-npm
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install composer dependencies 🤖
        run: composer install --no-dev --no-progress --prefer-dist --no-interaction --no-suggest --optimize-autoloader --quiet

      - name: Deploy Environment
        run: php vendor/bin/vapor deploy staging --commit="${{ github.sha }}"
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Upload Failure Artifacts 😰
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Logs
          path: ./storage/logs

      - name: Slack Notify Deployment Failure 🔔
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.ACTION_MONITORING_SLACK }}
          SLACK_COLOR: 'danger'
          SLACK_TITLE: Laravel Vapor Deployment failed for STAGING
          SLACK_USERNAME: Github

      - name: update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          env: ${{ steps.deployment.outputs.env }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}

  Sentry:
    runs-on: ubuntu-latest
    needs: deploy
    steps:
      - uses: actions/checkout@v4
      - name: Create Sentry release
        uses: getsentry/action-release@v3
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: tcf-sala
          SENTRY_PROJECT: backend
        with:
          environment: staging

  # Apollo-Studio:
  #   runs-on: ubuntu-latest
  #   needs: deploy
  #   steps:
  #     - name: Install Rover
  #       run: |
  #         curl -sSL https://rover.apollo.dev/nix/v0.1.0 | sh

  #         # Add Rover to the $GITHUB_PATH so it can be used in another step
  #         # https://docs.github.com/en/actions/reference/workflow-commands-for-github-actions#adding-a-system-path
  #         echo "$HOME/.rover/bin" >> $GITHUB_PATH
  #     - name: Run check against prod
  #       run: |
  #         rover graph introspect https://limitless-summer-utjsedi4gco3.vapor-farm-d1.com/graphql | rover graph publish tcf-sala@staging --schema -
