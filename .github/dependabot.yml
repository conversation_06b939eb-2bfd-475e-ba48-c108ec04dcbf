# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      # Check for updates to GitHub Actions every week
      interval: 'weekly'
  - package-ecosystem: 'composer' # See documentation for possible values
    directory: '/' # Location of package manifests
    schedule:
      interval: 'weekly'
    versioning-strategy: increase-if-necessary
  - package-ecosystem: 'npm' # See documentation for possible values
    directory: '/' # Location of package manifests
    schedule:
      interval: 'weekly'
    versioning-strategy: increase-if-necessary
    groups:
      minor-and-patch-security:
        applies-to: security-updates
        update-types:
          - 'patch'
          - 'minor'
      minor-and-patch-updates:
        applies-to: version-updates
        update-types:
          - 'patch'
          - 'minor'
