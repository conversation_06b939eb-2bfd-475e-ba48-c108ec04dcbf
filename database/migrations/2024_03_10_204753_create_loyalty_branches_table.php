<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_branches', function (Blueprint $table) {
            $table->id();
            $table
                ->foreignId('branch_id')
                ->nullable()
                ->constrained()
                ->on('branches')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table
                ->foreignId('loyalty_id')
                ->nullable()
                ->constrained()
                ->on('loyalties')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_branches');
    }
};