<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentStatusLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //'fort_id', 'order_id', 'merchant_reference', 'fort_status', 'payment_status', 'data'
        Schema::create('payment_status_logs', function (Blueprint $table) {
            $table->id();
            $table
                ->foreignId('order_id')
                ->nullable()
                ->constrained()
                ->on('orders')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('fort_id')->nullable();
            $table->string('merchant_reference')->nullable();
            $table->string('fort_status')->nullable();
            $table->integer('payment_status')->nullable();

            $table->longText('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_status_logs');
    }
}
