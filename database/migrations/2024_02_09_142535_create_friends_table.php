<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('friends', function (Blueprint $table) {
            $table->id();
            $table->string('email')->nullable();
            $table->string('mobile')->nullable();
            $table->integer('status')->nullable(); //0-1-2-3
            $table->dateTime('rejected_at')->nullable();
            $table->dateTime('accepted_at')->nullable();
            $table
                ->enum('invite_by', ['email', 'mobile'])
                ->default('email')
                ->nullable();

            $table
                ->foreignId('family_relation_id')
                ->nullable()
                ->constrained()
                ->on('family_relations')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->text('reason')->nullable();

            $table
                ->foreignId('sender_id')
                ->nullable()
                ->constrained()
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table
                ->foreignId('user_id')
                ->nullable()
                ->constrained()
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->softDeletes();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('friends');
    }
};
