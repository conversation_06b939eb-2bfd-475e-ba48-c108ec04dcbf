<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            //
            $table
                ->foreignId('bank_offer_id')
                ->nullable()
                ->constrained()
                ->on('bank_offers')
                ->onUpdate('cascade')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            //
            $table->dropForeign(['bank_offer_id']);
            $table->dropColumn('bank_offer_id');
        });
    }
};
