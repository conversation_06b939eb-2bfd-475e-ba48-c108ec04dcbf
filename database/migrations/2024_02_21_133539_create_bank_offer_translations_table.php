<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_offer_translations', function (Blueprint $table) {
            $table->id();
            $table
                ->foreignId('bank_offer_id')
                ->constrained()
                ->on('bank_offers')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('locale')->index();
            $table->string('title')->nullable();

            $table->unique(['bank_offer_id', 'locale']);
            $table->softDeletes();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_offer_translations');
    }
};