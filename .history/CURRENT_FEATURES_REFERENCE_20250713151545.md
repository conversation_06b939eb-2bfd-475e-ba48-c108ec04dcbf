# Sala Hub - Current Features Reference

## Platform Overview
Sala Hub is a comprehensive entertainment booking platform serving Saudi Arabia's entertainment sector, featuring multiple entertainment brands under one unified mobile application and admin system.

## Entertainment Brands Supported
- **Strike 10** - Bowling entertainment
- **Octo City** - Multi-entertainment complex
- **Octo Laser Tag** - Laser tag gaming
- **Kidzania** - Children's educational entertainment
- **Octo Karting** - Go-kart racing
- **<PERSON>** - Children's play center
- **Jumping Beez** - Trampoline park
- **Bob's** - Family entertainment center

## Core Business Features

### 1. User Management & Authentication
- User registration and profile management
- Multi-language support (Arabic/English)
- Social login integration (Apple, Google)
- Role-based access control with permissions
- Team and organizational email management
- User groups and custom groups
- Referral system with tracking codes

### 2. Booking & Ticketing System
- Real-time ticket availability checking
- Multi-brand booking in single transaction
- Date and time slot selection
- Branch-specific booking management
- Special ticket types (normal/special)
- Group booking requests
- Birthday event booking with time slots
- Bulk ticket creation and management
- QR code generation for tickets

### 3. Order Management
- Comprehensive order processing
- Order status tracking (pending, confirmed, completed, cancelled)
- Payment status management
- Order expiration handling
- Corporate booking support
- Order refund processing
- Order transfer capabilities
- Digital receipt generation

### 4. Payment Processing
- Multiple payment methods support
- PayFort payment gateway integration
- Digital wallet functionality
- Wallet transactions (add/deduct credits)
- Payment status tracking
- Refund processing
- ZATCA invoice compliance
- Card management and storage

### 5. Loyalty & Rewards System
- Points earning on purchases
- Brand-specific loyalty rates
- Referral bonus points
- Points redemption system
- Loyalty transaction history
- Automated point allocation
- Loyalty notifications

### 6. Promotional System
- Coupon management and validation
- Bundle offers and packages
- Bank offer integrations
- Discount calculations
- Promotional campaigns
- Referral promotions
- Birthday promotions
- Registration bonuses

### 7. Notification System
- Push notifications via Firebase FCM
- SMS notifications
- Email notifications
- In-app notifications
- Multi-language notification support
- Order status notifications
- Loyalty point notifications
- Promotional notifications

### 8. Location & Branch Management
- Multi-governorate support
- Branch-specific operations
- Working hours management
- Branch off-days handling
- Location-based services
- Governorate-based filtering

### 9. Content Management
- Multi-language content support
- Banner management
- Video content management
- FAQ system
- Terms and conditions
- Brand-specific content
- Social media links management

### 10. Feedback & Survey System
- Customer feedback collection
- Subject-based feedback categorization
- File attachments support
- Survey creation and management
- User survey responses
- Feedback status tracking
- Reference number generation

## Technical Features

### 1. API Architecture
- GraphQL API implementation
- RESTful endpoints
- Real-time data caching
- API rate limiting
- Authentication via Laravel Sanctum
- Mobile-optimized responses

### 2. Admin Dashboard
- Inertia.js + Vue.js frontend
- Real-time analytics
- Order management interface
- User management tools
- Content management system
- Report generation
- Export functionality (CSV, JSON)

### 3. Mobile App Support
- iOS and Android compatibility
- Push notification support
- Offline capability considerations
- QR code scanning
- Location services integration
- Deep linking support

### 4. Analytics & Reporting
- Activity logging system
- User behavior tracking
- Order analytics
- Revenue reporting
- Export capabilities
- Performance monitoring via Clockwork
- Error tracking via Sentry

### 5. Integration Capabilities
- Foodics POS integration
- Google Services integration
- Social media platform integration
- Third-party payment gateways
- SMS service providers
- Email service providers

## Data Management Features

### 1. User Data
- Personal information management
- Preference tracking
- Booking history
- Payment history
- Loyalty point balance
- Social connections (friends)

### 2. Business Intelligence
- Sales analytics
- User segmentation
- Revenue tracking
- Performance metrics
- Operational reports
- Customer insights

### 3. Security Features
- Data encryption
- Secure payment processing
- User authentication
- Permission-based access
- Activity logging
- Data backup and recovery