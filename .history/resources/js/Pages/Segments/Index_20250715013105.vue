<template>
    <admin-layout>
        <section class="section">
            <div class="section-header">
                <h1>{{ __('Segments') }}</h1>
                <div class="section-header-breadcrumb">
                    <div class="breadcrumb-item active">
                        <inertia-link :href="route('dashboard')">{{ __('Dashboard') }}</inertia-link>
                    </div>
                    <div class="breadcrumb-item">{{ __('Segments') }}</div>
                </div>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>{{ __('Segment Stats') }}</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Segment') }}</th>
                                                <th>{{ __('Users Count') }}</th>
                                                <th>{{ __('Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="segment in segments" :key="segment.id">
                                                <td>{{ segment.title }}</td>
                                                <td>{{ segment.users_count }}</td>
                                                <td>
                                                    <inertia-link :href="route('segments.show', segment.id)" class="btn btn-primary">{{ __('View Users') }}</inertia-link>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </admin-layout>
</template>

<script>
import AdminLayout from '@/Layouts/AdminLayout';

export default {
    components: {
        AdminLayout,
    },
    props: {
        segments: {
            type: Array,
            required: true,
        },
    },
};
</script>
