<template>
    <admin-layout>
        <section class="section">
            <div class="section-header">
                <h1>{{ data.title }}</h1>
                <breadcrumb :links="[
                    {
                        route: 'dashboard',
                        name: __('Dashboard'),
                    },
                    {
                        route: 'users.index',
                        name: __('Users'),
                    },
                    {
                        route: 'users.show',
                        name: data.userInfo.name,
                        params: data.userInfo.id
                    },
                ]"></breadcrumb>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>{{ data.userInfo.name }}</h4>
                            </div>
                            <div class="card-body">
                                <p><strong>{{ __('Email') }}:</strong> {{ data.userInfo.email }}</p>
                                <p><strong>{{ __('Mobile') }}:</strong> {{ data.userInfo.mobile }}</p>
                                <p><strong>{{ __('Country') }}:</strong> {{ data.userInfo.country }}</p>
                                <p><strong>{{ __('City') }}:</strong> {{ data.userInfo.city }}</p>
                                <p><strong>{{ __('Date of Birth') }}:</strong> {{ data.userInfo.date_of_birth }}</p>
                                <p><strong>{{ __('Nationality') }}:</strong> {{ data.userInfo.nationality }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>{{ __('Segments') }}</h4>
                            </div>
                            <div class="card-body">
                                <ul class="list-group">
                                    <li v-for="segment in data.userInfo.segments" :key="segment.id" class="list-group-item">
                                        {{ segment.title }}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </admin-layout>
</template>

<script>
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Breadcrumb';

export default {
    components: {
        AdminLayout,
        Breadcrumb,
    },
    props: {
        data: Object,
    },
};
</script>
