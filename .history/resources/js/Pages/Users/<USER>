<template>
    <admin-layout>
        <section class="section">
            <div class="section-header">
                <h1>{{ __('UserInfo') }}</h1>
                <breadcrumb :links="breadCrumbLinks"></breadcrumb>
            </div>
            <div class="section-body">
                <h2 class="section-title">{{ data.info.name }}</h2>
                <p class="section-lead">{{ __('UserInfoDescription') }}</p>

                <div class="row mt-sm-4">
                    <div class="col-12 col-md-12 col-lg-6">
                        <div class="card profile-widget">
                            <div class="profile-widget-header">
                                <img
                                    alt="image"
                                    :src="data.info.photo_path"
                                    class="rounded-circle profile-widget-picture"
                                />
                                <div class="profile-widget-items">
                                    <div class="profile-widget-item">
                                        <div class="profile-widget-item-label">{{ __('UpcomingOrder') }}</div>
                                        <div class="profile-widget-item-value">{{ data.upcoming }}</div>
                                    </div>
                                    <div class="profile-widget-item">
                                        <div class="profile-widget-item-label">{{ __('ConfirmedOrder') }}</div>
                                        <div class="profile-widget-item-value">{{ data.confirmed }}</div>
                                    </div>
                                    <div class="profile-widget-item">
                                        <div class="profile-widget-item-label">{{ __('CancelledOrder') }}</div>
                                        <div class="profile-widget-item-value">{{ data.cancelled }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="profile-widget-description">
                                <div class="profile-widget-name">
                                    {{ data.info.name }}
                                    <div class="text-muted d-inline font-weight-normal">
                                        <div class="slash"></div>
                                        {{ __('JoinAt') }}: {{ data.info.date }}

                                        <div class="slash"></div>
                                        {{ __('referral_code') }}: {{ data.info.referral_code }}

                                        <p class="mt-20">
                                            <strong class="section-lead" v-html="data.info.status_span"></strong>
                                            <strong class="section-lead badge badge-primary">{{
                                                __(data.info.gender)
                                            }}</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            

                            <div class="card-footer text-center">
                                <div v-if="data.roles.length > 0" class="font-weight-bold mb-2">
                                    {{ __('HaveRoles') }}
                                </div>
                                <span
                                    v-for="role in data.roles"
                                    :key="role.id"
                                    class="badge badge-light text-primary"
                                    >{{ role.name }}</span
                                >
                            </div>
                        </div>

                        <div v-if="data.info.segments && data.info.segments.length > 0" class="card">
                            <div class="card-header">
                                <h4>{{ __('Segments') }}</h4>
                            </div>
                            <div class="card-body">
                                <ul class="list-group">
                                    <li v-for="segment in data.info.segments" :key="segment.id" class="list-group-item">
                                        {{ segment.title }}
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-lg-4 col-md-6 col-sm-6 col-12">
                                    <div class="card card-statistic-1">
                                        <div class="card-icon-2 bg-success">
                                            <i class="far fa-user"></i>
                                        </div>
                                        <div class="card-wrap text-center pb-2">
                                            <div class="card-header">
                                                <h4 class="card-header-title">{{ __('TotalFriend') }}</h4>
                                            </div>
                                            <div class="card-body">
                                                <a :href="route('friends.index', { status: 1, id: data.info.id })">{{
                                                    data.info.friends_count
                                                }}</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-6 col-12">
                                    <div class="card card-statistic-1">
                                        <div class="card-icon-2 bg-danger">
                                            <i class="far fa-newspaper"></i>
                                        </div>
                                        <div class="card-wrap text-center pb-2">
                                            <div class="card-header">
                                                <h4 class="card-header-title">{{ __('TotalPendingFriendRequest') }}</h4>
                                            </div>
                                            <div class="card-body">
                                                <a :href="route('friends.index', { status: 0, id: data.info.id })">
                                                    {{ data.info.pending_friends_count }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-6 col-12">
                                    <div class="card card-statistic-1">
                                        <div class="card-icon-2 bg-warning">
                                            <i class="far fa-file"></i>
                                        </div>
                                        <div class="card-wrap text-center pb-2">
                                            <div class="card-header">
                                                <h4 class="card-header-title">
                                                    {{ __('TotalReceivedFriendRequest') }}
                                                </h4>
                                            </div>
                                            <div class="card-body">
                                                <a :href="route('friends.index', { status: 2, id: data.info.id })">{{
                                                    data.info.received_friends_count
                                                }}</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div v-if="members.data.length > 0" class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>{{ __('Member') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('CreationDate') }}</th>
                                    </tr>
                                    <tr
                                        v-for="row in members.data"
                                        :key="row.id"
                                        :class="{ disabled: !row.active }"
                                        class="bg-emerald-50"
                                    >
                                        <td>
                                            <div v-if="row.sender_id != data.info.id" class="text-sm text-gray-900">
                                                {{ row.user ? row.user.name : '' }}
                                            </div>
                                            <div
                                                v-if="row.email != data.info.email && row.mobile != data.info.mobile"
                                                class="text-sm text-gray-900"
                                            >
                                                <span v-if="row.email">{{ row.email }}</span>
                                                <span v-if="row.mobile">{{ row.mobile }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="profile-widget-item-value" v-html="row.status_span"></div>
                                        </td>
                                        <td>{{ row.date }}</td>
                                    </tr>
                                </table>
                                <pagination :links="members" />
                            </div>
                            <div v-else class="text-center alert alert-light m-4">No friends found!</div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-lg-6">
                        <div class="card">
                            <form method="post" class="needs-validation" novalidate="">
                                <div class="card-header">
                                    <h4>{{ __('ProfileData') }}</h4>
                                </div>
                                <div class="card-body">
                                    <div v-if="data.info.referral_token" class="row">
                                        <div id="print" class="form-group col-md-6 col-6 text-center print">
                                            <qrcode-vue id="qrcode" :value="qrCode" :size="size" level="H" />
                                        </div>
                                        <div class="form-group col-md-6 col-6">
                                            <label> {{ __('referral_count') }}</label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.referral_count"
                                                disabled
                                                readonly
                                            />

                                            <button
                                                v-if="qrCode"
                                                type="button"
                                                class="btn btn-primary mt-5"
                                                @click="downloadUrl"
                                            >
                                                <i class="fas fa-download"></i> {{ __('download_qr') }}
                                            </button>

                                            <button
                                                v-if="can('cashiers.copy')"
                                                type="button"
                                                title="copy to Clipboard"
                                                class="btn btn-warning ml-2 mt-5"
                                                @click="copy(data.info.referral_link)"
                                            >
                                                <i class="fas fa-copy"></i>
                                            </button>

                                            <button
                                                v-if="can('cashiers.print')"
                                                type="button"
                                                title="print"
                                                class="btn btn-danger ml-2 mt-5"
                                                @click="print()"
                                            >
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('FirstName') }}</label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.first_name"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('LastName') }}</label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.last_name"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('Email') }} </label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.email"
                                                disabled
                                                readonly
                                            />
                                        </div>

                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('Phone') }}</label>
                                            <input
                                                disabled
                                                readonly
                                                type="text"
                                                class="form-control"
                                                :value="data.info.mobile"
                                            />
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('OrganizationalEmail') }} </label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.organizational_email"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('OrganizationalEmailExpiration') }} </label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.expire_email_date"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                        <div class="form-group col-md-6 col-12">
                                            <label> {{ __('VerifiedOrganizationalEmail') }} </label>
                                            <span v-if="isExpired()" class="form-control">
                                                {{ __('No') }}
                                            </span>
                                            <span v-else class="form-control">
                                                {{ __('Yes') }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label>{{ __('Country') }}</label>
                                            <input
                                                type="country"
                                                class="form-control"
                                                :value="data.info.country"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                        <div class="form-group col-md-6 col-12">
                                            <label>{{ __('City') }}</label>
                                            <input
                                                disabled
                                                readonly
                                                type="text"
                                                class="form-control"
                                                :value="data.info.city"
                                            />
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label>{{ __('Nationality') }}</label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.nationality"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                        <div class="form-group col-md-6 col-12">
                                            <label>{{ __('DateBirth') }}</label>
                                            <input
                                                disabled
                                                readonly
                                                type="text"
                                                class="form-control"
                                                :value="data.info.date_of_birth"
                                            />
                                        </div>
                                    </div>
                                    <div v-if="data.info.fixed_group" class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label>{{ __('FixedGroup') }}</label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.fixed_group"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                    </div>
                                    <div v-if="data.info.dynamic_group" class="row">
                                        <div class="form-group col-md-6 col-12">
                                            <label>{{ __('DynamicGroup') }}</label>
                                            <input
                                                type="text"
                                                class="form-control"
                                                :value="data.info.dynamic_group"
                                                disabled
                                                readonly
                                            />
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </admin-layout>
</template>

<script>
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Breadcrumb';
import QrcodeVue from 'qrcode.vue';

export default {
    components: {
        AdminLayout,
        Breadcrumb,
        QrcodeVue,
    },
    props: {
        data: {
            type: Object,
            required: true,
            default: () => ({}),
        },
        members: {
            type: Object,
            required: true,
            default: () => ({}),
        },
    },
    data() {
        return {
            qrCode: this.data.info.referral_token ? this.data.info.referral_link : null,
            size: 200,
            photo_path: '',
            errors: {},
            breadCrumbLinks: [
                { route: 'dashboard', name: this.__('Dashboard'), active: '' },
                { route: 'users.index', name: this.__('Users'), active: '' },
                { route: ['users.show', this.data.info.id], name: this.__('UserInfo'), active: 'active' },
            ],
        };
    },
    computed: {
        downloadUrl() {
            if (this.qrCode) {
                const link = document.createElement('a');
                link.target = `_blank`;
                link.download = `qr-code.png`;
                const canvas = document.getElementById('qrcode');
                link.href = canvas.toDataURL('image/jpeg');
                document.body.appendChild(link);
                link.click();

                // document.write('<img src="'+img+'"/>');
            }
            return '';
        },
    },

    created() {},
    methods: {
        copy(link) {
            if (window.isSecureContext && navigator.clipboard) {
                navigator.clipboard.writeText(link);
            }
        },
        print() {
            const dataUrl = document.getElementById('qrcode').toDataURL(); // attempt to save base64 string to server using this var
            let windowContent = '<!DOCTYPE html>';
            windowContent += '<html>';
            windowContent += '<head><title>Print Qrcode</title></head>';
            windowContent += '<body>';
            windowContent += `<img src="${dataUrl}">`;
            windowContent += '</body>';
            windowContent += '</html>';
            const width = '800';
            const height = '600';
            const printWin = window.open('', '', `width=${width},height=${height}`);
            printWin.document.open();
            printWin.document.write(windowContent);

            printWin.document.addEventListener(
                'load',
                function () {
                    printWin.focus();
                    printWin.print();
                    printWin.document.close();
                    printWin.close();
                },
                true
            );
        },
        isExpired() {
            if (this.data.info.expire_email_date === null) {
                return true;
            }
            const expireDateObj = new Date(this.data.info.expire_email_date);
            const currentDate = new Date();
            return expireDateObj < currentDate;
        },
    },
};
</script>
