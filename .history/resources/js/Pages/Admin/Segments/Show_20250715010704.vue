<template>
    <admin-layout>
        <section class="section">
            <div class="section-header">
                <h1>{{ __('Segment') }}: {{ segment.title }}</h1>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>{{ __('Users') }}</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Name') }}</th>
                                                <th>{{ __('Email') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="user in users.data" :key="user.id">
                                                <td>{{ user.name }}</td>
                                                <td>{{ user.email }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <pagination :links="users.links" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </admin-layout>
</template>

<script>
import AdminLayout from '@/Layouts/AdminLayout';
import Pagination from '@/Components/Pagination';

export default {
    components: {
        AdminLayout,
        Pagination,
    },
    props: {
        segment: {
            type: Object,
            required: true,
        },
        users: {
            type: Object,
            required: true,
        },
    },
};
</script>
