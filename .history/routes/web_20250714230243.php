<?php

use App\Http\Controllers\AppSettingController;
use App\Http\Controllers\BankOffer\BankOffersController;
use App\Http\Controllers\Booking\AddonsController;
use App\Http\Controllers\Booking\BirthdayTicketsController;
use App\Http\Controllers\Booking\TicketsController;
use App\Http\Controllers\Booking\TicketSettingsController;
use App\Http\Controllers\Brand\BannerController;
use App\Http\Controllers\Brand\BirthdaySettingsController;
use App\Http\Controllers\Brand\BranchesController;
use App\Http\Controllers\Brand\BranchSettingsController;
use App\Http\Controllers\Brand\BrandsController;
use App\Http\Controllers\Brand\VideosController;
use App\Http\Controllers\Corporate\CorporateRequestController;
use App\Http\Controllers\Corporate\GroupTicketRequestController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Events\BirthdayEventController;
use App\Http\Controllers\Feedback\SubjectController;
use App\Http\Controllers\GovernorateController;
use App\Http\Controllers\Group\CustomGroupController;
use App\Http\Controllers\Group\DynamicGroupController;
use App\Http\Controllers\Group\FixedGroupController;
use App\Http\Controllers\Loyalty\LoyaltiesController;
use App\Http\Controllers\Offer\OffersController;
use App\Http\Controllers\Ordering\OrdersController;
use App\Http\Controllers\PaymentMethodsController;
use App\Http\Controllers\Permission\RolesController;
use App\Http\Controllers\Promotions\BankOfferCouponsController;
use App\Http\Controllers\Promotions\BulkCouponsController;
use App\Http\Controllers\Promotions\BundlesController;
use App\Http\Controllers\Promotions\CouponsController;
use App\Http\Controllers\Promotions\GroupCouponsController;
use App\Http\Controllers\Promotions\OfferCouponsController;
use App\Http\Controllers\Promotions\RegisterCouponsController;
use App\Http\Controllers\Promotions\SingleCouponsController;
use App\Http\Controllers\QuestionsController;
use App\Http\Controllers\ReferralQuestionController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\Survey\CategoriesController;
use App\Http\Controllers\Survey\ResultsController;
use App\Http\Controllers\Survey\SurveysController;
use App\Http\Controllers\User\LogsController;
use App\Http\Controllers\User\UserController;
use App\Http\Controllers\Wallet\SalaCreditController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\Admin\SegmentController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::get('language/{language}', function ($language) {
    Session()->put('locale', $language);

    return redirect()->back();
})->name('language');

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
})->name('home');

Route::get('{locale}/generate-invoice/{uuid}', 'Api\OrdersController@generateInvoice');

Route::get('/unauthorized', function () {
    return Inertia::render('Auth/UnAuthorized');
})->name('unauthorized');

Route::get('orders/preview/{order_uuid}', 'GuestController@previewOrder')->name('orders.preview');
Route::get('support', 'GuestController@support')->name('support');
Route::get('privacy-policy', 'GuestController@privacy')->name('privacy');
Route::post('orders/assign/{order_uuid}', 'GuestController@assignOrder')->name('orders.assign');
Route::post('orders/notify/{order_uuid}', 'GuestController@resendEmail')->name('orders.notify');

Route::get('download-app/{referral_token}', 'GuestController@showDownloadApp')->name('downloadApp.show');

// CanAccessDashboard

Route::group(['middleware' => ['auth:sanctum', 'verified', 'activated']], function () {
    // logout
    Route::post('/user-logout', [DashboardController::class, 'logout'])->name('user.logout');
    Route::get('/dashboard', [DashboardController::class, 'dashboard'])->name('dashboard');
    Route::resource('governorate', GovernorateController::class);
    Route::resource('brands', BrandsController::class);
    Route::get('brands-list/{governorateId}', [BrandsController::class, 'list'])->name('brands.list');

    Route::post('brands/filter/update', 'Brand\BrandsController@saveFilteredBrand')->name('brands.saveFilteredBrand');
    Route::get('filter-branches/{id}', 'Brand\BranchesController@filterByBrand')->name('brands.branches');
    Route::get('filter-offers/{id}/{branch_id}', [OffersController::class, 'filterByBrand'])->name('brands.offers');

    Route::get('filter-ticket-branches/{id}', 'Brand\BranchesController@filterByBrandTicket')->name(
        'brands.branches.tickets'
    );
    Route::get('filter-tickets/{id}/{branches}', 'Booking\TicketsController@filterByBrand')->name(
        'brands.tickets.filter'
    );
    Route::get('filter-governorate/{id}', 'Brand\BranchesController@filterGovernorateByBrand')->name(
        'brands.governorate'
    );
    Route::resource('branches', BranchesController::class);
    Route::resource('videos', VideosController::class);
    Route::resource('branches-settings', BranchSettingsController::class)->only(['show', 'update', 'destroy']);
    Route::resource('tickets-settings', TicketSettingsController::class)->only(['show', 'update', 'destroy']);
    Route::get('tickets-list/{brandId}', 'Booking\TicketsController@list')->name('tickets.list');
    Route::get(
        'tickets-governorate-list/{brandId}/{governorateId}',
        'Booking\TicketsController@listByGovernorate'
    )->name('tickets.list.governorate');
    Route::resource('tickets', TicketsController::class);
    Route::resource('addons', AddonsController::class);
    Route::resource('birthday-tickets', BirthdayTicketsController::class);
    Route::resource('categories', CategoriesController::class);
    Route::post('bulk-orders/new', 'Ordering\OrdersController@newBulk')->name('bulk-orders.store');
    Route::get('bulk-orders/new', 'Ordering\OrdersController@newBulk')->name('bulk-orders.newBulk');
    Route::get('bulk-orders/list', 'Ordering\OrdersController@bulkOrders')->name('bulk-orders.bulkOrders');
    Route::get('bulk-orders/show/{id}', 'Ordering\OrdersController@showBulk')->name('bulk-orders.showBulk');
    Route::post('bulk-orders/export/{id}', 'Ordering\OrdersController@exportBulk')->name('bulk-orders.exportBulk');
    Route::post('bulk-orders/notify/{id}', 'Ordering\OrdersController@resendEmailNotify')->name(
        'bulk-orders.resendEmail'
    );

    Route::get('bulk-orders/bulkTickets/{corporateId}', 'Ordering\OrdersController@bulkTickets')->name(
        'bulk-orders.bulkTickets'
    );

    Route::post('bulk-tickets/export/{corporateId}', 'Ordering\OrdersController@bulkTicketExports')->name(
        'bulk-orders.bulkTickets.export'
    );
    Route::get('/orders/reports/weekly', [OrdersController::class, 'weeklyOrders'])->name('orders.weekly');
    Route::resource('reports', ReportsController::class);
    Route::resource('orders', OrdersController::class);
    Route::resource('coupons', CouponsController::class);
    Route::resource('single-coupons', SingleCouponsController::class);
    Route::resource('group-coupons', GroupCouponsController::class);
    Route::resource('register-coupons', RegisterCouponsController::class);

    // RegisterCouponsController
    Route::resource('logs', LogsController::class);
    Route::get('log-modules', [LogsController::class, 'getAllLogModule'])->name('logs.modules');

    Route::delete('delete-logs', 'User\LogsController@deleteAll')->name('logs.delete-all');
    Route::get('filter-coupons/{id}', 'Promotions\CouponsController@filterByBrand')->name('brands.coupons');
    Route::get('filter-bundles/{id}', 'Promotions\BundlesController@filterByBrand')->name('brands.bundles');
    Route::get('products/{brandId}', 'Ordering\OrdersController@getFoodicsProducts')->name('products.list');
    Route::get('product/{brandId}/{productId}', 'Ordering\OrdersController@getFoodicsProductById')->name(
        'product.details'
    );
    Route::resource('bundles', BundlesController::class);
    Route::resource('bulk-coupons', BulkCouponsController::class);
    Route::resource('offer-coupons', OfferCouponsController::class);
    Route::get('user-coupons', 'Promotions\OfferCouponsController@list_user_coupons')->name('offer-coupons.users');

    Route::post('export-coupons/{id}', 'Promotions\BulkCouponsController@export')->name('bulk-coupons.export');

    Route::post('orders/cancel/{id}', 'Ordering\OrdersController@cancel')->name('orders.cancel');
    Route::get('orders/{id}/logs', 'Ordering\OrdersController@getOrderLogsById')->name('orders.logs'); // getOrderLogsById
    Route::post('invoices/pull-status', 'Ordering\InvoicesController@pull')->name('invoices.pull-status');
    Route::post('invoices/send-invoice/{id}', 'Ordering\InvoicesController@send')->name('invoices.send-invoice');
    Route::post('export-orders', 'Ordering\OrdersController@export')->name('orders.export');
    Route::post('export-users', 'User\UserController@export')->name('users.export');
    Route::post('orders/change-status/{id}', 'Ordering\OrdersController@changeStatus')->name('orders.changeStatus');
    Route::post('orders/change-branch', 'Ordering\OrdersController@changeBranch')->name('orders.changeBranch');
    Route::get('import-users', 'User\UserController@import_user')->name('users.import.show');
    Route::post('import-users', 'User\UserController@import')->name('users.import.store');
    Route::post('export-cashiers', 'User\UserController@exportCashiers')->name('cashiers.export');

    Route::get('payment-orders/{id}', 'Ordering\OrdersController@paymentLogs')->name('orders.payment-log');
    Route::get('foodics-orders/{id}', 'Ordering\OrdersController@foodicsLogs')->name('orders.foodics-log');

    Route::get('orders/children/{id}', 'Ordering\OrdersController@children')->name('orders.children');
    Route::post('orders/claim/{id}', 'Ordering\OrdersController@claim')->name('orders.claim');
    Route::post('bulk-claim-orders', 'Ordering\OrdersController@bulkClaim')->name('orders.bulk-claim');

    Route::post('orders/pay/{id}', 'Ordering\OrdersController@pay')->name('orders.pay');
    Route::post('orders/paid/{id}', 'Ordering\OrdersController@paid')->name('orders.paid');
    Route::post('orders/refund/{id}', 'Ordering\OrdersController@refund')->name('orders.refund');
    Route::resource('users', UserController::class);
    Route::get('userWallet/{id}', 'User\UserController@wallet')->name('users.wallet');
    Route::post('userWallet/{userId}/{currencyId}', 'User\UserController@walletTransaction')->name(
        'users.walletTransaction'
    );
    Route::get('filter-users/{search}', 'User\UserController@filterUser')->name('users.filter');
    Route::get('users-cashiers', 'User\UserController@cashiers')->name('users.cashiers');

    Route::post('users/active/{id}', 'User\UserController@active')->name('users.active');
    Route::post('users/deactivate/{id}', 'User\UserController@deactivate')->name('users.deactivate');
    Route::resource('questions', QuestionsController::class); // common question faq
    Route::resource('referral-questions', ReferralQuestionController::class); // common question referral
    Route::resource('subjects', SubjectController::class);
    Route::get('corporate/list', 'Corporate\CorporateRequestController@list')->name('corporate.list');
    Route::resource('corporate-requests', CorporateRequestController::class)->only(['index', 'show']);
    Route::resource('birthday-events', BirthdayEventController::class)->only(['index', 'show']);

    Route::resource('group_tickets', GroupTicketRequestController::class);
    Route::resource('app_settings', AppSettingController::class)->only(['index', 'update']);
    Route::resource('banners', BannerController::class);
    Route::resource('fixed_groups', FixedGroupController::class);
    Route::get('fixed_groups/manage/{id}', 'Group\FixedGroupController@manageUser')->name('fixed_groups.manage');
    Route::put('fixed_groups/manage/{id}', 'Group\FixedGroupController@assignUser')->name('fixed_groups.assign');
    Route::delete('fixed_groups/remove_user/{group_id}/{id}', 'Group\FixedGroupController@removeUser')->name(
        'fixed_groups.removeUser'
    );
    Route::get('fixed_groups/manage/groups/filter-users/{id}/{search}', 'Group\FixedGroupController@filterUser')->name(
        'fixed_groups.filterUser'
    );
    Route::post('fixed_groups/manage/group/export-users', 'Group\FixedGroupController@export')->name('users.export');

    Route::resource('dynamic_groups', DynamicGroupController::class);
    Route::get('dynamic_groups/manage/{id}', 'Group\DynamicGroupController@manageUser')->name('dynamic_groups.manage');
    Route::put('dynamic_groups/manage/{id}', 'Group\DynamicGroupController@assignUser')->name('dynamic_groups.assign');
    Route::delete('dynamic_groups/remove_user/{group_id}/{id}', 'Group\DynamicGroupController@removeUser')->name(
        'dynamic_groups.removeUser'
    );
    Route::get(
        'dynamic_groups/manage/groups/filter-users/{id}/{search}',
        'Group\FixedGroupController@filterUser'
    )->name('dynamic_groups.filterUser');

    Route::post('dynamic_groups/manage/group/export-users', 'Group\FixedGroupController@export')->name('users.export');

    Route::delete('branch-images/{id}', 'Brand\BranchesController@deleteImage')->name('branches.delete.images');

    Route::resource('roles', RolesController::class);
    Route::get('roles/manage/{id}', 'Permission\RolesController@manageUser')->name('roles.manage');
    Route::put('roles/manage/{id}', 'Permission\RolesController@assignUser')->name('roles.assign');

    // survey_categories
    Route::resource('survey-categories', CategoriesController::class);
    Route::resource('survey-results', ResultsController::class);
    Route::resource('surveys', SurveysController::class);
    Route::post('export-surveys', 'Survey\ResultsController@export')->name('surveys.export');
    Route::resource('friends', 'Invitation\FriendsController')->only(['index', 'edit', 'update', 'show', 'destroy']);
    Route::resource('family-relations', 'Invitation\FamilyRelationsController');

    Route::resource('offers', OffersController::class);
    Route::resource('bank-offers', BankOffersController::class);
    Route::resource('bank-offer-coupons', BankOfferCouponsController::class);
    Route::resource('custom-groups', CustomGroupController::class);
    Route::get('custom-groups/manage/{id}', 'Group\CustomGroupController@manageUser')->name('custom-groups.manage');
    Route::post('/custom-groups/deleteUser/{id}', [CustomGroupController::class, 'deleteUser'])->name(
        'custom-groups.deleteUser'
    );
    Route::put('/custom-groups/addUser/{id}', [CustomGroupController::class, 'addUser'])->name('custom-groups.addUser');
    Route::post('/custom-groups/updateUser/{id}', [CustomGroupController::class, 'updateUser'])->name(
        'custom-groups.updateUser'
    );
    Route::resource('loyalties', LoyaltiesController::class)->only(['index', 'store']);
    Route::resource('sala-credit', SalaCreditController::class)->only(['index', 'store']);

    Route::resource('birthday-settings', BirthdaySettingsController::class)->only(['show', 'update', 'destroy']);
    Route::resource('payment-methods', PaymentMethodsController::class);
});

Route::prefix('admin')->middleware(['auth', 'role:super-admin'])->name('admin.')->group(function () {
    Route::resource('segments', SegmentController::class)->only(['index', 'show']);
});
