<?php

namespace Database\Seeders;

use App\Console\Commands\SegmentUsers;
use App\Models\Segment;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SegmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {        
        
        $segments = [
            [
                'title' => 'Loyal Customers',
                'description' => 'Customers who have made more than 5 purchases.',
            ],
            [
                'title' => 'High-Value Customers',
                'description' => 'Customers who have spent more than $1000 in total.',
            ],
            [
                'title' => 'New Customers',
                'description' => 'Customers who signed up in the last 30 days.',
            ],
            [
                'title' => 'No Orders Customers',
                'description' => 'Customers who signed up and did not make any order yet.',
            ],
            [
                'title' => 'Inactive Customers',
                'description' => 'Customers who have not made a purchase in the last 90 days.',
            ],
            [
                'title' => 'Weekend Shoppers',
                'description' => 'Customers who primarily make purchases on weekends.',
            ],
            [
                'title' => 'Weekday Shoppers',
                'description' => 'Customers who primarily make purchases on weekdays.',
            ],
        ];

        foreach ($segments as $segment) {
            DB::table('segments')->insert([
                'title' => $segment['title'],
                'description' => $segment['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
