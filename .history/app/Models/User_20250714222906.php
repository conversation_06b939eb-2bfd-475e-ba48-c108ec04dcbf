<?php

namespace App\Models;

use App\Models\Permission\RoleUser;
use App\Models\Wallet\WalletTransaction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Jetstream\HasTeams;
use <PERSON><PERSON>\Sanctum\Contracts\HasApiTokens as HasApiTokensContract;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Traits\HasRoles;
use App\Models\Ordering\Order;
use App\Models\Segment;

class User extends Authenticatable implements HasApiTokensContract
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use HasRoles;
    use HasTeams;
    use LogsActivity;
    use Notifiable;
    use SoftDeletes;
    use TwoFactorAuthenticatable;

    protected $defaults = [
        'country' => '',
        'nationality' => '',
        'city' => '',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'profile_photo_url',
        'date_of_birth',
        'country',
        'city',
        'nationality',
        'mobile',
        'first_name',
        'last_name',
        'active',
        'lang',
        'foodics_id',
        'profile_photo',
        'gender',
        'referral_count',
        'referral_token',
        'organizational_email',
        'expire_email_date',
        'referral_code',
        'is_test_account',
    ];

    public function profilePhotoUrl()
    {
        $name = trim(
            collect(explode(' ', $this->name))
                ->map(function ($segment) {
                    return mb_substr($segment, 0, 1);
                })
                ->join(' ')
        );

        return 'https://ui-avatars.com/api/?name='.urlencode($name).'&color=7F9CF5&background=EBF4FF&size=200';
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'name',
                'email',
                'date_of_birth',
                'country',
                'city',
                'nationality',
                'mobile',
                'first_name',
                'last_name',
                'active',
                'lang',
                'referral_count',
                'referral_token',
                'is_test_account',
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = ['password', 'remember_token', 'two_factor_recovery_codes', 'two_factor_secret'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'photo_path',
        'date',
        'full_name',
        'status_span',
        'status_text',
        'referral_link',
        'role_names',
        'group_names',
        'fixed_group',
        'dynamic_group',
        'friends_count',
        'received_friends_count',
        'pending_friends_count',
    ];

    public function getDateAttribute()
    {
        return $this->created_at
            ? \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('m/d/Y')
            : '';
    }

    public function providers()
    {
        return $this->hasMany(Provider::class, 'user_id', 'id');
    }

    public function setNameAttribute()
    {
        return $this->first_name.' '.$this->last_name;
    }

    public function getFullNameAttribute()
    {
        return $this->first_name ?? (' '.$this->last_name ?? '');
    }

    public function getNameAttribute($value)
    {
        if (!$value) {
            $value = ($this->first_name ?? '').' '.($this->last_name ?? '');
        }

        return $value ?? 'SALA';
    }

    public function getFirstNameAttribute($value)
    {
        return $value ?? ($this->name ?? '');
    }

    public function getLastNameAttribute($value)
    {
        return $value ?? '';
    }

    public function billingAddress()
    {
        return $this->hasOne(BillingAddress::class, 'user_id', 'id');
    }

    public function devices()
    {
        return $this->hasMany('App\Models\Device');
    }

    public function embedCards()
    {
        return $this->hasOne('App\Models\EmbedCard');
    }

    public function getNoOrdersAttribute()
    {
        return Ordering\Order::where('user_id', $this->id)->count();
    }

    public function getPhotoPathAttribute()
    {
        // return $this->profile_photo_url;
        return $this->profile_photo ? \Storage::url($this->profile_photo) : $this->profilePhotoUrl();
    }

    public function getRoles()
    {
        return $this->hasMany(RoleUser::class, 'model_id')->with('role.list_brands');
    }

    public function getStatusSpanAttribute()
    {
        if ($this->active == 1) {
            return "<span class='badge badge-pill badge-success'>Activated</span>";
        } else {
            return "<span class='badge badge-pill badge-danger'>Deactivate</span>";
        }
    }

    public function getStatusTextAttribute()
    {
        if ($this->active == 1) {
            return 'Activated';
        } else {
            return 'Deactivate';
        }
    }

    public function group()
    {
        return $this->belongsTo(Group\Group::class);
    }

    public function getReferralLinkAttribute()
    {
        if (count($this->roles) > 0 && !$this->referral_token) {
            // generate referral_token
            $this->update(['referral_token' => \Illuminate\Support\Str::uuid()]);
        }

        return $this->referral_token ? route('downloadApp.show', $this->referral_token) : null;
    }

    public function getReferralCountAttribute()
    {
        return ScanQrLog::where('user_id', $this->id)->count();
    }

    public function getRoleNamesAttribute()
    {
        $roles = [];
        if (count($this->roles) > 0) {
            // generate referral_token
            $roles = $this->roles()
                ->pluck('name')
                ->toArray();
        }

        return implode(' - ', $roles);
    }

    public function groups()
    {
        return $this->belongsToMany(Group\Group::class, 'group_users', 'user_id', 'group_id');
    }

    public function getGroupNamesAttribute()
    {
        if (app()->getLocale() == 'en') {
            $groupTitles = $this->groups->pluck('title_en')->implode(' - ');
        } else {
            $groupTitles = $this->groups->pluck('title_ar')->implode(' - ');
        }
        $groupTitles = $groupTitles ?? '';

        return $this->email.' - '.$groupTitles;
    }

    public function getFixedGroupAttribute()
    {
        if (app()->getLocale() == 'en') {
            $groupTitles = $this->groups
                ->where('type', 'fixed')
                ->pluck('title_en')
                ->first();
        } else {
            $groupTitles = $this->groups
                ->where('type', 'fixed')
                ->pluck('title_ar')
                ->first();
        }

        return $groupTitles ?? '';
    }

    public function getDynamicGroupAttribute()
    {
        if (app()->getLocale() == 'en') {
            $groupTitles = $this->groups
                ->where('type', 'dynamic')
                ->pluck('title_en')
                ->first();
        } else {
            $groupTitles = $this->groups
                ->where('type', 'dynamic')
                ->pluck('title_ar')
                ->first();
        }

        return $groupTitles ?? '';
    }

    public function getDynamicFixedGroupsIdsAttribute()
    {
        return $this->groups->pluck('id')->toArray();
    }

    public function getFriendsCountAttribute()
    {
        return Invitation\Friend::Friend($this->id)
            ->whereStatus(1)
            ->count();
    }

    public function getPendingFriendsCountAttribute()
    {
        return Invitation\Friend::where('user_id', $this->id)
            ->whereStatus(0)
            ->count();
    }

    public function getReceivedFriendsCountAttribute()
    {
        return Invitation\Friend::where('user_id', $this->id)
            ->whereStatus(0)
            ->count();
    }

    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class, 'user_id');
    }

    // Define a method to get the current creadit for each user where currency_id = 1 which is wallet sala credit
    public function walletTotal()
    {
        $adds = $this->walletTransactions()
            ->where('currency_id', 1)
            ->where('transaction_type', '1')
            ->sum('transaction_amount');

        $deducts = $this->walletTransactions()
            ->where('currency_id', 1)
            ->where('transaction_type', '0')
            ->sum('transaction_amount');

        return $adds - $deducts;
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function segments()
    {
        return $this->belongsToMany(Segment::class, 'segment_user');
    }

    // Define a method to get the current available points for each user where currency_id = 2 which is loyality points
    public function loyalityTotal()
    {
        $adds = $this->walletTransactions()
            ->where('currency_id', 2)
            ->where('transaction_type', '1')
            ->sum('transaction_amount');

        $deducts = $this->walletTransactions()
            ->where('currency_id', 2)
            ->where('transaction_type', '0')
            ->sum('transaction_amount');

        return (int) ($adds - $deducts);
    }

    public function scopeHasDownload($query)
    {
        return $query->where('referral_count', '>', 0);
    }

    public function getPointsAttribute()
    {
        return $this->loyalityTotal();
    }
}
