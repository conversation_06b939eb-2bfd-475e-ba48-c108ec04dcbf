<?php

namespace App\Services;

use App\Models\User;
use App\Models\Segment;
use Carbon\Carbon;

class SegmentationService
{
    public function segmentUser(User $user)
    {
        $segments = Segment::all();
        $userSegments = [];

        foreach ($segments as $segment) {
            $methodName = 'is' . str_replace(' ', '', $segment->title);
            if (method_exists($this, $methodName) && $this->$methodName($user)) {
                $userSegments[] = $segment->id;
            }
        }

        $user->segments()->sync($userSegments);
    }

    private function isLoyalCustomers(User $user)
    {
        return $user->orders()->count() > 5;
    }

    private function isHighValueCustomers(User $user)
    {
        return $user->orders()->sum('total') > 1000;
    }

    private function isNewCustomers(User $user)
    {
        return $user->created_at->gt(Carbon::now()->subDays(30));
    }

    private function isInactiveCustomers(User $user)
    {
        return $user->orders()->latest()->first()->created_at->lt(Carbon::now()->subDays(90));
    }

    private function isCartAbandoners(User $user)
    {
        // This logic depends on how cart abandonment is tracked in your application.
        // For example, you might have a `carts` table with a `user_id` and an `updated_at` timestamp.
        // If a cart is updated but not converted to an order within a certain time frame,
        // the user could be considered a cart abandoner.
        return false;
    }

    private function isFrequentVisitors(User $user)
    {
        // This logic depends on how you track user visits.
        // You might have a `user_visits` table that logs each visit.
        return false;
    }

    private function isWeekendShoppers(User $user)
    {
        $weekendOrders = $user->orders()->where(function ($query) {
            $query->whereRaw('DAYOFWEEK(created_at) = 1') // Sunday
                  ->orWhereRaw('DAYOFWEEK(created_at) = 7'); // Saturday
        })->count();

        $totalOrders = $user->orders()->count();

        return $totalOrders > 0 && ($weekendOrders / $totalOrders) >= 0.5;
    }

    private function isWeekdayShoppers(User $user)
    {
        $weekdayOrders = $user->orders()->where(function ($query) {
            $query->whereRaw('DAYOFWEEK(created_at) > 1')
                  ->whereRaw('DAYOFWEEK(created_at) < 7');
        })->count();

        $totalOrders = $user->orders()->count();

        return $totalOrders > 0 && ($weekdayOrders / $totalOrders) >= 0.5;
    }
}
