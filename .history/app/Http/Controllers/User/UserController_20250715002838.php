<?php

namespace App\Http\Controllers\User;

use App\Events\LoyaltyPointCreated;
use App\Exports\CashiersExport;
use App\Exports\UsersExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequest;
use App\Imports\UpdateGenderImport;
use App\Jobs\ExportEmailJob;
use App\Models\Group\Group;
use App\Models\Invitation\Friend;
use App\Models\Ordering\Order;
use App\Models\Permission\RoleBrand;
use App\Models\User;
use App\Models\Segment;
use App\Models\Wallet\WalletTransaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth']);
        $this->middleware('checkPermission:users.index')->only(['index']);
        $this->middleware('checkPermission:users.show')->only(['show']);
        $this->middleware('checkPermission:users.edit')->only(['edit']);
        $this->middleware('checkPermission:users.delete')->only(['destroy']);
        $this->middleware('checkPermission:users.active')->only(['active']);
        $this->middleware('checkPermission:users.deactivate')->only(['deactivate']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $users = User::orderBy('id', 'desc');
        if (isset($fromDate) && $fromDate != '') {
            $users = $users->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $users = $users->whereDate('created_at', '<=', $toDate);
        }
        if (isset($search) && $search != '') {
            $users = $users->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%')
                    ->orWhere('first_name', 'like', '%'.$search.'%')
                    ->orWhere('last_name', 'like', '%'.$search.'%')
                    ->orWhere('mobile', 'like', '%'.$search.'%')
                    ->orWhere('nationality', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhere('date_of_birth', 'like', '%'.$search.'%')
                    ->orWhere('country', 'like', '%'.$search.'%')
                    ->orWhere('city', 'like', '%'.$search.'%');
            });
        }

        if (isset($active) && $active != '') {
            $users = $users->where('active', $active);
        }

        $users = $users->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Users/Index', [
            'data' => $users,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'search' => $search,
            'active' => $active,
        ]);
    }

    public function cashiers(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $role_ids = [];
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $role_ids = RoleBrand::where('brand_id', Cookie::get('brandId'))
                ->pluck('role_id')
                ->toArray();
        }

        $users = User::whereHas('roles')->orderBy('created_at', 'desc');
        if (count($role_ids) > 0) {
            $users = $users->whereHas('roles', function ($q) use ($role_ids) {
                $q->whereIn('id', $role_ids);
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $users = $users->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $users = $users->whereDate('created_at', '<=', $toDate);
        }
        if (isset($search) && $search != '') {
            $users = $users->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%')
                    ->orWhere('first_name', 'like', '%'.$search.'%')
                    ->orWhere('last_name', 'like', '%'.$search.'%')
                    ->orWhere('mobile', 'like', '%'.$search.'%')
                    ->orWhere('nationality', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhere('date_of_birth', 'like', '%'.$search.'%')
                    ->orWhere('country', 'like', '%'.$search.'%')
                    ->orWhere('city', 'like', '%'.$search.'%');
            });
        }

        if (isset($active) && $active != '') {
            $users = $users->where('active', $active);
        }

        $users = $users->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Users/Cashiers', [
            'data' => $users,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'search' => $search,
            'active' => $active,
        ]);
    }

    public function import(Request $request)
    {
        $file = '';
        if ($request->key) {
            $path = 'users/imports/';
            $name = $request->uuid.'.xlsx';
            $file = $path.$name;
            Storage::disk('s3')->copy($request->key, $file);
        } else {
            $file = 'users/imports/f3b796bd-210b-4b1e-b028-5df3b06dcc6b.xlsx';
        }
        if ($file != '') {
            // queueImport
            // Excel::queueImport(new UpdateGenderImport(), $file, 's3');
            // Excel::import(new UpdateGenderImport(), $file, 's3');
            /*dispatch(function () use ($file) {
                Excel::import(new UpdateGenderImport(), $file, 's3');
            })->onQueue('import');
            */
            Excel::import(new UpdateGenderImport(), $file, 's3', \Maatwebsite\Excel\Excel::XLSX);
        }

        $request->session()->flash('success', 'the imported data is processing');

        return redirect()->back();
    }

    public function export(Request $request)
    {
        $filename = 'exports/'.time().'-users.csv';
        (new UsersExport($request->all()))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/users.csv')])
            ->onQueue('export-users');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    // exportCashiers
    public function exportCashiers(Request $request)
    {
        $role_ids = [];
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $role_ids = RoleBrand::where('brand_id', Cookie::get('brandId'))
                ->pluck('role_id')
                ->toArray();
        }
        $filename = 'exports/'.time().'-cashiers.csv';

        $data = $request->all();
        $data['role_ids'] = $role_ids;

        (new CashiersExport($data))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/cashiers.csv')])
            ->onQueue('export-cashiers');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    public function create()
    {
        $data = ['title' => 'Create new user'];

        return Inertia::render('Users/Create', ['data' => $data]);
    }

    // import_user
    public function import_user()
    {
        $data = ['title' => 'import user to update gender'];

        return Inertia::render('Users/Import', ['data' => $data]);
    }

    public function store(UserRequest $request)
    {
        $data = $request->all();
        $data['password'] = $data['password'] != '' ? Hash::make($request->password) : null;

        $data['referral_code'] = uniqueStringCode('App\Models\User', 'referral_code', 3);

        $user = User::create($data);

        $group = Group::whereTranslation('title', 'Silver', 'en')->first();
        if ($group) {
            $user->groups()->save($group);
        }

        if ($user->organizational_email) {
            $user->update(['expire_email_date' => Carbon::now()->addYear()]);

            $search = trim(explode('@', $user->organizational_email)[1]);
            $fixed_group = Group::where('domain', 'like', '%'.$search.'%')->first();
            if (!empty($fixed_group)) {
                $user->groups()->save($fixed_group);
            }
        }

        $pageTitle = __('theUser');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('users.index', ['active' => 1]);
    }

    public function show(User $user)
    {
        $user->load('segments');
          // check if user have roles
        if (count($user->roles) > 0 && !$user->referral_token) {
            // generate referral_token
            $user->update(['referral_token' => Str::uuid()]);
        }

        $data = [
            'info' => $user,
            'title' => $user->name.' Info',
            'confirmed' => Order::where('status', 1)
                ->where('user_id', $user->id)
                ->count(),
            'cancelled' => Order::where('status', 3)
                ->where('user_id', $user->id)
                ->count(),
            'upcoming' => Order::where('status', 0)
                ->where('user_id', $user->id)
                ->count(),
            'roles' => $user->roles,
        ];
        $group = '';
        if ($user->group_id) {
            $group = Group::find($user->group_id);
            $data['group'] = $group;
        }

        return Inertia::render('Users/Details', [
            'data' => $data,
            'members' => Friend::Friend($user->id)
                ->with('user', 'sender')
                ->paginate(10),
        ]);
    }

    public function edit($id)
    {
        $currentUser = User::find($id);
        $data = ['formInfo' => $currentUser, 'title' => 'Update User Info'];

        return Inertia::render('Users/Edit', ['data' => $data]);
    }

    public function update(UserRequest $request, User $user)
    {
        $data = $request->all();
        $data['password'] = $data['password'] != '' ? Hash::make($request->password) : $user->password;

        $user->update($data);

        if (
            $user->organizational_email
            && (strtotime($user->expire_email_date) <= strtotime(date('Y-m-d H:i:s')) || !$user->expire_email_date)
        ) {
            $user->update(['expire_email_date' => Carbon::now()->addYear()]);
        }

        $pageTitle = __('theUser');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('users.index', ['active' => 1]);
    }

    public function destroy(Request $request, $id)
    {
        $user = User::find($id);
        $user->delete();

        $pageTitle = __('theUser');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('users.index', ['active' => 1]);
    }

    public function active(Request $request, $id)
    {
        $user = User::find($id);
        $user->update(['active' => 1]);
        $user->roles()->detach();

        $pageTitle = __('theUser');
        $request->session()->flash('success', __('ReActivatedSuccess', ['title' => $pageTitle]));

        return redirect()->route('users.index', ['active' => 1]);
    }

    public function deactivate(Request $request, $id)
    {
        $user = User::find($id);
        try {
            $user->update(['active' => 0]);
            // remove from role
            $user->syncRoles([]);
        } catch (\Illuminate\Database\QueryException $e) {
            exit;
        }

        $pageTitle = __('theUser');
        $request->session()->flash('success', __('DeactivatedSuccess', ['title' => $pageTitle]));

        return redirect()->route('users.index', ['active' => 0]);
    }

    public function list(Request $request, $q)
    {
        $users = User::select('id', 'first_name', 'last_name')->orderBy('id', 'desc');
        $users = $users->where(function ($query) use ($q) {
            $query
                ->where('id', 'like', '%'.$q.'%')
                ->orWhere('first_name', 'like', '%'.$q.'%')
                ->orWhere('last_name', 'like', '%'.$q.'%');
        });

        return response()
            ->json(['users' => $users->get()])
            ->header('Content-Type', 'application/json');
    }

    public function filterUser($search)
    {
        // $searchWords = explode(',', $search);
        $users = User::select('id', 'first_name', 'last_name', 'name', 'mobile', 'email', 'active', 'nationality', 'date_of_birth', 'country', 'city')
            ->orderBy('first_name', 'ASC')->where('active', 1);
        if (isset($search) && $search != '') {
            $users = $users->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%')
                    ->orWhere('first_name', 'like', '%'.$search.'%')
                    ->orWhere('last_name', 'like', '%'.$search.'%')
                    ->orWhere('mobile', 'like', '%'.$search.'%')
                    ->orWhere('nationality', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhere('date_of_birth', 'like', '%'.$search.'%')
                    ->orWhere('country', 'like', '%'.$search.'%')
                    ->orWhere('city', 'like', '%'.$search.'%');
            });
        }

        return $users->get();
    }

    public function wallet($id)
    {
        // check if user already exists
        if ($user = User::find($id)) {
            // get the wallet records
            $walletRecords = WalletTransaction::where('user_id', $id)
                ->SalaCredit()
                ->orderBy('id', 'desc')
                ->paginate(20);
            // get the wallet total as sum(adds)-sum(deducts)
            $walletTotal = $user->walletTotal();
            // get the loyality records
            $loyalityRecords = WalletTransaction::where('user_id', $id)
                ->Loyalty()
                ->orderBy('id', 'desc')
                ->paginate(20);

            // get the loyality total as sum(adds)-sum(deducts)
            $loyalityTotal = $user->loyalityTotal();

            $data = [
                'info' => $user,
                'title' => $user->name.' Info',
                'walletRecords' => $walletRecords,
                'walletTotal' => $walletTotal,
                'loyalityRecords' => $loyalityRecords,
                'loyalityTotal' => $loyalityTotal,
            ];

            return Inertia::render('Users/wallet', [
                'data' => $data,
            ]);
        }
    }

    public function walletTransaction($userId, $currencyId, Request $request)
    {
        // set wallet transaction type var with default value of add
        $walletTrasactionType = '1';
        if ($currencyId == '1') {
            // check if no value or value = deduct the override its value
            if ($request->wallet_transaction_type === null || $request->wallet_transaction_type == 'deduct') {
                $walletTrasactionType = '0';
            }
            $arr['transaction_type'] = $walletTrasactionType;
            $arr['transaction_amount'] = $request->wallet_transaction_amount;
            $arr['transaction_reason'] = $request->wallet_transaction_reason;
            $arr['user_id'] = $userId;
            $arr['admin_id'] = Auth::user()->id;
            $arr['currency_id'] = $currencyId;
        } elseif ($currencyId == '2') {
            // check if no value or value = deduct the override its value
            if ($request->loyality_transaction_type === null || $request->loyality_transaction_type == 'deduct') {
                $walletTrasactionType = '0';
            }
            $arr['transaction_type'] = $walletTrasactionType;
            $arr['transaction_amount'] = $request->loyality_transaction_amount;
            $arr['transaction_reason'] = $request->loyality_transaction_reason;
            $arr['user_id'] = $userId;
            $arr['admin_id'] = Auth::user()->id;
            $arr['currency_id'] = $currencyId;
        }

        $wallet = WalletTransaction::create($arr);
        if ($currencyId == '2') {
            event(new LoyaltyPointCreated($wallet, [], 'notify'));
        }

        return redirect()->route('users.wallet', [$userId]);
    }
}
