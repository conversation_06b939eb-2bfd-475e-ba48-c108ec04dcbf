<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\SegmentationService;
use Illuminate\Console\Command;

class SegmentUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'segment:users {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Segment users based on their activity';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(SegmentationService $segmentationService)
    {
        $userId = $this->argument('user_id');

        if ($userId) {
            $user = User::find($userId);
            if ($user) {
                $segmentationService->segmentUser($user);
                $this->info("User with ID {$userId} has been segmented.");
            } else {
                $this->error("User with ID {$userId} not found.");
            }
        } else {
            $users = User::all();
            foreach ($users as $user) {
                $segmentationService->segmentUser($user);
            }
            $this->info('All users have been segmented.');
        }

        return 0;
    }
}
