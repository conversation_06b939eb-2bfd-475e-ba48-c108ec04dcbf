<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\SegmentationService;
use Illuminate\Console\Command;

class SegmentUsersChunked extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:segment-users-chunked';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Segment all users in chunks';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(SegmentationService $segmentationService)
    {
        $this->info('Starting user segmentation in chunks...');

        $users = User::skip(0)->take(50)->get();

            foreach ($users as $user) {
                $segmentationService->segmentUser($user);
                $this->info("Segmented user {$user->id}");
            }
        

        $this->info('User segmentation completed.');

        return 0;
    }
}
