<?php

namespace App\Http\Controllers\Survey;

use Inertia\Inertia;
use App\Models\Brand\Brand;
use App\Models\Brand\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\SurveyRequest;
use App\Models\Survey\Survey\Survey;
use App\Models\Permission\RoleBranch;
use Illuminate\Support\Facades\Cookie;
use App\Models\Booking\Category\Category;
use App\Models\Survey\Survey\SurveyBranch;
use App\Models\Survey\Survey\SurveyTranslation;

class SurveysController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:surveys.index')->only(['index']);
        $this->middleware('checkPermission:surveys.show')->only(['show']);
        $this->middleware('checkPermission:surveys.edit')->only(['edit']);
        $this->middleware('checkPermission:surveys.delete')->only(['destroy']);
        $this->middleware('checkPermission:surveys.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        if (Cookie::get('role_categories')) {
            $brand_ids = unserialize(Cookie::get('role_categories'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $category = $request->category;

        $surveys = Survey::Recent()->with('category', 'branches');

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $surveys = $surveys->where('brand_id', Cookie::get('brandId'));
        }

        if (count($brand_ids) > 0) {
            $surveys = $surveys->whereIn('brand_id', $brand_ids);
        }

        if (isset($search) && $search != '') {
            $surveys = $surveys->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $surveys = $surveys->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $surveys = $surveys->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $surveys = $surveys->where('active', $active);
        }
        if (isset($category) && $category != '') {
            $surveys = $surveys->where('category_id', $category);
        }

        $surveys = $surveys->paginate(15);

        return Inertia::render('Survey/Survey/Index', [
            'data' => $surveys->withQueryString(),
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'category' => $category,
        ]);
    }

    public function create()
    {
        $categories = Category::Recent()->get();
        $brands = Brand::where('id', Cookie::get('brandId'))
            ->Recent()
            ->get();
        $branches = Branch::where('brand_id', Cookie::get('brandId'))
            ->Recent()
            ->get();
        return Inertia::render('Survey/Survey/Create', [
            'categories' => $categories,
            'brands' => $brands,
            'branches' => $branches,
        ]);
    }

    public function store(SurveyRequest $request)
    {
        $data = $request->all();
        $validateTitleEn = SurveyTranslation::where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('survey', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();
        $validateTitleAr = SurveyTranslation::where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('survey', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();

        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => ['The english title has already been taken.']]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => ['The arabic title has already been taken.']]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $survey = Survey::create($data);
        SurveyBranch::where('survey_id', $survey->id)->delete();
        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                SurveyBranch::create([
                    'branch_id' => $row,
                    'survey_id' => $survey->id,
                ]);
            }
        }

        $request->session()->flash('success', 'Question created successfully!');

        return redirect()->route('surveys.index');
    }

    public function edit(Survey $survey)
    {
        $data = [
            'survey' => $survey,
            'title' => $survey->title . ' Edit',
        ];
        $brands = Brand::where('id', $survey->brand_id)
            ->Recent()
            ->get();
        $branches = Branch::where('brand_id', $survey->brand_id)
            ->Recent()
            ->get();

        return Inertia::render('Survey/Survey/Edit', [
            'data' => $data,
            'categories' => Category::Recent()->get(),
            'brands' => $brands,
            'branches' => $branches,
        ]);
    }

    public function show($id)
    {
        $survey = Survey::where('id', $id)
            ->with('category', 'brand', 'branches')
            ->first();
        $data = [
            'item' => $survey,
            'title' => $survey->title . ' Info',
        ];

        return Inertia::render('Survey/Survey/Show', [
            'data' => $data,
        ]);
    }

    public function update(SurveyRequest $request, Survey $survey)
    {
        $data = $request->all();

        $validateTitleEn = SurveyTranslation::where('survey_id', '!=', $survey->id)
            ->where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('survey', function ($q) use ($survey) {
                $q->where('brand_id', $survey->brand_id);
            })
            ->count();
        $validateTitleAr = SurveyTranslation::where('survey_id', '!=', $survey->id)
            ->where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('survey', function ($q) use ($survey) {
                $q->where('brand_id', $survey->brand_id);
            })
            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => ['The english title has already been taken.']]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => ['The arabic title has already been taken.']]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        try {
            DB::beginTransaction();
            $survey->update($data);
            $branches = [];
            if ($data['branch_id'] && count($data['branch_id']) > 0) {
                foreach ($data['branch_id'] as $row) {
                    array_push($branches, [
                        'branch_id' => $row,
                        'survey_id' => $survey->id,
                    ]);
                }
                SurveyBranch::where('survey_id', $survey->id)->delete();
                SurveyBranch::insert($branches);
            } else {
                SurveyBranch::where('survey_id', $survey->id)->delete();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        $request->session()->flash('success', 'Question updated successfully!');

        return redirect()->route('surveys.index');
    }

    public function destroy(Request $request, $id)
    {
        Survey::find($id)->delete();
        $request->session()->flash('success', 'Question deleted successfully!');

        return redirect()->route('surveys.index');
    }
}