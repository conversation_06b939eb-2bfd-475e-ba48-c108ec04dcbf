<?php

namespace App\Http\Controllers\Survey;

use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryRequest;
use Illuminate\Support\Facades\Storage;
use App\Models\Booking\Category\Category;
use App\Models\Booking\Category\CategoryTranslation;

class CategoriesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:survey-categories.index')->only(['index']);
        $this->middleware('checkPermission:survey-categories.show')->only(['show']);
        $this->middleware('checkPermission:survey-categories.edit')->only(['edit']);
        $this->middleware('checkPermission:survey-categories.delete')->only(['destroy']);
        $this->middleware('checkPermission:survey-categories.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $categories = Category::Survey()->Recent();
        if (isset($search) && $search != '') {
            $categories = $categories->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhere('code', 'like', '%' . $search . '%');
                $q->orWhere('discount', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $categories = $categories->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $categories = $categories->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $categories = $categories->where('active', $active);
        }

        $categories = $categories->paginate(15)->withQueryString();

        return Inertia::render('Survey/Category/Index', [
            'data' => $categories,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
        ]);
    }

    public function create()
    {
        return Inertia::render('Survey/Category/Create');
    }

    public function store(CategoryRequest $request)
    {
        $data = $request->all();

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['type'] = 1;
        if ($request->key) {
            $path = 'categories/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }

        Category::create($data);

        $request->session()->flash('success', 'Category created successfully!');

        return redirect()->route('survey-categories.index');
    }

    public function edit($id)
    {
        $category = Category::find($id);

        $data = ['category' => $category, 'title' => $category->title . ' Edit'];

        return Inertia::render('Survey/Category/Edit', [
            'data' => $data,
        ]);
    }

    public function show($id)
    {
        $category = Category::find($id);

        $data = ['item' => $category, 'title' => $category->title . ' Info'];

        return Inertia::render('Survey/Category/Show', [
            'data' => $data,
        ]);
    }

    public function update(CategoryRequest $request, $id)
    {
        $category = Category::where('id', $id)->first();
        $data = $request->all();
        $validateTitleEn = CategoryTranslation::where('category_id', '!=', $category->id)
            ->where('title', $request->title_en)
            ->where('locale', 'en')
            ->count();
        $validateTitleAr = CategoryTranslation::where('category_id', '!=', $category->id)
            ->where('title', $request->title_ar)
            ->where('locale', 'ar')

            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => ['The english title has already been taken.']]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => ['The arabic title has already been taken.']]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        if ($request->key) {
            $path = 'categories/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $category->update($data);
        $request->session()->flash('success', 'category updated successfully!');

        return redirect()->route('survey-categories.index');
    }

    public function destroy(Request $request, $id)
    {
        Category::find($id)->delete();
        $request->session()->flash('success', 'category deleted successfully!');

        return redirect()->route('survey-categories.index');
    }
}