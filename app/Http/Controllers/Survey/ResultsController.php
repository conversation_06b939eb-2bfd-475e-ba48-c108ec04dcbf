<?php

namespace App\Http\Controllers\Survey;

use App\Exports\SurveyExport;
use App\Http\Controllers\Controller;
use App\Jobs\ExportEmailJob;
use App\Models\Booking\Category\Category;
use App\Models\Brand\Brand;
use App\Models\Permission\RoleBranch;
use App\Models\Survey\Survey\Survey;
use App\Models\Survey\UserSurvey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class ResultsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:surveys.index')->only(['index']);
        $this->middleware('checkPermission:surveys.show')->only(['show']);
        $this->middleware('checkPermission:surveys.edit')->only(['edit']);
        $this->middleware('checkPermission:surveys.delete')->only(['destroy']);
        $this->middleware('checkPermission:surveys.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $surveys = [];

        return Inertia::render('Survey/Results/Index', [
            'data' => $surveys,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function export(Request $request)
    {
        $filename = 'exports/' . time() . '-surveys.csv';
        $brand = Cookie::get('brandId') !== null && Cookie::get('brandId') !== '' ? Cookie::get('brandId') : '';

        (new SurveyExport($request->all(), $brand))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/surveys.csv')])
            ->onQueue('export-surveys');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    public function show($id)
    {
        $survey = UserSurvey::where('id', $id)
            ->with('user', 'order', 'answers')
            ->first();
        $data = [
            'item' => $survey,
            'title' => $survey->title . ' Info',
        ];

        return Inertia::render('Survey/Results/Show', [
            'data' => $data,
        ]);
    }

    public function destroy(Request $request, $id)
    {
        Survey::find($id)->delete();
        $request->session()->flash('success', 'Question deleted successfully!');

        return redirect()->route('surveys.index');
    }
}