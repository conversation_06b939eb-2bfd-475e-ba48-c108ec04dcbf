<?php

namespace App\Http\Controllers\Booking;

use App\Helpers\Constant;
use App\Helpers\DateRange;
use App\Http\Controllers\Controller;
use App\Http\Requests\Booking\TicketSettingRequest;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Booking\Ticket\TicketBranch;
use App\Models\Booking\Ticket\TicketCalendar;
use App\Models\Brand\WorkHour;
use App\Models\Brand\WorkingDay;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class TicketSettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        $this->middleware('checkPermission:tickets.settings');
    }

    public function date_range($first, $last, $step = '+1 day', $output_format = 'Y-m-d')
    {
        $dates = [];
        $current = strtotime($first);
        $last = strtotime($last);

        while ($current <= $last) {
            $dates[] = date($output_format, $current);
            $current = strtotime($step, $current);
        }

        return $dates;
    }

    public function show($id)
    {
        $ticket = Ticket::with('brand', 'branches')
            ->where('id', $id)
            ->first();
        $ids = TicketBranch::where('ticket_id', $id)
            ->pluck('branch_id')
            ->toArray();

        $disabledDates = WorkHour::whereIn('branch_id', $ids)
            ->OffDates()
            ->select('date', 'id')
            ->get()
            ->pluck('formate_date')
            ->toArray();
        $brandWorkingDay = WorkingDay::where('brand_id', $ticket->brand_id)
            ->pluck('day_id')
            ->toArray();
        $daysId = [1, 2, 3, 4, 5, 6, 7];

        $disableWorkingDay = array_diff($daysId, $brandWorkingDay);
        $list = [];

        if (count($disableWorkingDay) > 0) {
            $day_arr = [];
            foreach ($disableWorkingDay as $key => $day_id) {
                $index = array_search($day_id, array_column(Constant::Days, 'id'));
                $title = Constant::Days[$index]['title_en'];
                array_push($day_arr, $title);
            }
            $yearDates = DateRange::getDateRange(date('Y-m-d'), date('Y-m-d', strtotime(date('Y-m-d').' +1 year')));

            foreach ($yearDates as $time) {
                $date = date('Y-m-d', strtotime($time));
                if (in_array(date('l', strtotime($date)), $day_arr)) {
                    $list[] = date('Y-m-d', strtotime($time));
                    array_push($disabledDates, date('Y-n-j', strtotime($time)));
                }
            }
        }
        $oldDates = TicketCalendar::where('date_type', 0)
            ->where('date', '>=', date('Y-m-d'))
            ->where('ticket_id', $id)
            ->get();
        $oldSelectedDates = TicketCalendar::where('date_type', 0)
            ->where('date', '>=', date('Y-m-d'))
            ->where('ticket_id', $id)
            ->pluck('date')
            ->toArray();

        $oldSelectedRangeDates = TicketCalendar::where('date_type', 1)
            ->where('start', '<=', date('Y-m-d'))
            ->where('end', '>=', date('Y-m-d'))
            ->where('ticket_id', $id)
            ->get();

        $oldSelectedRanges = [];

        foreach ($oldSelectedRangeDates as $row) {
            $period = CarbonPeriod::create(
                date('Y-m-d', strtotime($row['start'])),
                date('Y-m-d', strtotime($row['end']))
            );
            // Iterate over the period
            foreach ($period as $item) {
                array_push($oldSelectedRanges, $item->format('Y-m-d'));
            }
        }

        $oldRangeDates = TicketCalendar::where('date_type', 1)
            ->where('start', '<=', date('Y-m-d'))
            ->where('end', '>=', date('Y-m-d'))
            ->where('ticket_id', $id)
            ->get();

        $selectDateRange = TicketCalendar::where('date_type', 1)
            ->where('start', '>=', date('Y-m-d'))
            ->where('end', '>=', date('Y-m-d'))

            ->where('ticket_id', $id)
            ->select('start', 'end')
            ->get();
        $selectDatesRange = [];
        foreach ($selectDateRange as $item) {
            array_push($selectDatesRange, [
                'start' => date('Y-m-d', strtotime($item->start.' - 1 days')),
                'end' => date('Y-m-d', strtotime($item->end.' + 1 days')),
            ]);
        }
        $disabledDates = array_merge($disabledDates, $oldSelectedDates);
        $disabledDates = array_merge($disabledDates, $oldSelectedRanges);

        $data = [
            'ticket' => $ticket,
            'oldDates' => $oldDates,
            'selectDateRange' => $selectDatesRange,
            'oldRangeDates' => $oldRangeDates,
            'disabledDates' => $disabledDates,
            'begin' => date('Y-m-d'),
            'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
        ];

        return Inertia::render('Booking/Tickets/Settings', [
            'data' => $data,
        ]);
    }

    public function update(TicketSettingRequest $request, $id)
    {
        $ticket = Ticket::find($id);
        $data = $request->all();

        //check if date have orders already exist out dates
        DB::beginTransaction();
        if ($data['dates'] && $data['dates']) {
            $dateObjects = [];
            foreach ($data['dates'] as $row) {
                if ($data['date_type'] === '0') {
                    $checkExist = TicketCalendar::where('ticket_id', $id)
                        ->whereDate('date', $row)
                        ->first();

                    if (! $checkExist) {
                        array_push($dateObjects, ['date' => $row, 'ticket_id' => $ticket->id, 'date_type' => 0]);
                    }
                } elseif ($data['date_type'] === '1') {
                    $checkExist = TicketCalendar::where('ticket_id', $id)
                        ->whereDate('start', $row['start'])
                        ->whereDate('end', $row['end'])
                        ->first();

                    if (! $checkExist) {
                        array_push($dateObjects, [
                            'start' => $row['start'],
                            'end' => $row['end'],
                            'ticket_id' => $ticket->id,
                            'date_type' => 1,
                        ]);
                    }
                }
            }
            if (count($dateObjects) > 0) {
                TicketCalendar::insert($dateObjects);
            }
        }

        DB::commit();
        //create work our

        $pageTitle = __('theTicketSettings');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('tickets-settings.show', $id);
    }

    public function destroy(Request $request, $id)
    {
        $workDate = TicketCalendar::where('id', $id)->first();
        if ($workDate) {
            $ticket = $workDate->ticket_id;
            $workDate->delete();
            $request->session()->flash('success', 'Ticket work date deleted successfully!');
        }

        return redirect()->route('tickets-settings.show', $ticket);
    }
}
