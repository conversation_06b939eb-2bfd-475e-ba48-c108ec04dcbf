<?php

namespace App\Http\Controllers\Booking;

use App\Http\Controllers\Controller;
use App\Http\Requests\Booking\TicketRequest;
use App\Models\ActivityLog;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Booking\Ticket\TicketBranch;
use App\Models\Booking\Ticket\TicketExtraInfo;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Permission\RoleBranch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class BirthdayTicketsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:birthday-tickets.index')->only(['index']);
        $this->middleware('checkPermission:birthday-tickets.show')->only(['show']);
        $this->middleware('checkPermission:birthday-tickets.edit')->only(['edit']);
        $this->middleware('checkPermission:birthday-tickets.delete')->only(['destroy']);
        $this->middleware('checkPermission:birthday-tickets.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // ini_set('memory_limit', '-1');
        $brand_ids = [];
        $branch_ids = [];
        $user = auth()->user();
        if (Cookie::get('role_brands') && !$user->hasRole('Super Admin')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $type = $request->type;

        $tickets = Ticket::with(['brand', 'bundles', 'branches'])
            ->Ordered()
            ->Recent()
            ->Birthday();

        if (isset($search) && $search != '') {
            $tickets = $tickets->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($type) && $type != '') {
            $tickets = $tickets->where('ticket_type', $type);
        }
        if (isset($fromDate) && $fromDate != '') {
            $tickets = $tickets->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $tickets = $tickets->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $tickets = $tickets->where('active', $active);
        }
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $tickets = $tickets->where('brand_id', Cookie::get('brandId'));
        }
        if (count($brand_ids) > 0) {
            $tickets = $tickets->whereIn('brand_id', $brand_ids);
        }

        $tickets = $tickets->paginate(15)->appends(request()->query());
        // ->withQueryString();

        $brands = Brand::Recent()->get();

        return Inertia::render('Booking/BirthdayTickets/Index', [
            'data' => $tickets,
            'brands' => $brands,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $branches = Branch::where('brand_id', Cookie::get('brandId'))
            ->Recent()
            ->get();

        return Inertia::render('Booking/BirthdayTickets/Create', ['branches' => $branches]);
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['sub_title'] = $request->sub_title_en;
        $data['ar']['sub_title'] = $request->sub_title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['enable_extra_info'] = $request->enableExtraInfo;
        $data['ticket_type'] = 3; // that mean it is bearthday coupon
        if ($request->key) {
            $path = 'brands/'.$data['brand_id'].'/tickets/';
            $name = $request->content_type
                ? $request->uuid.'.'.explode('/', $request->content_type)[1]
                : $request->uuid.'.png';
            $data['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $ticket = Ticket::create($data);
        if (!empty($data['extraInfoItems'])) {
            $this->saveExtraInfo($ticket, $data['extraInfoItems']);
        }
        $branches = [];
        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                TicketBranch::create([
                    'branch_id' => $row,
                    'ticket_id' => $ticket->id,
                    'brand_id' => $ticket->brand_id,
                ]);
            }
        }
        $pageTitle = __('theTicket');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('birthday-tickets.index');
    }

    public function edit(Ticket $birthdayTicket)
    {
        $branches = Branch::where('brand_id', $birthdayTicket->brand_id)
            ->Recent()
            ->get();
        $extraInfoItems = TicketExtraInfo::where('ticket_id', $birthdayTicket->id)
            ->Recent()
            ->get();

        $data = ['ticket' => $birthdayTicket, 'title' => $birthdayTicket->title.' Edit'];
        $branchIds = $birthdayTicket->branch_ids;

        return Inertia::render('Booking/BirthdayTickets/Edit', [
            'data' => $data,
            'branchIds' => $branchIds,
            'branches' => $branches,
            'extraInfoItems' => $extraInfoItems,
        ]);
    }

    public function show($id)
    {
        $ticket = Ticket::Birthday()
            ->where('id', $id)
            ->with('brand', 'branches', 'extraInfo')
            ->first();
        if (!$ticket) {
            return redirect()->back();
        }
        if (!empty($ticket)) {
            $activities = ActivityLog::where('subject_id', $ticket->id)
                ->where('subject_type', 'App\Models\Booking\Ticket\Ticket')
                ->orderBy('created_at', 'DESC')
                ->select('*')
                ->get()
                ->groupBy(function ($date) {
                    return Carbon::parse($date->created_at)->format('F Y');
                });
        }

        $data = ['item' => $ticket, 'title' => $ticket->title.' Info', 'activities' => $activities];

        return Inertia::render('Booking/BirthdayTickets/Show', [
            'data' => $data,
        ]);
    }

    public function update(TicketRequest $request, $ticket)
    {
        $data = $request->all();
        $ticket = Ticket::find($ticket);
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['en']['sub_title'] = $request->sub_title_en;
        $data['ar']['sub_title'] = $request->sub_title_ar;
        $data['enable_extra_info'] = $request->enableExtraInfo;
        if ($request->key) {
            $path = 'brands/'.$data['brand_id'].'/tickets/';
            $name = $request->content_type
                ? $request->uuid.'.'.explode('/', $request->content_type)[1]
                : $request->uuid.'.png';
            $data['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $ticket->update($data);
        if (!empty($data['extraInfoItems'])) {
            $this->saveExtraInfo($ticket, $data['extraInfoItems']);
        }

        TicketBranch::where('ticket_id', $ticket->id)
        ->delete();
        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                TicketBranch::updateOrCreate(
                    [
                        'branch_id' => $row,
                        'ticket_id' => $ticket->id,
                        'brand_id' => $ticket->brand_id,
                    ],
                    [
                        'branch_id' => $row,
                        'ticket_id' => $ticket->id,
                        'brand_id' => $ticket->brand_id,
                    ]
                );
            }
        }

        $pageTitle = __('theTicket');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('birthday-tickets.index');
    }

    public function destroy(Request $request, $id)
    {
        Ticket::find($id)->delete();

        $pageTitle = __('theTicket');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('birthday-tickets.index');
    }

    public function saveExtraInfo($ticket, $extraInfoItems)
    {
        $ticketExtraInfo = TicketExtraInfo::where('ticket_id', $ticket->id)->get();
        if (!is_null($ticketExtraInfo)) {
            TicketExtraInfo::where('ticket_id', $ticket->id)->delete();
        }
        $ticketExtraInfo = [];
        foreach ($extraInfoItems as $item) {
            $data['ar']['title'] = $item['title_ar'];
            $data['en']['title'] = $item['title_en'];
            $data['required'] = $item['required'];
            $data['ticket_id'] = $ticket->id;
            TicketExtraInfo::create($data);
        }
    }
}
