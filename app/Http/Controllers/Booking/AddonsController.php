<?php

namespace App\Http\Controllers\Booking;

use Carbon\Carbon;
use Inertia\Inertia;
use App\Models\ActivityLog;
use App\Models\Brand\Brand;
use App\Models\Brand\Branch;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Booking\Addon\Addon;
use App\Models\Booking\Addon\AddonBranch;
use App\Models\Permission\RoleBranch;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;

class AddonsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:addons.index')->only(['index']);
        $this->middleware('checkPermission:addons.show')->only(['show']);
        $this->middleware('checkPermission:addons.edit')->only(['edit']);
        $this->middleware('checkPermission:addons.delete')->only(['destroy']);
        $this->middleware('checkPermission:addons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // ini_set('memory_limit', '-1');
        $brand_ids = [];
        $branch_ids = [];
        $user = auth()->user();
        if (Cookie::get('role_brands') && !$user->hasRole('Super Admin')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $type = $request->type;

        $addons = Addon::with(['brand', 'branches'])
            ->Ordered()
            ->Recent();

        if (isset($search) && $search != '') {
            $addons = $addons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $addons = $addons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $addons = $addons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $addons = $addons->where('active', $active);
        }
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $addons = $addons->where('brand_id', Cookie::get('brandId'));
        }
        if (count($brand_ids) > 0) {
            $addons = $addons->whereIn('brand_id', $brand_ids);
        }

        $addons = $addons->paginate(15)->appends(request()->query());
        //->withQueryString();

        $brands = Brand::Recent()->get();

        return Inertia::render('Booking/Addons/Index', [
            'data' => $addons,
            'brands' => $brands,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $branches = Branch::where('brand_id', Cookie::get('brandId'))
            ->Recent()
            ->get();

        return Inertia::render('Booking/Addons/Create', ['branches' => $branches]);
    }

    public function store(Request $request)
    {
        $data = $request->all();

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['sub_title'] = $request->sub_title_en;
        $data['ar']['sub_title'] = $request->sub_title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['enable_extra_info'] = $request->enableExtraInfo;
        if ($request->key) {
            $path = 'brands/' . $data['brand_id'] . '/Addons/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $addon = Addon::create($data);

        $branches = [];
        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                AddonBranch::create([
                    'branch_id' => $row,
                    'addon_id' => $addon->id,
                    'brand_id' => $addon->brand_id,
                ]);
            }
        }
        $pageTitle = __('theAddon');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('addons.index');
    }

    public function edit(Addon $addon)
    {
        $branches = Branch::where('brand_id', $addon->brand_id)
            ->Recent()
            ->get();

        $data = ['addon' => $addon, 'title' => $addon->title . ' Edit'];
        $branchIds = $addon->branch_ids;

        return Inertia::render('Booking/Addons/Edit', [
            'data' => $data,
            'branchIds' => $branchIds,
            'branches' => $branches,
        ]);
    }

    public function show($id)
    {
        $addon = Addon::where('id', $id)
            ->with('brand', 'branches')
            ->first();

        $activities = ActivityLog::where('subject_id', $addon->id)
            ->where('subject_type', 'App\Models\Booking\Addon\Addon')
            ->orderBy('created_at', 'DESC')
            ->select('*')
            ->get()
            ->groupBy(function ($date) {
                return Carbon::parse($date->created_at)->format('F Y');
            });

        $data = ['item' => $addon, 'title' => $addon->title . ' Info', 'activities' => $activities];

        return Inertia::render('Booking/Addons/Show', [
            'data' => $data,
        ]);
    }

    public function update(Request $request, Addon $addon)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['en']['sub_title'] = $request->sub_title_en;
        $data['ar']['sub_title'] = $request->sub_title_ar;

        if ($request->key) {
            $path = 'brands/' . $data['brand_id'] . '/Addons/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $addon->update($data);

        $branches = [];

        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                AddonBranch::updateOrCreate(
                    [
                        'branch_id' => $row,
                        'addon_id' => $addon->id,
                        'brand_id' => $addon->brand_id,
                    ],
                    [
                        'branch_id' => $row,
                        'addon_id' => $addon->id,
                        'brand_id' => $addon->brand_id,
                    ]
                );

                $addonBranches = AddonBranch::whereNotIn('branch_id', $data['branch_id'])
                    ->where('addon_id', $addon->id)
                    ->get();
                foreach ($addonBranches as $addonBranch) {
                    $addonBranch->delete();
                }
            }
        }

        $pageTitle = __('theAddon');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('addons.index');
    }

    public function destroy(Request $request, $id)
    {
        Addon::find($id)->delete();

        $pageTitle = __('theAddon');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('addons.index');
    }
}