<?php

namespace App\Http\Controllers;

use App\Exports\EmbedUsersExport;
use App\Exports\FoodicsExport;
use App\Exports\SaleExport;
use App\Exports\TicketCreatedExport;
use App\Exports\TicketsExport;
use App\Exports\TicketUpdatedExport;
use App\Exports\UsersExport;
use App\Helpers\Constant;
use App\Jobs\ExportEmailJob;
use App\Jobs\ExportJSONJob;
use App\Models\Governorate\Governorate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class ReportsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:all-reports.index')->only(['index']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $type = $request->type;
        $export_type = $request->export_type;
        $city = $request->city;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $reportTypes = Constant::ReportTypes;
        $exportTypes = Constant::ExportTypes;
        $cities = Governorate::publish()->get();

        return Inertia::render('Reports/Index', [
            'data' => [],
            'search' => $search,
            'type' => $type,
            'export_type' => $export_type,
            'city' => $city ?? '',
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'reportTypes' => $reportTypes,
            'cities' => $cities,
            'exportTypes' => $exportTypes,
        ]);
    }

    public function exportSaleReport($data, $brand, $exportType)
    {
        $filename = 'exports/reports/'.time().'-sale-reports.'.$exportType;

        // \Maatwebsite\Excel\Facades\Excel::store(new SaleExport($data, $brand), $filename);
        (new SaleExport($data, $brand))

            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/Sale-report.'.$exportType)])
            ->onQueue('report-sale');
    }

    public function exportEmbedCardReport($data, $exportType)
    {
        $filename = 'exports/reports/'.time().'-embed-cards-reports.'.$exportType;

        // \Maatwebsite\Excel\Facades\Excel::store(new SaleExport($data, $brand), $filename);
        (new EmbedUsersExport($data))

            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/Embed Card Registrations Reports')])
            ->onQueue('export-embed-cards');
    }

    public function exportTicketCreatedReport($data, $brand, $exportType)
    {
        $filename = 'exports/reports/'.time().'-item-master.'.$exportType;
        (new TicketCreatedExport($data, $brand))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/Item-master.'.$exportType)])
            ->onQueue('report-created-ticket');
    }

    public function exportTicketUpdatedReport($data, $brand, $exportType)
    {
        $filename = 'exports/reports/'.time().'-Price-history.'.$exportType;
        (new TicketUpdatedExport($data, $brand))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/Price-history.'.$exportType)])
            ->onQueue('report-updated-ticket');
    }

    public function exportFoodicsReport($data, $brand, $exportType)
    {
        // $this->middleware('checkPermission:foodics-reports.export')->only(['exportFoodicsReport']);
        $filename = 'exports/reports/'.time().'-Foodics-Sale-Reports.'.$exportType;
        (new FoodicsExport($data, $brand))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/Foodics-Sale-Reports.'.$exportType)])
            ->onQueue('report-foodics');
    }

    public function exportTicketsReport($data, $brand, $exportType)
    {
        $filename = 'exports/reports/'.time().'-Tickets-Sale-Reports.'.$exportType;
        (new TicketsExport($data, $brand))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/Tickets-Sale-Reports.'.$exportType)])
            ->onQueue('report-tickets');
    }

    public function exportUsersReport($data, $exportType)
    {
        $filename = 'exports/'.time().'-users..'.$exportType;
        (new UsersExport($data))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/users.csv')])
            ->onQueue('export-users');
    }

    public function store(Request $request)
    {
        $exportType = 'csv';
        $type = $request->type;
        $export_type = $request->export_type;
        $filename = '';

        if ($request->type == '') {
            $request->session()->flash('error', __('SelectReportType'));
        }
        if ($request->export_type == '') {
            $request->session()->flash('error', __('SelectExportType'));
        }
        $brand = Cookie::get('brandId') !== null && Cookie::get('brandId') !== '' ? Cookie::get('brandId') : '';
        // type == 0 --> ticket create  - report-created-ticket
        // type == 1 --> ticket update  - report-updated-ticket
        // type == 2 --> sale report    - report-sale
        // type == 3 --> foodics report - report-foodics
        // type == 3 --> tickets report - report-tickets

        if ($export_type == 1) {
            $exportType = 'json';
        }

        $data = $request->all();
        if ($export_type == 0) {
            if ($type == 0) {
                $this->exportTicketCreatedReport($data, $brand, $exportType);
            } elseif ($type == 1) {
                $this->exportTicketUpdatedReport($data, $brand, $exportType);
            } elseif ($type == 2) {
                $this->exportSaleReport($data, $brand, $exportType);
            } elseif ($type == 3) {
                $this->exportFoodicsReport($data, $brand, $exportType);
            } elseif ($type == 4) {
                $this->exportTicketsReport($data, $brand, $exportType);
            } elseif ($type == 5) {
                $this->exportUsersReport($data, $exportType);
            } elseif ($type == 6) {
                $this->exportEmbedCardReport($data, $exportType);
            }
        }
        if ($export_type == 1) {
            // call json
            $user = auth()->user();
            ExportJSONJob::dispatch($filename, $data, $user);
        }

        $request->session()->flash('success', __('ExportResultMessage'));

        $search = $request->search;
        $city = $request->city;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $reportTypes = Constant::ReportTypes;
        $exportTypes = Constant::ExportTypes;
        $cities = Governorate::publish()->get();

        return Inertia::render('Reports/Index', [
            'data' => [],
            'search' => $search,
            'type' => $type,
            'export_type' => $export_type,
            'city' => $city ?? '',
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'reportTypes' => $reportTypes,
            'exportTypes' => $exportTypes,
            'cities' => $cities,
        ]);
    }
}