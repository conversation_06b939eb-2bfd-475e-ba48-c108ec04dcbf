<?php

namespace App\Http\Controllers;

use App\Events\OrderNotification;
use App\Models\Ordering\Order;
use App\Models\ScanQrLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class GuestController extends Controller
{
    public function __construct()
    {
    }

    public function previewOrder($order_uuid)
    {
        $order = Order::where('order_uuid', $order_uuid)->first();

        $corporate = null;
        if (! empty($order)) {
            if ($order->is_bulk) {
                $corporate = $order->corporate;
            }
            $children = [];
            $view = 'Ordering/Orders/Preview';
            if ($order->is_parent) {
                $children = Order::where('parent', $order->id)->paginate(20);
            }
            if ($order->is_parent == 0) {
                $view = 'Ordering/Orders/SinglePreview';
            }

            return Inertia::render($view, [
                'data' => [
                    'order' => $order,
                    'children' => $children,
                    'corporate' => $corporate,
                ],
            ]);
        }
        abort(404);
    }

    public function assignOrder(Request $request, $order_uuid)
    {
        $order = Order::where('order_uuid', $order_uuid)->first();
        if (! empty($order)) {
            $userOrder = Order::where('id', $request->data['order_id'])->first();
            if (! empty($userOrder)) {
                $userOrder->update(['email' => $request->data['email']]);
                $request
                    ->session()
                    ->flash('success', 'Order assigned to '.$request->data['email'].' successfully!');
                //send order to email
                event(new OrderNotification($userOrder, 6));

                return redirect()->back();
            }
        }
        abort(404);
    }

    //resendEmail
    public function resendEmail(Request $request, $order_uuid)
    {
        $order = Order::where('order_uuid', $order_uuid)->first();
        if (! empty($order)) {
            $userOrder = Order::where('id', $request->data['order_id'])->first();

            if (! empty($userOrder)) {
                $request->session()->flash('success', __('messages.EmailSent'));
                //send order to email
                event(new OrderNotification($userOrder, 6));

                return redirect()->back();
            }
        }
        abort(404);
    }

    public function support()
    {
        return Inertia::render('Support');
    }

    public function privacy()
    {
        return Inertia::render('Privacy');
    }

    //downloadApp
    public function showDownloadApp(Request $request, $ref)
    {
        $ipAddress = request()->ip(); //REMOTE_ADDR

        if (Cookie::get('referral_token') || ScanQrLog::where('ip', $ipAddress)->count() > 0) {
            return redirect(config('app.download_app_link'));
        }
        if (isset($ref)) {
            $user = \App\Models\User::where('referral_token', $ref)->first();
            if (! empty($user)) {
                //browser data with ipand device_id
                $userAgent = $_SERVER['HTTP_USER_AGENT'];
                $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'];

                $fingerprint = $userAgent.$acceptLanguage.$ipAddress;
                $token = md5($fingerprint);
                ScanQrLog::firstOrCreate(
                    ['ip' => $ipAddress],
                    [
                        'fingerprint' => $token,
                        'lang' => $acceptLanguage,
                        'ip' => $ipAddress,
                        'referral_token' => $ref,
                        'user_agent' => $userAgent,
                        'user_id' => $user->id,
                    ]
                );

                Cookie::queue('referral_token', $ref);
            }
        }

        return redirect(config('app.download_app_link'));
    }
}
