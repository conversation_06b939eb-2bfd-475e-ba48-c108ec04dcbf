<?php

namespace App\Http\Controllers\Ordering;

use App\Events\CancelFoodicsOrder;
use App\Events\CouponOrderNotifications;
use App\Events\GroupSpendingData;
use App\Events\LoyaltyPointCreated;
use App\Events\OrderNotification;
use App\Events\PurchaseOrder;
use App\Events\RefundOrder;
use App\Events\ZatcaCreateInvoice;
use App\Exports\BulkOrdersExport;
use App\Exports\OrdersExport;
use App\Helpers\Constant;
use App\Helpers\FoodicsApi;
use App\Helpers\General;
use App\Helpers\SMS;
use App\Http\Controllers\Controller;
use App\Http\Requests\Ordering\OrderRequest;
use App\Jobs\CreateBulkTicket;
use App\Jobs\ExportEmailJob;
use App\Mail\BulkTicketsMail;
use App\Models\ActivityLog;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Booking\Ticket\TicketBranch;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Corporate\Corporate;
use App\Models\Ordering\FoodicsLog;
use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Models\Ordering\PaymentStatusLog;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Models\Ordering\ZatcaInvoice;
use App\Models\Permission\RoleBranch;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Wallet\WalletTransaction;
use App\Services\Foodics\FoodicsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Inertia\Inertia;

class OrdersController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:bulk-orders.index')->only(['bulkOrders']);
        $this->middleware('checkPermission:bulk-orders.create')->only(['newBulk']);
        $this->middleware('checkPermission:bulk-orders.paid')->only(['paid']);
        $this->middleware('checkPermission:bulk-orders.show')->only(['showBulk']);
        $this->middleware('checkPermission:orders.index')->only(['index']);
        $this->middleware('checkPermission:orders.show')->only(['show']);
        $this->middleware('checkPermission:orders.edit')->only(['edit']);
        $this->middleware('checkPermission:orders.delete')->only(['destroy']);
        $this->middleware('checkPermission:orders.cancel')->only(['cancel']);
        $this->middleware('checkPermission:orders.refund')->only(['refund']);
        $this->middleware('checkPermission:orders.claim')->only(['claim']);
        $this->middleware('checkPermission:orders.pay')->only(['pay']);
        $this->middleware('checkPermission:orders.changeBranch')->only(['changeBranch']);
    }

    public function index(Request $request)
    {
        // Start time
        $startTime = microtime(true);
        $brand_ids = [];
        $branches = [];
        $branch_ids = [];
        if (Cookie::get('role_brands')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $version = $request->version;
        $orders = Order::select(
            'id',
            'order_number',
            'user_id',
            'total_price',
            'price',
            'status',
            'branch_id',
            'order_date',
            'claimed_at',
            'status',
            'payment_status',
            'created_at',
            'parent_offer_order_id',
            'parent_order_transfer',
            'offer_id',
            'claimed_user_id'
        )
            ->orderBy('order_date', 'desc')
            ->orderBy('id', 'asc');
        $orders = $orders->where('is_parent', 0);
        $orders = $orders->where(function ($q) {
            $q->whereNull('offer_id');
            $q->orWhere(function ($q) {
                $q->whereNotNull('offer_id');
                $q->whereNotNull('parent_offer_order_id');
            });
        });

        // if (count($brand_ids) > 0) {
        //     $orders = $orders->whereIn('brand_id', $brand_ids);
        // }
        $orders = $orders->with([
            'user',
            'corporate',
            'parentOrderTransferObj',
            'claimed_user',
            'parentOfferOrder' => function ($query) {
                $query->without('user', 'brand', 'tickets');
                $query->with([
                    'offer_sub_orders' => function ($query) {
                        $query->without('user', 'brand', 'tickets');
                    },
                ]);
            },
        ]);

        if (count($branch_ids) > 0 && !(isset($search) && $search != '')) {
            $orders = $orders->whereIn('branch_id', $branch_ids);
        }
        // to not show the child orders for order transfer
        if (!isset($search)) {
            $orders = $orders->where('parent_order_transfer', null);
        }
        if (isset($search) && $search != '') {
            $search = str_replace('"', '', $search);
            $orders = $orders->where(function ($q) use ($search) {
                $q->where('order_number', 'like', '%'.$search.'%');
                $q->orWhere('qr_code', 'like', '%'.$search.'%');
                $q->orWhere('id', 'like', '%'.$search.'%');
                $q->orWhere('name', 'like', '%'.$search.'%');
                $q->orWhere('email', 'like', '%'.$search.'%');
                $q->orWhere('mobile', 'like', '%'.$search.'%');
            });
            $multipleOrderNumber = explode(' ', $search);
            if (count($multipleOrderNumber) > 1) {
                $orders = $orders->orWhereIn('order_number', $multipleOrderNumber);
                $orders = $orders->orWhereIn('id', $multipleOrderNumber);
            }
        } else {
            $orders = $orders->where('sending_status', '!=', 'receive');
        }
        $orders = $this->handleQueryRequest($orders, $request);

        // $orders = $orders->dd();
        $orders = $orders->paginate(env('PER_PAGE', 10));
        $title = __('all');
        if (isset($request->status)) {
            if ($request->status == 0) {
                $title = __('Upcoming');
            } elseif ($request->status == 1) {
                $title = __('Confirmed');
            } elseif ($request->status == 2) {
                $title = __('Cancelled');
            } elseif ($request->status == 3) {
                $title = __('DoesNotShow');
            }
        }

        $paymentStatuses = Constant::PaymentStatus;
        $paymentMethods = Constant::PaymentMethod;
        $statuses = Constant::OrderStatus;
        date_default_timezone_set('Asia/Riyadh');
        $time = date('Y-m-d');
        $branches = Branch::Recent()
            ->where('brand_id', Cookie::get('brandId'))
            ->get();
        $responseData = [
            'data' => $orders->withQueryString(),
            'branches' => $branches,
            'search' => $search,
            'status' => $request->status,
            'branch' => $request->branch ?? '',
            'fromDate' => $request->fromDate,
            'toDate' => $request->toDate,
            'bookingDate' => $request->bookingDate,
            'paymentStatus' => $request->payment_status,
            'paymentMethod' => $request->payment_method,
            'corporate' => $request->corporate,
            'title' => $title,
            'paymentStatuses' => $paymentStatuses,
            'paymentMethods' => $paymentMethods,
            'statuses' => $statuses,
            'now' => $time,
            'haveOffer' => $request->haveOffer,
        ];

        $responseSize = strlen(json_encode($responseData['data']));
        // End time and response time calculation
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

        return Inertia::render(
            'Ordering/Orders/Index',
            array_merge($responseData, [
                'responseSize' => $responseSize,
                'responseTime' => $responseTime,
            ])
        );
    }

    public function handleQueryRequest($q, $request)
    {
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $q->where('brand_id', Cookie::get('brandId'));
        }
        if (isset($request->branch) && $request->branch != '') {
            $q = $q->where('branch_id', $request->branch);
        }
        if (isset($request->fromDate) && $request->fromDate != '') {
            $q = $q->whereDate('created_at', '>=', $request->fromDate);
        }
        if (isset($request->toDate) && $request->toDate != '') {
            $q = $q->whereDate('created_at', '<=', $request->toDate);
        }
        if (isset($request->status) && $request->status != '') {
            $q = $q->where('status', $request->status);
        }
        if (isset($request->bookingDate) && $request->bookingDate != '') {
            $q = $q->whereDate('order_date', '=', $request->bookingDate);
        }

        if (isset($request->payment_method) && $request->payment_method != '') {
            $q = $q->where('payment_method', $request->payment_method);
        }
        if (isset($request->payment_status) && $request->payment_status != '') {
            $q = $q->where(function ($q) use ($request) {
                $q->whereDoesntHave('parentOfferOrder') // If no parentOfferOrder, use row's payment_status
                    ->where('payment_status', $request->payment_status)
                    ->orWhereHas('parentOfferOrder', function ($query) use ($request) {
                        $query->where('payment_status', $request->payment_status); // If parentOfferOrder exists, use its payment_status
                    });
            });
        }
        if (isset($request->corporate)) {
            $q = $q->where('corporate_request', $request->corporate);
        }

        if (isset($request->app_version)) {
            $q = $q->where('app_version', $request->app_version);
        }
        if (isset($request->haveOffer) && $request->haveOffer == '1') {
            // parent_offer_order_id or offer_id
            $orders = $q->whereNotNull('offer_id');
        } elseif (isset($request->haveOffer) && $request->haveOffer == '0') {
            // parent_offer_order_id or offer_id
            $orders = $q->whereNull('offer_id');
        }

        return $q;
    }

    public function edit(Request $request, Order $order)
    {
        if ($order->status != 0 || $order->parentOfferOrder) {
            $request->session()->flash('error', 'Order can\'t be editable ');

            return redirect()->route('orders.show', $order->id);
        }

        $brands = Brand::Recent()->get();
        $branches = Branch::where('brand_id', $order->brand_id)
            ->Recent()
            ->get();
        $paymentStatuses = Constant::PaymentStatus;
        $qrCodeStatuses = Constant::QrCodeStatus;
        date_default_timezone_set('Asia/Riyadh');
        $time = date('Y-m-d');

        return Inertia::render('Ordering/Orders/Edit', [
            'now' => $time,
            'data' => [
                'order' => $order,
                'begin' => date('Y-m-d'),
                'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
            ],
            'branches' => $branches,
            'brands' => $brands,
            'paymentStatuses' => $paymentStatuses,
            'qrCodeStatuses' => $qrCodeStatuses,
        ]);
    }

    public function exportLocal(Request $request)
    {
        $filePath = 'exports/'.time().'-orders.xlsx';
        $brand = Cookie::get('brandId') ?: '';

        \Excel::store(new OrdersExport($request->all(), $brand), $filePath, 'local');

        $fullPath = storage_path('app/'.$filePath);

        if (file_exists($fullPath)) {
            return response()
                ->download($fullPath)
                ->deleteFileAfterSend();
        }

        return back()->with('error', 'Failed to generate the export file.');
    }

    public function export(Request $request)
    {
        $filename = 'exports/'.time().'-orders.csv';
        $brand = Cookie::get('brandId') !== null && Cookie::get('brandId') !== '' ? Cookie::get('brandId') : '';
        (new OrdersExport($request->all(), $brand))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/orders.csv')])
            ->onQueue('export-transfer');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    public function show(Order $order)
    {
        $ableToBeCancelled = true;
        $ableToBeRefunded = true;

        if ($order->is_parent_order_transfer > 0) {
            // get the children orders
            $children = Order::where('parent_order_transfer', $order->id)->get();
            foreach ($children as $child) {
                if ($child->status == 1) {
                    $ableToBeCancelled = false;
                }
            }
        } elseif ($order->parent_order_transfer > 0) {
            $ableToBeCancelled = false;

            $ableToBeRefunded = false;
        }
        $order = Order::with('governorate', 'coupon', 'referral_user', 'parentOfferOrder', 'parentOrderTransferObj', 'addons.addon')

            ->where('id', $order->id)
            ->first();
        $corporate = null;
        $children = [];
        $child_order_transfer = [];
        $title = '';

        if ($order->status == 0) {
            $title = __('Upcoming');
        } elseif ($order->status == 1) {
            $title = __('Confirmed');
        } elseif ($order->status == 2) {
            $title = __('Cancelled');
        } elseif ($order->status == 3) {
            $title = __('DoesNotShow');
        }

        $view = 'Ordering/Orders/Show';
        if ($order->is_bulk) {
            $corporate = $order->corporate;
        }

        if ($order->is_parent) {
            $children = Order::where('parent', $order->id)->paginate(20);
            $view = 'Ordering/Orders/ShowBulk';
        }

        if ($order->is_parent_order_transfer) {
            $child_order_transfer = Order::where('parent_order_transfer', $order->id)->paginate(20);
        }

        $invoices = ZatcaInvoice::where('order_id', $order->id)->get();
        $offerOrders = Order::where('parent_offer_order_id', $order->id)->get();

        $offerOrderPayments = '';
        if (!empty($order->parentOfferOrder) && !empty($order->parentOfferOrder->payments())) {
            $offerOrderPayments = $order->parentOfferOrder->payments();
        }

        return Inertia::render($view, [
            'data' => [
                'order' => $order,
                'ableToBeCancelled' => $ableToBeCancelled,
                'ableToBeRefunded' => $ableToBeRefunded,
                'parentOfferOrder' => $order->parentOfferOrder,
                'parentOfferOrderPayments' => $offerOrderPayments,
                'retry_zatca' => ZatcaInvoice::where('order_id', $order->id)
                    ->where('reportingStatus', 'REPORTED')
                    ->count(),
                'invoices' => $invoices,
                'title' => $title,
                'children' => $children,
                'child_order_transfer' => $child_order_transfer,
                'corporate' => $corporate,
                'payFort' => Config::get('app.payFort_Admin_url'),
                'tamara' => Config::get('app.tamara_admin_url'),
                'foodicsUrl' => Config::get('app.foodics_Admin_url'),
                'payments' => $order->payments(),
                'offerOrders' => $offerOrders,
                'loyaltyPayments' => WalletTransaction::where('order_id', $order->id)
                    ->Deduct()
                    ->Loyalty()
                    ->sum('transaction_amount'),
                'walletPayments' => WalletTransaction::where('order_id', $order->id)
                    ->Deduct()
                    ->SalaCredit()
                    ->sum('transaction_amount'),

                'payfortPayments' => count($order->payments()) > 0
                        ? $order
                            ->payments()
                            ->where('method_type', '1')
                            ->sum('amount')
                        : $order->total_price,

                'tamaraPayments' => count($order->payments()) > 0
                        ? $order
                            ->payments()
                            ->where('method_type', '2')
                            ->sum('amount')
                        : 0,
                'refunded_payments' => $order->refunded_payments(),
            ],
        ]);
    }

    public function getOrderLogsById($id)
    {
        $order = Order::find($id);
        $activitiesIds = ActivityLog::where('subject_id', $id)
            ->where('subject_type', 'App\Models\Ordering\Order')
            ->where('created_at', '>=', $order->created_at)
            ->paginate(10);
        $links = $activitiesIds->links();

        $items = $activitiesIds->items();

        $activitiesIds = collect($items)->pluck('id')->toArray();

        $activities = ActivityLog::whereIn('id', $activitiesIds)->where('subject_id', $id)
            ->where('subject_type', 'App\Models\Ordering\Order')
            ->where('created_at', '>=', $order->created_at)

            ->get()
            ->groupBy(function ($date) {
                return Carbon::parse($date->created_at)->format('F Y');
            });

        return ['activities' => $activities, 'items' => $links];
    }

    public function showBulk(Request $request, $id)
    {
        $order = Order::find($id);
        $children = Order::where('parent', $order->id)->paginate(20);

        $corporate = $order->corporate;
        $view = 'Ordering/Orders/ShowBulk';

        return Inertia::render($view, [
            'data' => ['order' => $order, 'children' => $children, 'corporate' => $corporate],
        ]);
    }

    public function update(OrderRequest $request, Order $order)
    {
        $pageTitle = __('theOrder');

        // 'status' => Constant::ClaimOrder,
        if (
            ($order->sending_status == 'receive'
                && Order::where('status', Constant::ClaimOrder)
                    ->where('id', $order->parent_order_transfer)
                    ->count() > 0)
            || ($order->sending_status == 'sent'
                && (Order::where('status', Constant::ClaimOrder)
                    ->where('parent_order_transfer', $order->id)
                    ->count() > 0
                    || $order->sending_status == Constant::ClaimOrder))
        ) {
            $request->session()->flash('error', __('CantEditOrder', ['title' => $pageTitle]));

            return redirect()->route('orders.show', $order->id);
        }
        $paymentStatus = $order->payment_status;
        $oldDate = $order->order_date;
        $data = $request->all();
        $order->update($data);
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        if ($order->sending_status == 'sent' && $order->is_parent_order_transfer) {
            $children = Order::where('parent_order_transfer', $order->id);
            $children->update([
                'order_date' => date('Y-m-d', strtotime($order->order_date)),
                'order_time' => $order->order_time ? $order->order_time : null,
            ]);
            foreach ($children->get() as $child) {
                event(new OrderNotification($child, 1));
            }
        }
        if (strtotime($oldDate) != strtotime($data['order_date'])) {
            // update booking date
            event(new OrderNotification($order, 1));
        }
        if ($request->payment_status == 1 && $paymentStatus != 1) {
            event(new ZatcaCreateInvoice($order->id));
        }

        return redirect()->route('orders.show', $order->id);
    }

    public function destroy(Request $request, $id)
    {
        $order = Order::find($id);
        $order->delete();
        $request->session()->flash('success', 'Order deleted successfully!');

        return redirect()->route('orders.index');
    }

    public function cancel(Request $request, $id)
    {
        $refundData = $request->all();
        $order = Order::find($id);
        $updateDate = ['status' => Constant::CancelOrder];
        if ($order->is_parent && $order->is_bulk) {
            $updateDate['payment_status'] = 5;
            $order->update($updateDate);
            $order->children()->update($updateDate);

            return redirect()->route('bulk-orders.showBulk', $order->id);
        } else {
            // $this->refundOrderProcess($order, $refundData);

            if ($order->sending_status == 'receive') {
                $order->update($updateDate);
                Order::where('parent_offer_order_id', $order->id)->update($updateDate);
                $request->session()->flash('error', __('CantCancelChildOrder'));

                return redirect()->route('orders.show', $order->id);
            }

            if ($order->sending_status == 'sent' && $order->is_parent_order_transfer) {
                $this->refundOrderProcess($order, $refundData);
                $children = Order::where('parent_order_transfer', $order->id);
                $children->update($updateDate);
                foreach ($children->get() as $child) {
                    event(new OrderNotification($child, 2));
                }
            } else {
                $this->refundOrderProcess($order, $refundData);
            }
            $order->update($updateDate);
            Order::where('parent_offer_order_id', $order->id)->update($updateDate);
        }
        $request->session()->flash('success', 'Order canceled successfully!');

        if ($order->foodics_id != null) {
            event(new CancelFoodicsOrder($order)); // cancel order
        }
        event(new OrderNotification($order, 2));

        if ($order->coupon_id != null) {
            $order->update([
                'old_coupon_id' => $order->coupon_id,
                'coupon_id' => null,
            ]);
        }

        return redirect()->route('orders.show', $order->id);
    }

    public function refund(Request $request, $id)
    {
        $refundData = $request->all();
        $order = Order::find($id);

        // get payfort paid amount

        $this->refundOrderProcess($order, $refundData);

        $request->session()->flash('success', 'Order refunded successfully!');

        return redirect()->route('orders.show', $order->id);
    }

    public function refundOrderProcess($order, $refundData): void
    {
        $totalRefundedAmount = 0;
        if (isset($refundData['data']['refund']) && $refundData['data']['refund']) {
            if (
                isset($refundData['data']['amount'])
                && $order
                    ->payments()
                    ->where('method_type', '1')
                    ->sum('amount') > 0
                && $refundData['data']['amount'] > 0
            ) {
                event(new RefundOrder($order->id, $refundData['data']['amount'], 'payfort'));
                $totalRefundedAmount += $refundData['data']['amount'];
            }

            if (
                isset($refundData['data']['amount_tamara'])
                && $order
                    ->payments()
                    ->where('method_type', '2')
                    ->sum('amount') > 0
                && $refundData['data']['amount_tamara'] > 0
            ) {
                event(new RefundOrder($order->id, $refundData['data']['amount_tamara'], 'tamara'));
                $totalRefundedAmount += $refundData['data']['amount_tamara'];
            }

            if ($order->payments()) {
                if (isset($refundData['data']['amount_point']) && $refundData['data']['amount_point'] > 0) {
                    $wallet = WalletTransaction::create([
                        'user_id' => $order->user_id,
                        'order_id' => $order->id,
                        'transaction_amount' => $refundData['data']['amount_point'],
                        'transaction_type' => '1',
                        'transaction_reason' => 'Get from refunded order number :'.$order->order_number,
                        'currency_id' => 2,
                    ]);

                    $payment = [];
                    $payment['method_type'] = '0';
                    $payment['method_id'] = 2;
                    $payment['is_refund'] = true;
                    $payment['reference_number'] = time();
                    $payment['order_id'] = $order->id;
                    $payment['user_id'] = $order->user_id;
                    $payment['status'] = Constant::RefundOrder;
                    $payment['amount'] = $refundData['data']['amount_point'];
                    $payment['transaction_id'] = $wallet->id;

                    // 'transaction_id',
                    Payment::updateOrCreate(
                        [
                            'order_id' => $order->id,
                            'status' => Constant::RefundOrder,
                            'method_id' => 2,
                            'method_type' => '0',
                        ],
                        $payment
                    );
                    $totalRefundedAmount += $order
                        ->payments()
                        ->where('method_type', '0')
                        ->where('method_id', 2)
                        ->sum('amount');
                    $order->update([
                        'is_refunded' => 1,
                        'payment_status' => Constant::RefundOrder,
                    ]);
                }

                if (isset($refundData['data']['amount_wallet']) && $refundData['data']['amount_wallet'] > 0) {
                    $wallet = WalletTransaction::create([
                        'user_id' => $order->user_id,
                        'order_id' => $order->id,
                        'transaction_amount' => $refundData['data']['amount_wallet'],
                        'transaction_type' => '1',
                        'transaction_reason' => 'Get from refunded order number :'.$order->order_number,
                        'currency_id' => 1,
                    ]);
                    // id 1 means sala credit and 2 means loyalty
                    $payment = [];
                    $payment['method_type'] = '0';
                    $payment['method_id'] = 1;
                    $payment['is_refund'] = true;
                    $payment['reference_number'] = time();
                    $payment['order_id'] = $order->id;
                    $payment['user_id'] = $order->user_id;
                    $payment['status'] = Constant::RefundOrder;
                    $payment['amount'] = $refundData['data']['amount_wallet'];
                    $payment['transaction_id'] = $wallet->id;

                    // 'transaction_id',
                    Payment::create($payment);
                    $totalRefundedAmount += $order
                        ->payments()
                        ->where('method_type', '0')
                        ->where('method_id', 1)
                        ->sum('amount');
                    $order->update([
                        'is_refunded' => 1,
                        'payment_status' => Constant::RefundOrder,
                    ]);
                }
                $order->update([
                    'refund_amount' => $totalRefundedAmount,
                ]);
            }
            event(new OrderNotification($order, 8));
        }
    }

    public function claim(Request $request, $id)
    {
        $order = Order::find($id);
        if ($order->status != 0) {
            $request->session()->flash('error', 'Order already claimed.');

            return redirect()->route('orders.show', $order->id);
        }

        if ($order->tickets()->count() < 1 || ($order->tickets()->count() == 1 && $order->tickets[0]->quantity < 1)) {
            $request->session()->flash('error', __('CantClaimOrderWithNoTickets'));

            return redirect()->route('orders.show', $order->id);
        }

        $updates = [
            'status' => Constant::ClaimOrder,
            'payment_status' => Constant::PaidOrder,
            'claimed_at' => now(),
            'claimed_user_id' => auth()->user()->id,
        ];
        if ($request->branch_id && $request->branch_id != null && $request->branch_id != 'null') {
            $updates['branch_id'] = $request->branch_id;
        }
        $order->update($updates);

        // handle offer cases
        if (!empty($order->offer_id)) {
            // case offer sub order
            if (!empty($order->parent_offer_order_id)) {
                $offerOrder = $order->parentOfferOrder;
                if (!empty($offerOrder->offer_sub_orders) && count($offerOrder->offer_sub_orders) > 0) {
                    if ($offerOrder->status == 0) {
                        event(new LoyaltyPointCreated(null, $offerOrder, 'order'));
                    }
                    $offerSubOrdersStatuses = [];
                    foreach ($offerOrder->offer_sub_orders as $offerSubOrder) {
                        $offerSubOrdersStatuses[$offerSubOrder->status] = 1;
                    }
                    if (count($offerSubOrdersStatuses) == 1) {
                        $offerOrder->status = array_key_first($offerSubOrdersStatuses);
                    } elseif (count($offerSubOrdersStatuses) > 1) {
                        $offerOrder->status = 4;
                    }

                    $offerOrder->save();
                }
            // case main offer order
            } else {
                foreach ($order->offer_sub_orders as $offerSubOrder) {
                    $offerSubOrder->status = Constant::ClaimOrder;
                    $offerSubOrder->save();
                }
            }
        }

        $couponOrder = Coupon::Order()
            ->Publish()
            ->NotUser()
            ->where('notify_days', 0)
            ->first();

        if (!empty($couponOrder)) {
            event(new CouponOrderNotifications($order->user, $order, $couponOrder));
        }

        if (empty($order->parent_offer_order_id)) {
            event(new LoyaltyPointCreated(null, $order, 'order'));
        }
        event(new GroupSpendingData($order->id));

        $request->session()->flash('success', 'Order claimed successfully!');

        return redirect()->route('orders.index', ['status' => 1]);
    }

    // bulkClaim
    public function bulkClaim(Request $request)
    {
        $orders = Order::where('status', 0)
            ->where('order_date', '<=', date('Y-m-d'))
            ->whereIn('id', $request->order_ids)->get();

        $updates = [
            'status' => Constant::ClaimOrder,
            'payment_status' => Constant::PaidOrder,
            'claimed_at' => now(),
            'claimed_user_id' => auth()->user()->id,
        ];
        foreach ($orders as $order) {
            $order->update($updates);
            // handle offer cases
            if (!empty($order->offer_id)) {
                // case offer sub order
                if (!empty($order->parent_offer_order_id)) {
                    $offerOrder = $order->parentOfferOrder;
                    if (!empty($offerOrder->offer_sub_orders) && count($offerOrder->offer_sub_orders) > 0) {
                        if ($offerOrder->status == 0) {
                            event(new LoyaltyPointCreated(null, $offerOrder, 'order'));
                        }
                        $offerSubOrdersStatuses = [];
                        foreach ($offerOrder->offer_sub_orders as $offerSubOrder) {
                            $offerSubOrdersStatuses[$offerSubOrder->status] = 1;
                        }
                        if (count($offerSubOrdersStatuses) == 1) {
                            $offerOrder->status = array_key_first($offerSubOrdersStatuses);
                        } elseif (count($offerSubOrdersStatuses) > 1) {
                            $offerOrder->status = 4;
                        }

                        $offerOrder->save();
                    }
                // case main offer order
                } else {
                    foreach ($order->offer_sub_orders as $offerSubOrder) {
                        $offerSubOrder->status = Constant::ClaimOrder;
                        $offerSubOrder->save();
                    }
                }
            }

            $couponOrder = Coupon::Order()
                ->Publish()
                ->NotUser()
                ->where('notify_days', 0)
                ->first();

            if (!empty($couponOrder)) {
                event(new CouponOrderNotifications($order->user, $order, $couponOrder));
            }

            if (empty($order->parent_offer_order_id)) {
                event(new LoyaltyPointCreated(null, $order, 'order'));
            }
            event(new GroupSpendingData($order->id));
        }
        $request->session()->flash('success', 'Orders claimed successfully!');

        return redirect()->route('orders.index', ['status' => 1]);
    }

    public function pay(Request $request, $id)
    {
        $order = Order::find($id);
        $back = event(new PurchaseOrder($order->id));
    }

    public function paid(Request $request, $id)
    {
        $order = Order::find($id);

        if ($order->status != 0) {
            $request->session()->flash('error', 'Order already paid.');

            return redirect()->route('orders.show', $order->id);
        }

        $order->payment_status = Constant::PaidOrder;
        $order->save();
        Order::where('parent', $order->id)->update(['payment_status' => Constant::PaidOrder]);
        event(new ZatcaCreateInvoice($order->id));
        $childeOrders = $order->children;
        $this->sendBulkOrderUpdateEmail($order);

        return redirect()->route('bulk-orders.bulkOrders');
    }

    public function children(Request $request, $id)
    {
        $order = Order::find($id);

        return response()
            ->json(['children' => $order->children])
            ->header('Content-Type', 'application/json');
    }

    public function newBulk(Request $request)
    {
        if ($request->isMethod('post')) {
            $generalHelper = new General();
            $data = $request->all();
            $orderItemIds = array_column($data['orderItems'], 'id');

            $dataOrder = [];
            $dataOrder['customer_type'] = $data['customer_type'];
            $dataOrder['corporate_request'] = $data['corporate_request'];
            $dataOrder['order_date'] = $data['order_date'];
            $dataOrder['expire_date'] = $data['expire_date'];
            $dataOrder['is_bulk'] = 1;
            $dataOrder['is_parent'] = 1;
            $dataOrder['payment_method'] = $generalHelper->getConstantItemByTitle('PaymentMethod', 'Bank Transfer')[
                'id'
            ];
            $dataOrder['status'] = $generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
            $dataOrder['payment_status'] = $generalHelper->getConstantItemByTitle('PaymentStatus', 'waiting')['id'];

            foreach ($data['brand_id'] as $brand) {
                $dataOrder['brand_id'] = $brand;

                $branchIds = TicketBranch::whereIn('ticket_id', $orderItemIds)
                    ->whereIn('branch_id', $data['branch_id'])
                    ->pluck('branch_id')
                    ->toArray();

                $branches = Branch::whereIn('id', $branchIds)
                    ->where('brand_id', $brand)
                    ->pluck('id')
                    ->toArray();
                if (!empty($branches) && count($branches) > 0) {
                    $dataOrder['branch_id'] = $branches[0];

                    $ticketIds = TicketBranch::whereIn('ticket_id', $orderItemIds)
                        ->where('branch_id', $dataOrder['branch_id'])
                        ->pluck('ticket_id')
                        ->toArray();

                    $items = [];
                    foreach ($ticketIds as $id) {
                        $key = array_search($id, array_column($data['orderItems'], 'id'));
                        array_push($items, $data['orderItems'][$key]);
                    }

                    $totalItemsPrice = $this->totalPriceCalc($items);

                    $dataOrder['price'] = $totalItemsPrice;
                    $dataOrder['total_price'] = $totalItemsPrice;
                    $latestOrder = Order::orderBy('created_at', 'DESC')->first();
                    $dataOrder['order_number'] = $latestOrder ? $latestOrder->id.uniqid('Sala') : ''.uniqid('Sala');

                    // DB::beginTransaction();
                    // try {
                    $dataOrder['order_uuid'] = Str::orderedUuid();
                    $order = Order::create($dataOrder);

                    // create available OrderBranch
                    // order , data,general
                    // $this->storeOrderItems($data['orderItems'], $order, $generalHelper);

                    $createBulkTicketJobs = (new CreateBulkTicket($items, $order, $generalHelper, $branches))->onQueue(
                        'bulk-tickets'
                    );
                    $this->dispatch($createBulkTicketJobs);
                }

                // } catch (\Exception $e) {
                //     DB::rollBack();
                // }

                // DB::commit();
            }
            $request
                ->session()
                ->flash(
                    'success',
                    'the create bulk order is processing, will send email to corporate after it finished !'
                );
            // $this->sendBulkOrderUpdateEmail($order);

            // return redirect()->route('bulk-orders.showBulk', $order->id);
            return redirect()->route('bulk-orders.bulkOrders');
        }

        return Inertia::render('Ordering/Orders/Bulk/Create', ['data' => []]);
    }

    public function exportBulk(Request $request, $id)
    {
        $filename = 'exports/'.time().'-bulk-orders.csv';
        (new BulkOrdersExport($id))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/bulk-orders.csv')])
            ->onQueue('bulk-exports');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    public function sendBulkOrderUpdateEmail($order)
    {
        $corporate = Corporate::find($order->corporate_request);
        $childeOrders = $order->children;
        $emailData = [];
        $emailData['tickets'] = $childeOrders;
        $emailData['order_number'] = $order->order_number;
        $emailData['corporate_name'] = $corporate->name;
        $emailData['content'] = 'Order Paid Successful, you can check order status on the following link:';
        $emailData['link'] = route('orders.preview', $order->order_uuid);

        $mailDataView = new BulkTicketsMail($emailData);
        Mail::to($corporate['email'])->send($mailDataView);
        SMS::send($corporate['mobile'], __('messages.bulkOrderSMS', ['link' => $emailData['link']]));
    }

    public function totalPriceCalc($orderItems)
    {
        $total = 0;
        foreach ($orderItems as $item) {
            $total = $total + $item['price'] * $item['quantity'];
        }

        return $total;
    }

    public function paymentLogs($id)
    {
        $logs = PaymentStatusLog::where('order_id', $id)->paginate(15);

        return Inertia::render('Ordering/Orders/PaymentStatusLog', ['data' => $logs]);
    }

    public function foodicsLogs($id)
    {
        $logs = FoodicsLog::where('order_id', $id)->paginate(15);

        return Inertia::render('Ordering/Orders/FoodicsLog', ['data' => $logs]);
    }

    public function storeOrderItems($orderItems, $order, $generalHelper)
    {
        foreach ($orderItems as $item) {
            $quantity = $item['quantity'];
            for ($i = 0; $i < $quantity; ++$i) {
                $childOrder = [];
                $childOrder['is_bulk'] = 1;
                $childOrder['payment_method'] = $generalHelper->getConstantItemByTitle(
                    'PaymentMethod',
                    'Bank Transfer'
                )['id'];
                $childOrder['status'] = $generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
                $childOrder['payment_status'] = $generalHelper->getConstantItemByTitle('PaymentStatus', 'waiting')[
                    'id'
                ];
                $totalItemsPrice = $item['price'];
                $childOrder['order_uuid'] = Str::orderedUuid();
                $childOrder['price'] = $totalItemsPrice;
                $childOrder['total_price'] = $totalItemsPrice;
                $childOrder['order_date'] = date('Y-m-d', strtotime($order->order_date));
                $childOrder['expire_date'] = date('Y-m-d', strtotime($order->expire_date));
                $childOrder['order_number'] = $order->id.uniqid('Sala');
                $childOrder['parent'] = $order->id;
                $childOrder['brand_id'] = $order->brand_id;
                $childOrder['branch_id'] = $order->branch_id;
                $childOrder['corporate_request'] = $order->corporate_request;
                $childOrder = Order::create($childOrder);

                $ticket = Ticket::where('id', $item['id'])->first();
                $orderTicket = [];
                $orderTicket['price'] = $item['price'];
                $orderTicket['quantity'] = 1;
                $orderTicket['total_price'] = $item['price'];
                $orderTicket['ticket_id'] = $item['id'];
                $orderTicket['order_id'] = $childOrder->id;
                $orderTicket = OrderTicket::create($orderTicket);
                // if (!$orderTicket) {
                //     DB::rollBack();
                //     break;
                // }

                $this->updateExtraInfo($orderTicket);
            }
        }
    }

    public function updateExtraInfo($orderTicket)
    {
        $info = [];
        $bookingItem = Ticket::where('id', $orderTicket['ticket_id'])->first();
        $info['ar']['title'] = $bookingItem->getTitleArAttribute();
        $info['en']['title'] = $bookingItem->getTitleEnAttribute();
        $info['ar']['sub_title'] = $bookingItem->getSubTitleArAttribute();
        $info['en']['sub_title'] = $bookingItem->getSubTitleEnAttribute();
        $orderTicket->update($info);
    }

    public function bulkOrders(Request $request)
    {
        $startTime = microtime(true);

        $search = $request->search;
        $orders = Order::select(
            'id',
            'order_number',
            'is_bulk',
            'corporate_request',
            'price',
            'total_price',
            'branch_id',
            'status',
            'payment_status',
            'order_date',
            'created_at'
        )
            ->without('user', 'brand', 'claimed_user', 'tickets', 'bundles')
            ->whereHas('corporate')
            ->orderBy('order_date', 'desc');
        $orders = $orders->where(['is_bulk' => 1, 'is_parent' => 1]);

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $orders = $orders->where('brand_id', Cookie::get('brandId'));
        }

        if (isset($search) && $search != '') {
            $orders = $orders->where(function ($q) use ($search) {
                $q->where('order_number', 'like', '%'.$search.'%');
                $q->orWhere('qr_code', 'like', '%'.$search.'%');
                $q->orWhere('id', 'like', '%'.$search.'%');
                $q->orWhere('name', 'like', '%'.$search.'%');
                $q->orWhere('email', 'like', '%'.$search.'%');
                $q->orWhere('mobile', 'like', '%'.$search.'%');
            });
        }

        if (isset($request->fromDate) && $request->fromDate != '') {
            $orders = $orders->whereDate('created_at', '>=', $request->fromDate);
        }
        if (isset($request->toDate) && $request->toDate != '') {
            $orders = $orders->whereDate('created_at', '<=', $request->toDate);
        }
        if (isset($request->status) && $request->status != '') {
            $orders = $orders->where('status', $request->status);
        }

        if (isset($request->payment_method) && $request->payment_method != '') {
            $orders = $orders->where('payment_method', $request->payment_method);
        }
        if (isset($request->payment_status) && $request->payment_status != '') {
            $orders = $orders->where('payment_status', $request->payment_status);
        }

        $preDefinedCorporate = null;
        if (isset($request->corporate)) {
            $orders = $orders->where('corporate_request', $request->corporate);
            $preDefinedCorporate = Corporate::find($request->corporate);
            $preDefinedCorporate = ['id' => $preDefinedCorporate->id, 'name' => $preDefinedCorporate->name];
        }
        $orders = $orders->paginate(env('PER_PAGE', 10));

        $title = 'All';
        if (isset($request->status) && !empty($request->status)) {
            if ($request->status == 0) {
                $title = 'Upcoming';
            } elseif ($request->status == 1) {
                $title = 'Confirmed';
            } elseif ($request->status == 2) {
                $title = 'Cancelled';
            } elseif ($request->status == 3) {
                $title = 'Didn\'t show';
            }
        }

        $paymentStatuses = Constant::PaymentStatus;
        $paymentMethods = Constant::PaymentMethod;
        $statuses = Constant::OrderStatus;

        $responseTime = round(microtime(true) - $startTime, 2);
        $responseData = [
            'data' => $orders->withQueryString(),
            'search' => $search,
            'status' => $request->status,
            'fromDate' => $request->fromDate,
            'toDate' => $request->toDate,
            'paymentStatus' => $request->payment_status,
            'paymentMethod' => $request->payment_method,
            'corporate' => $request->corporate,
            'preDefinedCorporate' => $preDefinedCorporate,
            'title' => $title,
            'paymentStatuses' => $paymentStatuses,
            'paymentMethods' => $paymentMethods,
            'statuses' => $statuses,
        ];

        $responseSize = round(strlen(json_encode($responseData)) / (1024 * 1024), 2);

        return Inertia::render(
            'Ordering/Orders/BulkOrders',
            array_merge($responseData, [
                'responseTime' => $responseTime,
                'responseSize' => $responseSize,
            ])
        );
    }

    public function bulkTickets(Request $request, $corporateId)
    {
        $orders = Order::where([
            'is_bulk' => 1,
            'is_parent' => 0,
            'corporate_request' => $corporateId,
        ])->paginate(20);

        $corporate = Corporate::find($corporateId);

        return Inertia::render('Ordering/Orders/bulkTickets', [
            'data' => $orders,
            'corporate' => $corporate,
        ]);
    }
    // bulkTicketExports

    public function bulkTicketExports(Request $request, $corporateId)
    {
        $filename = 'exports/'.time().'-bulk-orders.csv';

        (new BulkOrdersExport($corporateId, 'corporate'))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/bulk-orders.csv')])
            ->onQueue('bulk-ticket-exports');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    public function getFoodicsProducts(Request $request, $brandId = null)
    {
        $url = 'products?page=1&filter[is_active]=true&filter[name]='.$request->search;
        $data = [];

        $brand = Brand::find($brandId);
        $result = (new FoodicsService())->sendRequest($url, [], 'GET', $brand->foodics_token, 'admin');
        if (!empty($result->data)) {
            foreach ($result->data as $product) {
                if (strncmp($product->sku, 'FP', 2) === 0 && $product->is_active == true) {
                    $product->name = $product->name.' - '.$product->sku;
                    $product->name_localized = $product->name_localized.' - '.$product->sku;
                    array_push($data, $product);
                }
            }
        }

        return response()
            ->json(['products' => $data])
            ->header('Content-Type', 'application/json');
    }

    public function getFoodicsProductById(Request $request, $brandId = null, $productId = null)
    {
        $url = 'products/'.$productId;

        $brand = Brand::find($brandId);
        $result = FoodicsApi::sendRequest($url, [], 'GET', $brand->foodics_token);

        return response()
            ->json(['data' => $result])
            ->header('Content-Type', 'application/json');
    }

    public function changeStatus(Request $request, $id)
    {
        $order = Order::find($id);
        $updateDate = ['status' => Constant::UpcomingOrder];
        $order->update($updateDate);
        $request->session()->flash('success', 'Order status is updated successfully!');

        return redirect()->route('orders.edit', $order->id);
    }

    public function resendEmailNotify(Request $request, $id)
    {
        $order = Order::find($id);
        // send email
        $corporate = Corporate::find($order->corporate_request);
        $childeOrders = $order->children;
        $emailData = [];
        $emailData['tickets'] = $childeOrders;
        $emailData['order_number'] = $order->order_number;
        $emailData['corporate_name'] = $corporate->name;
        $emailData['content'] = 'Order created Successful, you can check order status on the following link:';
        $emailData['link'] = route('orders.preview', $order->order_uuid);

        $mailDataView = new BulkTicketsMail($emailData);
        Mail::to($corporate['email'])->send($mailDataView);
        $request->session()->flash('success', __('messages.EmailSent'));

        return redirect()->back();
    }

    public function weeklyOrders(Request $request)
    {
        $startOfWeek = $request->query('start');
        $endOfWeek = $request->query('end');
        $branch = $request->query('branch');
        $branch_ids = [];
        if (Cookie::get('role_brands')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
            if (count($branch_ids) < 1) {
                $branch_ids = Branch::whereIn('brand_id', $brand_ids)
                    ->pluck('id')
                    ->toArray();
            }
        }
        if (!isset($startOfWeek)) {
            $startOfWeek = Carbon::now()->startOfWeek();
            $endOfWeek = Carbon::now()->endOfWeek();
        }

        $orders = DB::table('orders')
            ->where('orders.status', Constant::UpcomingOrder)
            ->whereNull('orders.claimed_at')
            ->selectRaw('DATE(orders.order_date) as date, COUNT(*) as count')
            ->whereBetween('orders.order_date', [$startOfWeek, $endOfWeek])
            ->groupBy('orders.order_date');

        if (count($branch_ids) > 0) {
            $orders = $orders->whereIn('orders.branch_id', $branch_ids);
        }
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $orders = $orders->where('orders.brand_id', Cookie::get('brandId'));
        }
        if (isset($branch) && $branch != '' && $branch != 'undefined') {
            $orders = $orders->where('orders.branch_id', $branch);
        }

        $transferedOrders = clone $orders;
        $transferedOrders = $transferedOrders
            ->join('orders as order_transfer', 'orders.parent_order_transfer', '=', 'order_transfer.id')
            ->where('order_transfer.payment_status', Constant::PaidOrder)
            ->whereNotNull('orders.parent_order_transfer')
            ->get();

        $bundleOrders = clone $orders;
        $bundleOrders = $bundleOrders
            ->whereNotNull('orders.parent_offer_order_id')
            ->join('orders as offer_parent_order', 'orders.parent_offer_order_id', '=', 'offer_parent_order.id')
            ->where('offer_parent_order.payment_status', Constant::PaidOrder)
            ->get();

        $normal = clone $orders;
        $normal = $normal
            ->where('orders.payment_status', Constant::PaidOrder)
            ->whereNull('orders.parent_offer_order_id')
            ->whereNull('orders.parent_order_transfer')
            ->whereNull('orders.offer_id')
            ->get();

        $result = [
            'normal' => $normal,
            'transferedOrders' => $transferedOrders,
            'bundleOrders' => $bundleOrders,
        ];

        return response()->json($result);
    }

    public function changeBranch(Request $request)
    {
        $data = $request->all();

        if (isset($data['branch_id'])) {
            $orders = Order::whereIn('id', $request->order_ids)->get();
            $orders->each(function ($order) use ($data) {
                $order->update(['branch_id' => $data['branch_id']]);
            });
            $request->session()->flash('success', 'Branch changed successfully!');
        }
        $orders = Order::whereIn('id', $request->order_ids)->paginate();

        return Inertia::render('Ordering/Orders/ChangeBranch', [
            'data' => $orders,
            'title' => __('ChangeBranch'),
            'order_ids' => $request->order_ids,
            'branches' => Branch::where('brand_id', Cookie::get('brandId'))->get(),
        ]);
    }
}
