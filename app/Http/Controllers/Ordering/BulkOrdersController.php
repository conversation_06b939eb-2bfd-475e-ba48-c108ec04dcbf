<?php

namespace App\Http\Controllers\Ordering;

use App\Helpers\SMS;
use Inertia\Inertia;
use App\Helpers\General;
use App\Helpers\Constant;
use App\Helpers\FoodicsApi;
use App\Models\Brand\Brand;
use Illuminate\Support\Str;
use App\Jobs\ExportEmailJob;
use Illuminate\Http\Request;
use App\Mail\BulkTicketsMail;
use App\Jobs\CreateBulkTicket;
use App\Models\Ordering\Order;
use App\Exports\BulkOrdersExport;
use App\Models\Corporate\Corporate;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Models\Booking\Ticket\Ticket;
use Illuminate\Support\Facades\Cookie;
use App\Models\Ordering\PaymentStatusLog;
use App\Models\Ordering\Ticket\OrderTicket;

class OrdersController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:bulk-orders.index')->only(['bulkOrders']);
        $this->middleware('checkPermission:bulk-orders.create')->only(['newBulk']);
        $this->middleware('checkPermission:bulk-orders.paid')->only(['paid']);
        $this->middleware('checkPermission:bulk-orders.show')->only(['showBulk']);
    }

    public function showBulk(Request $request, $id)
    {
        $order = Order::find($id);
        $children = Order::where('parent', $order->id)->paginate(20);

        $corporate = $order->corporate;
        $view = 'Ordering/Orders/ShowBulk';

        return Inertia::render($view, [
            'data' => ['order' => $order, 'children' => $children, 'corporate' => $corporate],
        ]);
    }

    public function children(Request $request, $id)
    {
        $order = Order::find($id);

        return response()
            ->json(['children' => $order->children])
            ->header('Content-Type', 'application/json');
    }

    public function newBulk(Request $request)
    {
        if ($request->isMethod('post')) {
            $generalHelper = new General();
            $data = $request->all();
            $data['is_bulk'] = 1;
            $data['is_parent'] = 1;
            $data['payment_method'] = $generalHelper->getConstantItemByTitle('PaymentMethod', 'Bank Transfer')['id'];
            $data['status'] = $generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
            $data['payment_status'] = $generalHelper->getConstantItemByTitle('PaymentStatus', 'waiting')['id'];

            $totalItemsPrice = $this->totalPriceCalc($data['orderItems']);

            $data['price'] = $totalItemsPrice;
            $data['total_price'] = $totalItemsPrice;
            $latestOrder = Order::orderBy('created_at', 'DESC')->first();
            $data['order_number'] = $latestOrder ? $latestOrder->id . uniqid('Sala') : '' . uniqid('Sala');

            // DB::beginTransaction();
            // try {
            $data['order_uuid'] = Str::orderedUuid();
            $order = Order::create($data);
            //order , data,general
            // $this->storeOrderItems($data['orderItems'], $order, $generalHelper);

            $createBulkTicketJobs = (new CreateBulkTicket($data['orderItems'], $order, $generalHelper))->onQueue(
                'bulk-tickets'
            );
            $this->dispatch($createBulkTicketJobs);
            // } catch (\Exception $e) {
            //     DB::rollBack();
            // }

            // DB::commit();
            $request
                ->session()
                ->flash(
                    'success',
                    'the create bulk order is processing, will send email to corporate after it finished !'
                );
            // $this->sendBulkOrderUpdateEmail($order);

            // return redirect()->route('bulk-orders.showBulk', $order->id);
            return redirect()->route('bulk-orders.bulkOrders');
        }

        return Inertia::render('Ordering/Orders/Bulk/Create', ['data' => []]);
    }

    public function exportBulk(Request $request, $id)
    {
        $filename = 'exports/' . time() . '-bulk-orders.csv';
        (new BulkOrdersExport($id))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/bulk-orders.csv')])
            ->onQueue('bulk-exports');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }

    public function sendBulkOrderUpdateEmail($order)
    {
        $corporate = Corporate::find($order->corporate_request);
        $childeOrders = $order->children;
        $emailData = [];
        $emailData['tickets'] = $childeOrders;
        $emailData['order_number'] = $order->order_number;
        $emailData['corporate_name'] = $corporate->name;
        $emailData['content'] = 'Order Paid Successful, you can check order status on the following link:';
        $emailData['link'] = route('orders.preview', $order->order_uuid);

        $mailDataView = new BulkTicketsMail($emailData);
        Mail::to($corporate['email'])->send($mailDataView);
        SMS::send($corporate['mobile'], __('messages.bulkOrderSMS', ['link' => $emailData['link']]));
    }

    public function totalPriceCalc($orderItems)
    {
        $total = 0;
        foreach ($orderItems as $item) {
            $total = $total + $item['price'] * $item['quantity'];
        }

        return $total;
    }

    public function paymentLogs($id)
    {
        $logs = PaymentStatusLog::where('order_id', $id)->paginate(15);

        return Inertia::render('Ordering/Orders/PaymentStatusLog', ['data' => $logs]);
    }

    public function storeOrderItems($orderItems, $order, $generalHelper)
    {
        foreach ($orderItems as $item) {
            $quantity = $item['quantity'];
            for ($i = 0; $i < $quantity; $i++) {
                $childOrder = [];
                $childOrder['is_bulk'] = 1;
                $childOrder['payment_method'] = $generalHelper->getConstantItemByTitle(
                    'PaymentMethod',
                    'Bank Transfer'
                )['id'];
                $childOrder['status'] = $generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
                $childOrder['payment_status'] = $generalHelper->getConstantItemByTitle('PaymentStatus', 'waiting')[
                    'id'
                ];
                $totalItemsPrice = $item['price'];
                $childOrder['order_uuid'] = Str::orderedUuid();
                $childOrder['price'] = $totalItemsPrice;
                $childOrder['total_price'] = $totalItemsPrice;
                $childOrder['order_date'] = date('Y-m-d', strtotime($order->order_date));
                $childOrder['expire_date'] = date('Y-m-d', strtotime($order->expire_date));
                $childOrder['order_number'] = $order->id . uniqid('Sala');
                $childOrder['parent'] = $order->id;
                $childOrder['brand_id'] = $order->brand_id;
                $childOrder['branch_id'] = $order->branch_id;
                $childOrder['corporate_request'] = $order->corporate_request;
                $childOrder = Order::create($childOrder);

                $ticket = Ticket::where('id', $item['id'])->first();
                $orderTicket = [];
                $orderTicket['price'] = $item['price'];
                $orderTicket['quantity'] = 1;
                $orderTicket['total_price'] = $item['price'];
                $orderTicket['ticket_id'] = $item['id'];
                $orderTicket['order_id'] = $childOrder->id;
                $orderTicket = OrderTicket::create($orderTicket);
                // if (!$orderTicket) {
                //     DB::rollBack();
                //     break;
                // }

                $this->updateExtraInfo($orderTicket);
            }
        }
    }

    public function updateExtraInfo($orderTicket)
    {
        $info = [];
        $bookingItem = Ticket::where('id', $orderTicket['ticket_id'])->first();
        $info['ar']['title'] = $bookingItem->getTitleArAttribute();
        $info['en']['title'] = $bookingItem->getTitleEnAttribute();
        $info['ar']['sub_title'] = $bookingItem->getSubTitleArAttribute();
        $info['en']['sub_title'] = $bookingItem->getSubTitleEnAttribute();
        $orderTicket->update($info);
    }

    public function bulkOrders(Request $request)
    {
        $search = $request->search;
        $orders = Order::orderBy('order_date', 'desc');
        $orders = $orders->where(['is_bulk' => 1, 'is_parent' => 1]);

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $orders = $orders->where('brand_id', Cookie::get('brandId'));
        }

        if (isset($search) && $search != '') {
            $orders = $orders->where(function ($q) use ($search) {
                $q->where('order_number', 'like', '%' . $search . '%');
                $q->orWhere('qr_code', 'like', '%' . $search . '%');
                $q->orWhere('id', 'like', '%' . $search . '%');
                $q->orWhere('name', 'like', '%' . $search . '%');
                $q->orWhere('email', 'like', '%' . $search . '%');
                $q->orWhere('mobile', 'like', '%' . $search . '%');
            });
        }

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $orders = $orders->where('brand_id', Cookie::get('brandId'));
        }

        if (isset($request->fromDate) && $request->fromDate != '') {
            $orders = $orders->whereDate('created_at', '>=', $request->fromDate);
        }
        if (isset($request->toDate) && $request->toDate != '') {
            $orders = $orders->whereDate('created_at', '<=', $request->toDate);
        }
        if (isset($request->status) && $request->status != '') {
            $orders = $orders->where('status', $request->status);
        }

        if (isset($request->payment_method) && $request->payment_method != '') {
            $orders = $orders->where('payment_method', $request->payment_method);
        }
        if (isset($request->payment_status) && $request->payment_status != '') {
            $orders = $orders->where('payment_status', $request->payment_status);
        }

        $preDefinedCorporate = null;
        if (isset($request->corporate)) {
            $orders = $orders->where('corporate_request', $request->corporate);
            $preDefinedCorporate = Corporate::find($request->corporate);
            $preDefinedCorporate = ['id' => $preDefinedCorporate->id, 'name' => $preDefinedCorporate->name];
        }
        $orders = $orders->paginate(10);

        $title = 'All';
        if (isset($request->status) && !empty($request->status)) {
            if ($request->status == 0) {
                $title = 'Upcoming';
            } elseif ($request->status == 1) {
                $title = 'Confirmed';
            } elseif ($request->status == 2) {
                $title = 'Cancelled';
            } elseif ($request->status == 3) {
                $title = 'Didn\'t show';
            }
        }

        $paymentStatuses = Constant::PaymentStatus;
        $paymentMethods = Constant::PaymentMethod;
        $statuses = Constant::OrderStatus;

        return Inertia::render('Ordering/Orders/BulkOrders', [
            'data' => $orders,
            'search' => $search,
            'status' => $request->status,
            'fromDate' => $request->fromDate,
            'toDate' => $request->toDate,
            'paymentStatus' => $request->payment_status,
            'paymentMethod' => $request->payment_method,
            'corporate' => $request->corporate,
            'preDefinedCorporate' => $preDefinedCorporate,
            'title' => $title,
            'paymentStatuses' => $paymentStatuses,
            'paymentMethods' => $paymentMethods,
            'statuses' => $statuses,
        ]);
    }

    public function bulkTickets(Request $request, $corporateId)
    {
        $orders = Order::where([
            'is_bulk' => 1,
            'is_parent' => 0,
            'corporate_request' => $corporateId,
        ])->get();

        $corporate = Corporate::find($corporateId);

        return Inertia::render('Ordering/Orders/bulkTickets', [
            'data' => $orders,
            'corporate' => $corporate,
        ]);
    }

    public function getFoodicsProducts(Request $request, $brandId = null)
    {
        $url = 'products?filter[is_active]=true';
        $data = [];

        $brand = Brand::find($brandId);
        $result = FoodicsApi::sendRequest($url, [], 'GET', $brand->foodics_token);

        return response()
            ->json(['products' => $result])
            ->header('Content-Type', 'application/json');
    }

    public function getFoodicsProductById(Request $request, $brandId = null, $productId = null)
    {
        $url = 'products/' . $productId;

        $brand = Brand::find($brandId);
        $result = FoodicsApi::sendRequest($url, [], 'GET', $brand->foodics_token);

        return response()
            ->json(['data' => $result])
            ->header('Content-Type', 'application/json');
    }
}
