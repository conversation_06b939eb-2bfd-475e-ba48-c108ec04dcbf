<?php

namespace App\Http\Controllers\Ordering;

use App\Events\ZatcaCreateInvoice;
use App\Events\ZatcaPullAndCleanInvoice;
use App\Http\Controllers\Controller;
use App\Models\Ordering\Order;
use Illuminate\Http\Request;

class InvoicesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:invoices.update')->only(['pull']);
        $this->middleware('checkPermission:invoices.create')->only(['send']);
    }

    public function pull(Request $request)
    {
        event(new ZatcaPullAndCleanInvoice());
        $request->session()->flash('success', 'Pull invoices successfully!');

        return redirect()->back();
    }

    public function send(Request $request, $id)
    {
        $order = Order::find($id);
        if (! empty($order)) {
            event(new ZatcaCreateInvoice($order->id));
        }

        $request->session()->flash('success', 'Invoice sent successfully!');

        return redirect()->route('orders.show', $order->id);
    }
}
