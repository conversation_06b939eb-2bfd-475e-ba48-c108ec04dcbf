<?php

namespace App\Http\Controllers\BankOffer;

use App\Http\Controllers\Controller;
use App\Http\Requests\BankOfferRequest;
use App\Models\BankOffer\BankOffer;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BankOffersController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:bank-offers.index')->only(['index']);
        $this->middleware('checkPermission:bank-offers.show')->only(['show']);
        $this->middleware('checkPermission:bank-offers.edit')->only(['edit']);
        $this->middleware('checkPermission:bank-offers.delete')->only(['destroy']);
        $this->middleware('checkPermission:bank-offers.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $bankOffers = BankOffer::Recent();
        if (isset($search) && $search != '') {
            $bankOffers = $bankOffers->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $bankOffers = $bankOffers->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $bankOffers = $bankOffers->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $bankOffers = $bankOffers->where('active', $active);
        }
        $bankOffers = $bankOffers->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('BankOffer/Index', [
            'data' => $bankOffers,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('BankOffer/Create');
    }

    public function store(BankOfferRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        BankOffer::create($data);
        $pageTitle = __('theBankOffer');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('bank-offers.index');
    }

    public function edit(BankOffer $bankOffer)
    {
        $data = ['bankOffer' => $bankOffer, 'title' => $bankOffer->title.' Edit'];

        return Inertia::render('BankOffer/Edit', ['data' => $data]);
    }

    public function show(BankOffer $bankOffer)
    {
        $data = ['bankOffer' => $bankOffer, 'title' => $bankOffer->title.' Info'];

        return Inertia::render('BankOffer/Show', ['data' => $data]);
    }

    public function update(BankOfferRequest $request, BankOffer $bankOffer)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $bankOffer->update($data);

        $pageTitle = __('theBankOffer');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('bank-offers.index');
    }

    public function destroy(Request $request, $id)
    {
        $bankOffer = BankOffer::find($id);
        $pageTitle = __('theBankOffer');
        $bankOffer->delete();

        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('bank-offers.index');
    }
}
