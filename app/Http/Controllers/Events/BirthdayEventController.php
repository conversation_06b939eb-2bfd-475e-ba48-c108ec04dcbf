<?php

namespace App\Http\Controllers\Events;

use App\Http\Controllers\Controller;
use App\Models\Event\BirthdayEvent;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BirthdayEventController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:birthday-events.index')->only(['index']);
        $this->middleware('checkPermission:birthday-events.show')->only(['show']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $items = BirthdayEvent::Recent();

        if (isset($search) && $search != '') {
            $items = $items->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('name', 'like', '%'.$search.'%');
                $q->orWhere('email', 'like', '%'.$search.'%');
                $q->orWhere('mobile', 'like', '%'.$search.'%');
                $q->orWhere('phone', 'like', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $items = $items->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $items = $items->whereDate('created_at', '<=', $toDate);
        }

        $items = $items->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('BirthdayEvent/Index', [
            'data' => $items,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function show(Request $request, $id)
    {
        $item = BirthdayEvent::where('id', $id)->first();

        return Inertia::render('BirthdayEvent/Show', [
            'data' => ['item' => $item],
        ]);
    }
}
