<?php

namespace App\Http\Controllers\Group;

use App\Http\Controllers\Controller;
use App\Http\Requests\Group\DynamicGroup\DynamicGroupRequest;
use App\Http\Requests\Group\DynamicGroup\UniqueDynamicGroupTypeRequest;
use App\Models\Group\Group;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DynamicGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:dynamic_groups.index')->only(['index']);
        $this->middleware('checkPermission:dynamic_groups.show')->only(['show']);
        $this->middleware('checkPermission:dynamic_groups.edit')->only(['edit']);
        $this->middleware('checkPermission:dynamic_groups.delete')->only(['destroy']);
        $this->middleware('checkPermission:dynamic_groups.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // Retrieve groups with type 'dynamic' and order by created_at in descending order
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $groups = Group::where('type', 'dynamic')->orderBy('created_at', 'desc');
        if (isset($search) && $search != '') {
            $groups = $groups->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $groups = $groups->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $groups = $groups->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $groups = $groups->where('active', $active);
        }
        $groups = $groups->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Groups/DynamicGroups/Index', [
            'data' => $groups,
            'groups' => $groups,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Groups/DynamicGroups/Create');
    }

    public function store(DynamicGroupRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['tier_name'] = $request->tier_name_en;
        $data['ar']['tier_name'] = $request->tier_name_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        Group::create($data);

        $pageTitle = __('TheGroup');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('dynamic_groups.index');
    }

    public function edit($id)
    {
        $group = Group::find($id);

        return Inertia::render('Groups/DynamicGroups/Edit', [
            'data' => ['group' => $group],
        ]);
    }

    public function update(DynamicGroupRequest $request, $id)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['tier_name'] = $request->tier_name_en;
        $data['ar']['tier_name'] = $request->tier_name_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $group = Group::find($id);

        $group->update($data);

        $pageTitle = __('TheGroup');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('dynamic_groups.index');
    }

    public function show(Request $request, $id)
    {
        $group = Group::find($id);
        $data = ['group' => $group, 'title' => $group->title];

        return Inertia::render('Groups/DynamicGroups/Show', ['data' => $data]);
    }

    public function destroy(Request $request, $id)
    {
        $pageTitle = __('TheGroup');
        $group = Group::withCount('users')
            ->where('id', $id)
            ->first();
        if ($group->users_count > 0) {
            //return with error message

            $request->session()->flash('error', __('GroupDeleteFailed', ['title' => $pageTitle]));

            return redirect()->route('dynamic_groups.index');
        }
        $group->users()->detach();
        if ($group->group_translations()) {
            $group->group_translations()->delete();
        }

        $group->delete();
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('dynamic_groups.index');
    }

    public function manageUser($id)
    {
        $group = Group::find($id);

        $group->users_ids = $group
            ->users()
            ->pluck('users.id')
            ->toArray();

        $data = ['group' => $group, 'title' => $group->title . ' Manage User'];

        return Inertia::render('Groups/DynamicGroups/Manage', [
            'data' => $data,
            'group_users' => $group
                ->users()
                ->paginate(5)
                ->withQueryString(),
        ]);
    }

    public function assignUser(UniqueDynamicGroupTypeRequest $request, $id)
    {
        $group = Group::find($id);
        $group->users()->sync($request->users);

        $pageTitle = __('TheGroup');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('dynamic_groups.index');
    }

    public function removeUser($group_id, $id)
    {
        $group = Group::find($group_id);
        $group->users()->detach($id);

        $group->users_ids = $group
            ->users()
            ->pluck('users.id')
            ->toArray();

        $data = ['group' => $group, 'title' => $group->title . ' Manage User'];

        return Inertia::render('Groups/DynamicGroups/Manage', [
            'data' => $data,
            'group_users' => $group
                ->users()
                ->paginate(5)
                ->withQueryString(),
        ]);
    }
}