<?php

namespace App\Http\Controllers\Group;

use App\Http\Controllers\Controller;
use App\Models\CustomGroup\CustomGroup;
use App\Models\CustomGroup\CustomGroupUser;
use Illuminate\Http\Request;

use Inertia\Inertia;

class CustomGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:custom-groups.index')->only(['index']);
        $this->middleware('checkPermission:custom-groups.show')->only(['show']);
        $this->middleware('checkPermission:custom-groups.edit')->only(['edit']);
        $this->middleware('checkPermission:custom-groups.delete')->only(['destroy']);
        $this->middleware('checkPermission:custom-groups.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // Retrieve groups with type 'fixed' and order by created_at in descending order
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $groups = CustomGroup::with('currentTranslation')->orderBy('created_at', 'desc');
        if (isset($search) && $search != '') {
            $groups = $groups->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $groups = $groups->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $groups = $groups->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $groups = $groups->where('active', $active);
        }
        $groups = $groups->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Groups/CustomGroups/Index', [
            'data' => $groups,
            'groups' => $groups,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function show(Request $request, $id)
    {
        $customGroup = CustomGroup::find($id);
        $data = [
            'group' => $customGroup,
            'title' => $customGroup->title,
            'en' => $customGroup->translate('en'),
            'ar' => $customGroup->translate('ar'),
        ];

        return Inertia::render('Groups/CustomGroups/Show', ['data' => $data]);
    }

    public function create()
    {
        return Inertia::render('Groups/CustomGroups/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'active' => 'required|in:0,1',
            'title_ar' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        if (empty($validated['active'])) {
            $validated['active'] = 0;
        }
        $customGroup = CustomGroup::create([
            'active' => (string) $validated['active'],
        ]);

        // Handle translations
        $customGroup->translateOrNew('ar')->title = $validated['title_ar'];
        $customGroup->translateOrNew('ar')->description = $validated['description_ar'];
        $customGroup->translateOrNew('en')->title = $validated['title_en'];
        $customGroup->translateOrNew('en')->description = $validated['description_en'];
        $customGroup->save();

        return redirect()
            ->route('custom-groups.index')
            ->with('success', __('Custom group created successfully'));
    }

    public function edit(CustomGroup $customGroup)
    {
        $data = [
            'data' => $customGroup,
            'en' => $customGroup->translate('en'),
            'ar' => $customGroup->translate('ar'),
        ];

        return Inertia::render('Groups/CustomGroups/Edit', ['data' => $data]);
    }

    public function update(Request $request, CustomGroup $customGroup)
    {
        $validated = $request->validate([
            'active' => 'required|in:0,1',
            'title_ar' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        $customGroup->update([
            'active' => $validated['active'],
        ]);

        // Handle translations
        $customGroup->translateOrNew('ar')->title = $validated['title_ar'];
        $customGroup->translateOrNew('ar')->description = $validated['description_ar'];
        $customGroup->translateOrNew('en')->title = $validated['title_en'];
        $customGroup->translateOrNew('en')->description = $validated['description_en'];
        $customGroup->save();

        return redirect()
            ->route('custom-groups.index')
            ->with('success', __('Custom group updated successfully'));
    }

    public function destroy(CustomGroup $customGroup)
    {
        $customGroup->delete();
        CustomGroupUser::where('custom_group_id', $customGroup->id)->delete();
        return redirect()
            ->route('custom-groups.index')
            ->with('success', __('Custom group deleted successfully'));
    }

    public function deleteUser($id)
    {
        $user = CustomGroupUser::findOrFail($id);
        $user->delete();

        return response()->json(['success' => true]);
    }

    public function addUser(Request $request, $id)
    {
        $request->validate([
            'emails.*' => 'required|email',
        ]);

        $addedEmails = [];
        $results = [];
        foreach ($request->emails as $email) {
            $user = CustomGroupUser::firstOrCreate([
                'custom_group_id' => $id,
                'email' => $email,
            ]);
            if ($user->wasRecentlyCreated) {
                $addedEmails[] = $email;
            }

            $results[] = [
                'email' => $email,
                'status' => $user->wasRecentlyCreated ? 'created' : 'exists',
            ];
        }

        $createdCount = collect($results)
            ->where('status', 'created')
            ->count();
        $existingCount = collect($results)
            ->where('status', 'exists')
            ->count();

        $message = '';
        if ($createdCount > 0) {
            $message .= __(':count emails added successfully. ', ['count' => $createdCount]);
        }
        if ($existingCount > 0) {
            $message .= __(':count emails already existed.', ['count' => $existingCount]);
        }

        return redirect()
            ->route('custom-groups.manage', $id)
            ->with($createdCount > 0 ? 'success' : 'error', $message)
            ->with('addedEmails', $addedEmails);
    }

    public function updateUser(Request $request, $id)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = CustomGroupUser::findOrFail($id);
        $userExists = CustomGroupUser::where('email', $request->email)
            ->where('custom_group_id', $user->custom_group_id)
            ->where('id', '!=', $id)
            ->first();
        if ($userExists) {
            return redirect()
                ->route('custom-groups.manage', $user->custom_group_id)
                ->with('error', __('User already exists'));
        }
        $user->update([
            'email' => $request->email,
        ]);

        return redirect()
            ->route('custom-groups.manage', $user->custom_group_id)
            ->with('success', __('User updated successfully'));
    }

    public function manageUser($id)
    {
        // Load data
        $group = CustomGroup::with('currentTranslation')->find($id);
        $groupUsers = CustomGroupUser::where('custom_group_id', $id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $data = [
            'group' => $group,
            'list_users' => $groupUsers,
            'title' => $group->title . ' Manage User',
        ];

        return Inertia::render('Groups/CustomGroups/Manage', [
            'data' => $data,
        ]);
    }
}