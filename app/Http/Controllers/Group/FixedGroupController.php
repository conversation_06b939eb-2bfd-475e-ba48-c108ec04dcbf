<?php

namespace App\Http\Controllers\Group;

use App\Exports\UsersExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Group\FixedGroup\FixedGroupRequest;
use App\Http\Requests\Group\FixedGroup\UniqueFixedGroupTypeRequest;
use App\Jobs\ExportEmailJob;
use App\Models\Group\Group;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FixedGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:fixed_groups.index')->only(['index']);
        $this->middleware('checkPermission:fixed_groups.show')->only(['show']);
        $this->middleware('checkPermission:fixed_groups.edit')->only(['edit']);
        $this->middleware('checkPermission:fixed_groups.delete')->only(['destroy']);
        $this->middleware('checkPermission:fixed_groups.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // Retrieve groups with type 'fixed' and order by created_at in descending order
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $groups = Group::where('type', 'fixed')->orderBy('created_at', 'desc');
        if (isset($search) && $search != '') {
            $groups = $groups->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $groups = $groups->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $groups = $groups->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $groups = $groups->where('active', $active);
        }
        $groups = $groups->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Groups/FixedGroups/Index', [
            'data' => $groups,
            'groups' => $groups,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Groups/FixedGroups/Create');
    }

    public function store(FixedGroupRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['type'] = 'fixed';

        $group = Group::create($data);

        $domain = $group->domain;
        $all_users = User::where('email', 'like', "%$domain%")->get();
        if ($all_users) {
            foreach ($all_users as $user) {
                $user->organizational_email = $user->email;
                $user->save();
            }
        }
        $users = User::where('expire_email_date', '>', now())
            ->where('organizational_email', 'like', "%$domain%")
            ->get();
        if ($users) {
            $group->users()->attach($users);
        }

        $pageTitle = __('TheGroup');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('fixed_groups.index');
    }

    public function edit($id)
    {
        $group = Group::find($id);

        return Inertia::render('Groups/FixedGroups/Edit', [
            'data' => ['group' => $group],
        ]);
    }

    public function update(FixedGroupRequest $request, $id)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $group = Group::find($id);

        $group->update($data);

        $pageTitle = __('TheGroup');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('fixed_groups.index');
    }

    public function show(Request $request, $id)
    {
        $group = Group::find($id);
        $data = ['group' => $group, 'title' => $group->title];

        return Inertia::render('Groups/FixedGroups/Show', ['data' => $data]);
    }

    public function destroy(Request $request, $id)
    {
        $pageTitle = __('TheGroup');

        $group = Group::withCount('users')
            ->where('id', $id)
            ->first();

        if ($group->users_count > 0) {
            //return with error message

            $request->session()->flash('error', __('GroupDeleteFailed', ['title' => $pageTitle]));

            return redirect()->route('fixed_groups.index');
        }
        $group->users()->detach();

        if ($group->group_translations()) {
            $group->group_translations()->delete();
        }

        $group->delete();

        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('fixed_groups.index');
    }

    public function manageUser($id)
    {
        $group = Group::find($id);

        $group->users_ids = $group
            ->users()
            ->pluck('users.id')
            ->toArray();

        $data = ['group' => $group, 'title' => $group->title.' Manage User'];

        return Inertia::render('Groups/FixedGroups/Manage', [
            'data' => $data,
            'group_users' => $group
                ->users()
                ->paginate(5)
                ->withQueryString(),
        ]);
    }

    public function assignUser(UniqueFixedGroupTypeRequest $request, $id)
    {
        $group = Group::find($id);

        $group->users()->sync($request->users);

        $pageTitle = __('TheGroup');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('fixed_groups.index');
    }

    public function removeUser($group_id, $id)
    {
        $group = Group::find($group_id);
        $group->users()->detach($id);

        $group->users_ids = $group
            ->users()
            ->pluck('users.id')
            ->toArray();

        $data = ['group' => $group, 'title' => $group->title.' Manage User'];

        return Inertia::render('Groups/FixedGroups/Manage', [
            'data' => $data,
            'group_users' => $group
                ->users()
                ->paginate(5)
                ->withQueryString(),
        ]);
    }

    public function filterUser($id, $search)
    {
        $group = Group::find($id);
        $users = $group->users();

        if (isset($search) && $search != '') {
            $users = $users->where(function ($query) use ($search) {
                $query
                    ->where('email', 'like', "%$search%")
                    ->orWhere('first_name', 'like', "%$search%")
                    ->orWhere('organizational_email', 'like', "%$search%");
            });
        }

        $usersData = $users->paginate(5)->withQueryString();
        $userIds = $users->pluck('id'); // Extracting user ids

        return response()->json([
            'users' => $usersData,
            'userIds' => $userIds,
        ]);
    }

    public function export(Request $request)
    {
        $filename = 'exports/'.time().'-users.csv';

        (new UsersExport($request->all()))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])
            ->chain([new ExportEmailJob($filename, 'Export/users.csv')])
            ->onQueue('export-users');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }
}
