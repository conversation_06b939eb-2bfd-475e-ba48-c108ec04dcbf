<?php

namespace App\Http\Controllers\Promotions;

use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\BundleRequest;
use App\Models\Brand\Brand;
use App\Models\Permission\RoleBranch;
use App\Models\Promotion\Bundle\Bundle;
use App\Models\Promotion\Bundle\BundleAddon;
use App\Models\Promotion\Bundle\BundleTicket;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Promotion\Bundle\BundleTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class BundlesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:bundles.index')->only(['index']);
        $this->middleware('checkPermission:bundles.show')->only(['show']);
        $this->middleware('checkPermission:bundles.edit')->only(['edit']);
        $this->middleware('checkPermission:bundles.delete')->only(['destroy']);
        $this->middleware('checkPermission:bundles.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        if (Cookie::get('role_brands')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $bundles = Bundle::Recent()->with('tickets');

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $bundles = $bundles->where('brand_id', Cookie::get('brandId'));
        }

        if (count($brand_ids) > 0) {
            $bundles = $bundles->whereIn('brand_id', $brand_ids);
        }

        if (isset($search) && $search != '') {
            $bundles = $bundles->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $bundles = $bundles->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $bundles = $bundles->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $bundles = $bundles->where('active', $active);
        }
        if (isset($type) && $type != '') {
            $bundles = $bundles->where('type', $type);
        }

        $bundles = $bundles->paginate(15);
        foreach ($bundles as $bundle) {
            $bundle->active =
                date('Y-m-d') < $bundle->started_at || date('Y-m-d') > $bundle->expired_at ? 0 : $bundle->active;
            $bundle->save();
        }

        return Inertia::render('Promotion/Bundles/Index', [
            'data' => $bundles->withQueryString(),
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $brands = Brand::Recent()->get();

        return Inertia::render('Promotion/Bundles/Create', [
            'brands' => $brands,
        ]);
    }

    public function store(BundleRequest $request)
    {
        $data = $request->all();
        $validateTitleEn = BundleTranslation::where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('bundle', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();
        $validateTitleAr = BundleTranslation::where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('bundle', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();

        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $data['brand_id'] = $data['brand_id'] == '' ? null : $data['brand_id'];
        $folder = $data['brand_id'] ?? 'home';

        $tickets = $request->tickets;
        $products = $request->products;
        DB::beginTransaction();

        try {
            $bundle = Bundle::create($data);
            foreach ($tickets as $ticket) {
                $bundleTicket = [];
                $bundleTicket['ticket_id'] = $ticket['id'];
                $bundleTicket['count'] = $ticket['count'];
                $bundleTicket['bundle_id'] = $bundle->id;
                $bundleTicket['en']['title'] = $ticket['title_en'];
                $bundleTicket['ar']['title'] = $ticket['title_ar'];
                $bundleTicket['en']['sub_title'] = $ticket['sub_title_en'];
                $bundleTicket['ar']['sub_title'] = $ticket['sub_title_ar'];
                $bundleTicket['en']['description'] = $ticket['description_en'];
                $bundleTicket['ar']['description'] = $ticket['description_ar'];
                BundleTicket::create($bundleTicket);
            }

            foreach ($products as $product) {
                $bundleAddon = [];
                $bundleAddon['foodics_product_id'] = $product['id'];
                $bundleAddon['bundle_id'] = $bundle->id;
                $bundleAddon['count'] = $product['count'];
                $bundleAddon['en']['title'] = $product['name'];
                $bundleAddon['ar']['title'] = $product['name_localized'];
                $bundleAddon['en']['sub_title'] = $product['description'];
                $bundleAddon['ar']['sub_title'] = $product['description_localized'];
                $bundleAddon['image'] = $product['image'];
                $bundleAddon['price'] = $product['price'];
                BundleAddon::create($bundleAddon);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        $pageTitle = __('theBundle');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));
        return redirect()->route('bundles.index');
    }

    public function edit(Bundle $bundle)
    {
        $tickets = $bundle->tickets()->get();
        foreach ($tickets as $bundleTicket) {
            $ticket = Ticket::find($bundleTicket['ticket_id']);
            if (!$ticket) {
                $bundleTicket['available'] = 0;
                $bundleTicket->save();
            }
        }
        $products = $bundle->products()->get();
        $data = [
            'bundle' => $bundle,
            'tickets' => $tickets,
            'products' => $products,
            'title' => $bundle->title . ' Edit',
        ];

        return Inertia::render('Promotion/Bundles/Edit', [
            'data' => $data,
            'brands' => Brand::Recent()->get(),
        ]);
    }

    public function show(Bundle $bundle)
    {
        $tickets = $bundle->tickets()->get();

        foreach ($tickets as $bundleTicket) {
            $ticket = Ticket::find($bundleTicket['ticket_id']);
            if (!$ticket) {
                $bundleTicket['available'] = 0;
                $bundleTicket->save();
            }
        }

        $products = $bundle->products()->get();

        $data = [
            'item' => $bundle,
            'title' => $bundle->title . ' Info',
            'tickets' => $tickets,
            'products' => $products,
        ];

        return Inertia::render('Promotion/Bundles/Show', [
            'data' => $data,
        ]);
    }

    public function update(BundleRequest $request, $id)
    {
        $bundle = Bundle::where('id', $id)->first();
        $data = $request->all();

        $validateTitleEn = BundleTranslation::where('bundle_id', '!=', $bundle->id)
            ->where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('bundle', function ($q) use ($bundle) {
                $q->where('brand_id', $bundle->brand_id);
            })
            ->count();
        $validateTitleAr = BundleTranslation::where('bundle_id', '!=', $bundle->id)
            ->where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('bundle', function ($q) use ($bundle) {
                $q->where('brand_id', $bundle->brand_id);
            })
            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $data['brand_id'] = $data['brand_id'] == '' ? null : $data['brand_id'];
        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'bundles/' . $folder . '/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar . '.' . explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar . '.png';
            $data['ar']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'bundles/' . $folder . '/en/';
            $name = $request->content_type_en
                ? $request->uuid_en . '.' . explode('/', $request->content_type_en)[1]
                : $request->uuid_en . '.png';
            $data['en']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }

        try {
            DB::beginTransaction();
            $bundle->update($data);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        $pageTitle = __('theBundle');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));
        return redirect()->route('bundles.index');
    }

    public function destroy(Request $request, $id)
    {
        Bundle::find($id)->delete();
        $pageTitle = __('theBundle');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('bundles.index');
    }

    public function filterByBrand(Request $request, $id)
    {
        $search = $request->search;
        return Bundle::Recent()
            ->where('brand_id', $id)
            ->where(function ($q) use ($search) {
                if ($search != '') {
                    $q->where('id', 'like', '%' . $search . '%');
                    $q->orWhereTranslationLike('title', '%' . $search . '%');
                }
            })
            ->take(10)
            ->get();
    }
}