<?php

namespace App\Http\Controllers\Promotions;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\BankOfferCouponRequest;
use App\Models\BankOffer\BankOffer;
use App\Models\Ordering\Order;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Promotion\Coupon\CouponTranslation;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BankOfferCouponsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:bank-offer-coupons.index')->only(['index']);
        $this->middleware('checkPermission:bank-offer-coupons.show')->only(['show']);
        $this->middleware('checkPermission:bank-offer-coupons.edit')->only(['edit']);
        $this->middleware('checkPermission:bank-offer-coupons.delete')->only(['destroy']);
        $this->middleware('checkPermission:bank-offer-coupons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = Coupon::BankOffer()->Recent();

        if (isset($search) && $search != '') {
            $coupons = $coupons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('code', 'like', '%'.$search.'%');
                $q->orWhere('discount', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->where(function ($q) use ($active) {
                if ($active == 1) {
                    $q->where('active', $active);
                    $q->whereDate('expired_at', '>=', now());
                    $q->whereDate('started_at', '<=', now());
                } else {
                    $q->where('active', $active);
                    $q->orWhere(function ($q1) {
                        $q1->whereDate('expired_at', '<', now());
                        $q1->whereDate('started_at', '<', now());
                    });
                }
            });
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->where('type', $type);
        }

        $coupons = $coupons->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Promotion/Coupons/BankOffer/Index', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $bank_offers = BankOffer::Recent()->get();

        return Inertia::render('Promotion/Coupons/BankOffer/Create', [
            'bankOffers' => $bank_offers,
            'data' => [
                'selectedDates' => [],
                'begin' => date('Y-m-d'),
                'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
            ],
        ]);
    }

    public function store(BankOfferCouponRequest $request)
    {
        $data = $request->all();
        $data['coupon_type'] = Constant::CouponBankOfferType;
        $data['is_bulk'] = 0;
        if (isset($data['brand_id'])) {
            $validateTitleEn = CouponTranslation::where('title', $request->title_en)
                ->where('locale', 'en')
                ->whereHas('coupon', function ($q) use ($data) {
                    $q->where('brand_id', $data['brand_id']);
                })
                ->count();

            $validateTitleAr = CouponTranslation::where('title', $request->title_ar)
                ->where('locale', 'ar')
                ->whereHas('coupon', function ($q) use ($data) {
                    $q->where('brand_id', $data['brand_id']);
                })
                ->count();

            $validateCode = Coupon::where('code', $request->code)
                ->where('brand_id', $data['brand_id'])
                ->count();

            if ($validateCode > 0) {
                $existCodes = Coupon::where('code', $request->code)
                    ->where('brand_id', $data['brand_id'])
                    ->pluck('code')
                    ->toArray();

                return redirect()
                    ->back()
                    ->withErrors(['code' => ['The '.implode('-', $existCodes).' has already been taken.']]);
            }
            if ($validateTitleEn > 0) {
                return redirect()
                    ->back()
                    ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
            }

            if ($validateTitleAr > 0) {
                return redirect()
                    ->back()
                    ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
            }
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        Coupon::create($data);
        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('bank-offer-coupons.index');
    }

    public function edit(Coupon $bank_offer_coupon)
    {
        $bank_offers = BankOffer::Recent()->get();
        $data = [
            'coupon' => $bank_offer_coupon,
            'title' => $bank_offer_coupon->title.' Edit',
            'begin' => date('Y-m-d'),
            'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
        ];

        return Inertia::render('Promotion/Coupons/BankOffer/Edit', [
            'data' => $data,
            'bankOffers' => $bank_offers,
        ]);
    }

    public function show($id)
    {
        $item = Coupon::with('brand', 'bank_offer')
            ->where('id', $id)
            ->first();
        $usage = Order::where('coupon_id', $item->id)->count();
        $data = ['item' => $item, 'usage' => $usage, 'title' => $item->title.' Info'];

        return Inertia::render('Promotion/Coupons/BankOffer/Show', [
            'data' => $data,
        ]);
    }

    public function update(BankOfferCouponRequest $request, Coupon $bank_offer_coupon)
    {
        $data = $request->all();

        $bank_offer_coupon->update($data);

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('bank-offer-coupons.index');
    }

    public function destroy(Request $request, $id)
    {
        Coupon::find($id)->delete();

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('bank-offer-coupons.index');
    }
}
