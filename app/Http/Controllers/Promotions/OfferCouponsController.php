<?php

namespace App\Http\Controllers\Promotions;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\CouponRequest;
use App\Http\Requests\Promotion\OfferCouponRequest;
use App\Models\Brand\Brand;
use App\Models\Permission\RoleBranch;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Promotion\Coupon\CouponBrand;
use App\Models\Promotion\Coupon\CouponDate;
use App\Models\Promotion\Coupon\CouponTranslation;
use App\Models\Promotion\Coupon\ToCouponBrand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;
use Inertia\Inertia;

class OfferCouponsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:coupons.index')->only(['index']);
        $this->middleware('checkPermission:coupons.show')->only(['show']);
        $this->middleware('checkPermission:coupons.edit')->only(['edit']);
        $this->middleware('checkPermission:coupons.delete')->only(['destroy']);
        $this->middleware('checkPermission:coupons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        // if (Cookie::get('role_brands')) {
        //     $brand_ids = unserialize(Cookie::get('role_brands'));
        //     $ids = Auth()
        //         ->user()
        //         ->roles->pluck('id')
        //         ->toArray();
        //     $branch_ids = RoleBranch::whereIn('role_id', $ids)
        //         ->pluck('branch_id')
        //         ->toArray();
        // }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = Coupon::Offer()
            ->NotUser()
            ->Recent();

        // if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
        //     $coupons = $coupons->where('brand_id', Cookie::get('brandId'));
        // }

        // if (count($brand_ids) > 0) {
        //     $coupons = $coupons->whereIn('brand_id', $brand_ids);
        // }

        if (isset($search) && $search != '') {
            $coupons = $coupons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('code', 'like', '%'.$search.'%');
                $q->orWhere('discount', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->where(function ($q) use ($active) {
                if ($active == 1) {
                    $q->where('active', $active);
                    $q->whereDate('expired_at', '>=', now());
                    $q->whereDate('started_at', '<=', now());
                } else {
                    $q->where('active', $active);
                    $q->orWhere(function ($q1) {
                        $q1->whereDate('expired_at', '<', now());
                        $q1->whereDate('started_at', '<', now());
                    });
                }
            });
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->where('type', $type);
        }

        $coupons = $coupons->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Promotion/Coupons/Offer/Index', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $brands = Brand::Recent()->get();

        return Inertia::render('Promotion/Coupons/Offer/Create', [
            'brands' => $brands,
        ]);
    }

    public function store(CouponRequest $request)
    {
        $data = $request->all();
        $validateTitleEn = CouponTranslation::where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('coupon', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();
        $validateTitleAr = CouponTranslation::where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('coupon', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();

        $validateCode = Coupon::where('code', $request->code)
            ->where('brand_id', $data['brand_id'])
            ->count();

        if ($validateCode > 0) {
            return redirect()
                ->back()
                ->withErrors(['code' => ['The code has already been taken.']]);
        }
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        Coupon::create($data);

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('offer-coupons.index');
    }

    public function unique_str()
    {
        $uniqueStr = '';
        $uniqueStr = Str::random(5);
        while (Coupon::where('code', $uniqueStr)->exists()) {
            $uniqueStr = Str::random(5);
        }

        return $uniqueStr;
    }

    public function edit(Coupon $offer_coupon)
    {
        $brandCouponItems = [];
        $items = CouponBrand::where('coupon_id', $offer_coupon->id)->get();
        if (! empty($items)) {
            foreach ($items as $item) {
                $to_brand_id = ToCouponBrand::where('coupon_brand_id', $item->id)
                    ->pluck('brand_id')
                    ->toArray();
                $setting = ToCouponBrand::where('coupon_brand_id', $item->id)->first();
                array_push($brandCouponItems, [
                    'id' => $item->id,
                    'from_brand_id' => $item->brand_id,
                    'to_brand_id' => $to_brand_id,
                    'max_value_discount' => $setting->max_value_discount ?? '',
                    'total_order_amount' => $setting->total_order_amount ?? '',
                    'condition' => $setting->condition ?? '',
                    'type' => $setting->type ?? '',
                    'discount' => $setting->discount ?? '',
                ]);
            }
        }

        $dates = CouponDate::where('coupon_id', $offer_coupon->id)
            ->pluck('day_id')
            ->toArray();

        $days = Constant::Days;

        $data = [
            'coupon' => $offer_coupon,
            'title' => $offer_coupon->title.' Edit',
            'disabledDates' => [],
            'begin' => date('Y-m-d'),
            'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
            'dates' => $dates,
            'days' => $days,
        ];

        return Inertia::render('Promotion/Coupons/Offer/Edit', [
            'data' => $data,
            'brandCouponItems' => $brandCouponItems,
        ]);
    }

    public function show(Coupon $offer_coupon)
    {
        //brands
        $locale = app()->getLocale();
        $returnDays = [];
        $brandCouponItems = [];
        $items = CouponBrand::where('coupon_id', $offer_coupon->id)->get();
        $allDays = Constant::Days;
        $days = CouponDate::where('coupon_id', $offer_coupon->id)
            ->whereNotNull('day_id')
            ->pluck('day_id')
            ->toArray();

        foreach ($allDays as $array) {
            if (in_array($array['id'], $days)) {
                array_push($returnDays, $array['title_'.$locale]);
            }
        }

        //get day text by title
        if (! empty($items)) {
            foreach ($items as $item) {
                $to_brand_id = ToCouponBrand::where('coupon_brand_id', $item->id)
                    ->pluck('brand_id')
                    ->toArray();
                $to_brand = ToCouponBrand::where('coupon_brand_id', $item->id)
                    ->orderBy('created_at', 'DESC')
                    ->first();

                $to_brand_id = Brand::whereIn('id', $to_brand_id)
                    ->get()
                    ->pluck('title_'.$locale)
                    ->toArray();
                $to_brand_id = implode(' , ', $to_brand_id);
                $brand = Brand::where('id', $item->brand_id)->first();
                if (! empty($brand)) {
                    array_push($brandCouponItems, [
                        'from_brand' => ! empty($brand) ? $brand->{'title_'.$locale} : '',
                        'to_brand' => $to_brand_id,
                        'max_value_discount' => $to_brand->max_value_discount,
                        'total_order_amount' => $to_brand->total_order_amount,
                        'condition' => $to_brand->condition,
                        'type' => $to_brand->type,
                        'discount' => $to_brand->discount,
                        'type_span' => $to_brand->type_span,

                    ]);
                }
            }
        }
        $data = ['item' => $offer_coupon, 'days' => $returnDays, 'brands' => $brandCouponItems, 'title' => $offer_coupon->title.' Info'];

        return Inertia::render('Promotion/Coupons/Offer/Show', [
            'data' => $data,
        ]);
    }

    public function update(OfferCouponRequest $request, $id)
    {
        $coupon = Coupon::where('id', $id)->first();
        $data = $request->all();

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $coupon->update($data);

        if ($coupon->coupon_type == Constant::CouponOrderType) {
            CouponBrand::where('coupon_id', $id)->delete();
            ToCouponBrand::where('coupon_id', $id)->delete();
            if (! empty($request->brandCouponItems) && count($request->brandCouponItems) > 0) {
                foreach ($request->brandCouponItems as $brandCouponItem) {
                    $coupon_brand = CouponBrand::create([
                        'brand_id' => $brandCouponItem['from_brand_id'],
                        'coupon_id' => $id,
                    ]);

                    if (! empty($brandCouponItem['to_brand_id'])) {
                        foreach ($brandCouponItem['to_brand_id'] as $to_brand) {
                            $to_coupon_brand = ToCouponBrand::create([
                                'brand_id' => $to_brand,
                                'coupon_brand_id' => $coupon_brand->id,
                                'coupon_id' => $id,
                                'max_value_discount' => $brandCouponItem['max_value_discount'],
                                'total_order_amount' => $brandCouponItem['total_order_amount'],
                                'condition' => $brandCouponItem['condition'],
                                'type' => $brandCouponItem['type'],
                                'discount' => $brandCouponItem['discount'],
                            ]);
                        }
                    }
                }
            } else {
                $coupon->update(['active' => 0]);
            }
            foreach ($data['dates'] as $day_id) {
                CouponDate::updateOrCreate(
                    [
                        'day_id' => $day_id,
                        'coupon_id' => $id,
                    ],
                    [
                        'day_id' => $day_id,
                        'coupon_id' => $id,
                    ]
                );
            }
        }

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('offer-coupons.index');
    }

    public function destroy(Request $request, $id)
    {
        Coupon::find($id)->delete();

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('offer-coupons.index');
    }

    public function filterByBrand(Request $request, $id)
    {
        $search = $request->search;

        return Coupon::Recent()
            ->where('brand_id', $id)
            ->where(function ($q) use ($search) {
                if ($search != '') {
                    $q->where('id', 'like', '%'.$search.'%');
                    $q->orWhere('code', 'like', '%'.$search.'%');
                    $q->orWhere('discount', 'like', '%'.$search.'%');
                    $q->orWhereTranslationLike('title', '%'.$search.'%');
                }
            })
            ->take(10)
            ->get();
    }

    public function list_user_coupons(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        // if (Cookie::get('role_brands')) {
        //     $brand_ids = unserialize(Cookie::get('role_brands'));
        //     $ids = Auth()
        //         ->user()
        //         ->roles->pluck('id')
        //         ->toArray();
        //     $branch_ids = RoleBranch::whereIn('role_id', $ids)
        //         ->pluck('branch_id')
        //         ->toArray();
        // }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = Coupon::with('user')
            ->UserType()
            ->Recent();

        // if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
        //     $coupons = $coupons->where('brand_id', Cookie::get('brandId'));
        // }

        if (count($brand_ids) > 0) {
            $coupons = $coupons->whereIn('brand_id', $brand_ids);
        }

        if (isset($search) && $search != '') {
            $coupons = $coupons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('code', 'like', '%'.$search.'%');
                $q->orWhere('discount', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->where(function ($q) use ($active) {
                if ($active == 1) {
                    $q->where('active', $active);
                    $q->whereDate('expired_at', '>=', now());
                    $q->whereDate('started_at', '<=', now());
                } else {
                    $q->where('active', $active);
                    $q->orWhere(function ($q1) {
                        $q1->whereDate('expired_at', '<', now());
                        $q1->whereDate('started_at', '<', now());
                    });
                }
            });
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->where('type', $type);
        }

        $coupons = $coupons->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Promotion/Coupons/Offer/Users', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }
}
