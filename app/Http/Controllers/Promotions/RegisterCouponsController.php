<?php

namespace App\Http\Controllers\Promotions;

use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\RegisterCouponRequest;
use App\Models\Brand\Brand;
use App\Models\Promotion\Coupon\Coupon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class RegisterCouponsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:coupons.index')->only(['index']);
        $this->middleware('checkPermission:coupons.show')->only(['show']);
        $this->middleware('checkPermission:coupons.edit')->only(['edit']);
        $this->middleware('checkPermission:coupons.delete')->only(['destroy']);
        $this->middleware('checkPermission:coupons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = Coupon::Register()->Recent();

        if (isset($search) && $search != '') {
            $coupons = $coupons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhere('code', 'like', '%' . $search . '%');
                $q->orWhere('discount', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->where('active', $active);
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->where('type', $type);
        }

        $coupons = $coupons->paginate(15)->withQueryString();

        return Inertia::render('Promotion/Coupons/Register/Index', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        return Inertia::render('Promotion/Coupons/Register/Create');
    }

    public function store(RegisterCouponRequest $request)
    {
        $data = $request->all();

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['brand_id'] = null;
        $data['maximum_usage'] = 1;
        $data['coupon_type'] = 1; //register
        $data['is_bulk'] = 0;

        $folder = 'register';
        // if ($request->key_ar) {
        //     $path = 'coupons/' . $folder . '/ar/';
        //     $name = $request->content_type_ar
        //         ? $request->uuid_ar . '.' . explode('/', $request->content_type_ar)[1]
        //         : $request->uuid_ar . '.png';
        //     $data['ar']['image'] = $path . $name;
        //     Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        // }

        // if ($request->key_en) {
        //     $path = 'coupons/' . $folder . '/en/';
        //     $name = $request->content_type_en
        //         ? $request->uuid_en . '.' . explode('/', $request->content_type_en)[1]
        //         : $request->uuid_en . '.png';
        //     $data['en']['image'] = $path . $name;
        //     Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        // }

        Coupon::create($data);

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('register-coupons.index');
    }

    public function unique_str()
    {
        $uniqueStr = '';
        $uniqueStr = Str::random(5);
        while (Coupon::where('code', $uniqueStr)->exists()) {
            $uniqueStr = Str::random(5);
        }

        return $uniqueStr;
    }

    public function edit($id)
    {
        $coupon = Coupon::where('id', $id)->first();
        $data = ['coupon' => $coupon, 'title' => $coupon->title . ' Edit'];
        return Inertia::render('Promotion/Coupons/Register/Edit', [
            'data' => $data,
            'brands' => Brand::Recent()->get(),
        ]);
    }

    public function show($id)
    {
        $coupon = Coupon::where('id', $id)->first();

        $data = ['item' => $coupon, 'title' => $coupon->title . ' Info'];

        return Inertia::render('Promotion/Coupons/Register/Show', [
            'data' => $data,
        ]);
    }

    public function update(RegisterCouponRequest $request, $id)
    {
        $coupon = Coupon::where('id', $id)->first();
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $folder = 'register';
        if ($request->key_ar) {
            $path = 'coupons/' . $folder . '/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar . '.' . explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar . '.png';
            $data['ar']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'coupons/' . $folder . '/en/';
            $name = $request->content_type_en
                ? $request->uuid_en . '.' . explode('/', $request->content_type_en)[1]
                : $request->uuid_en . '.png';
            $data['en']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }
        $coupon->update($data);

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));
        return redirect()->route('register-coupons.index');
    }

    public function destroy(Request $request, $id)
    {
        Coupon::find($id)->delete();

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));
        return redirect()->route('register-coupons.index');
    }
}