<?php

namespace App\Http\Controllers\Promotions;

use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\CouponRequest;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Permission\RoleBranch;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Promotion\Coupon\CouponBranch;
use App\Models\Promotion\Coupon\CouponDate;
use App\Models\Promotion\Coupon\CouponTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class CouponsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:coupons.index')->only(['index']);
        $this->middleware('checkPermission:coupons.show')->only(['show']);
        $this->middleware('checkPermission:coupons.edit')->only(['edit']);
        $this->middleware('checkPermission:coupons.delete')->only(['destroy']);
        $this->middleware('checkPermission:coupons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        if (Cookie::get('role_brands')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = Coupon::Single()->Recent();

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $coupons = $coupons->where(function ($q) {
                $q->where('brand_id', Cookie::get('brandId'));
                $q->orWhereNull('brand_id');
            });
        }

        if (count($brand_ids) > 0) {
            $coupons = $coupons->where(function ($q) use ($brand_ids) {
                $q->whereIn('brand_id', $brand_ids);
                $q->orWhereNull('brand_id');
            });
        }

        if (isset($search) && $search != '') {
            $coupons = $coupons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('code', 'like', '%'.$search.'%');
                $q->orWhere('discount', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->where(function ($q) use ($active) {
                if ($active == 1) {
                    $q->where('active', $active);
                    $q->whereDate('expired_at', '>=', now());
                    $q->whereDate('started_at', '<=', now());
                } else {
                    $q->where('active', $active);
                    $q->orWhere(function ($q1) {
                        $q1->whereDate('expired_at', '<', now());
                        $q1->whereDate('started_at', '<', now());
                    });
                }
            });
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->where('type', $type);
        }

        $coupons = $coupons->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Promotion/Coupons/Index', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $brands = Brand::Recent()->get();
        $branches = Branch::where('brand_id', Cookie::get('brandId'))
            ->Recent()
            ->get();

        return Inertia::render('Promotion/Coupons/Create', [
            'branches' => $branches,
            'brands' => $brands,
            'data' => [
                'selectedDates' => [],
                'begin' => date('Y-m-d'),
                'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
            ],
        ]);
    }

    public function store(CouponRequest $request)
    {
        $data = $request->all();
        $validateTitleEn = CouponTranslation::where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('coupon', function ($q) use ($data) {
                if (isset($data['brand_id'])) {
                    $q->where('brand_id', $data['brand_id']);
                }
            })
            ->count();
        $validateTitleAr = CouponTranslation::where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('coupon', function ($q) use ($data) {
                if (isset($data['brand_id'])) {
                    $q->where('brand_id', $data['brand_id']);
                }
            })
            ->count();

        $validateCode = Coupon::where('code', $request->code)
            ->where('brand_id', $data['brand_id'])
            ->count();

        if ($validateCode > 0) {
            return redirect()
                ->back()
                ->withErrors(['code' => ['The code has already been taken.']]);
        }
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'coupons/'.$folder.'/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar.'.'.explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar.'.png';
            $data['ar']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'coupons/'.$folder.'/en/';
            $name = $request->content_type_en
                ? $request->uuid_en.'.'.explode('/', $request->content_type_en)[1]
                : $request->uuid_en.'.png';
            $data['en']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }

        $coupon = Coupon::create($data);

        foreach ($data['dates'] as $date) {
            CouponDate::updateOrCreate(
                [
                    'date' => $date,
                    'coupon_id' => $coupon->id,
                ],
                [
                    'date' => $date,
                    'coupon_id' => $coupon->id,
                ]
            );
        }

        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                CouponBranch::create([
                    'branch_id' => $row,
                    'coupon_id' => $coupon->id,
                    'brand_id' => $coupon->brand_id,
                ]);
            }
        }

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('coupons.index');
    }

    public function unique_str()
    {
        $uniqueStr = '';
        $uniqueStr = Str::random(5);
        while (Coupon::where('code', $uniqueStr)->exists()) {
            $uniqueStr = Str::random(5);
        }

        return $uniqueStr;
    }

    public function edit(Coupon $coupon)
    {
        $selectedDates = CouponDate::where('coupon_id', $coupon->id)
            ->pluck('date')
            ->toArray();
        $branches = Branch::where('brand_id', $coupon->brand_id)
            ->Recent()
            ->get();
        $data = [
            'coupon' => $coupon,
            'title' => $coupon->title.' Edit',
            'selectedDates' => $selectedDates,
            'begin' => date('Y-m-d'),
            'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
        ];

        return Inertia::render('Promotion/Coupons/Edit', [
            'data' => $data,
            'branchIds' => $coupon->branch_ids,
            'branches' => $branches,
            'brands' => Brand::Recent()->get(),
        ]);
    }

    public function show($id)
    {
        $coupon = Coupon::with('brand', 'branches')
            ->where('id', $id)
            ->first();
        $data = ['item' => $coupon, 'title' => $coupon->title.' Info'];

        return Inertia::render('Promotion/Coupons/Show', [
            'data' => $data,
        ]);
    }

    public function update(CouponRequest $request, $id)
    {
        $coupon = Coupon::where('id', $id)->first();
        $data = $request->all();
        $validateTitleEn = CouponTranslation::where('coupon_id', '!=', $coupon->id)
            ->where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('coupon', function ($q) use ($coupon) {
                if (isset($coupon->brand_id)) {
                    $q->where('brand_id', $coupon->brand_id);
                }
                // $q->where('brand_id', $coupon->brand_id);
            })
            ->count();
        $validateTitleAr = CouponTranslation::where('coupon_id', '!=', $coupon->id)
            ->where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('coupon', function ($q) use ($coupon) {
                if (isset($coupon->brand_id)) {
                    $q->where('brand_id', $coupon->brand_id);
                }
            })
            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }

        $validateCode = Coupon::where('code', $request->code)
            ->where('id', '!=', $coupon->id)
            ->where('brand_id', $data['brand_id'])
            ->count();

        if ($validateCode > 0) {
            return redirect()
                ->back()
                ->withErrors(['code' => ['The english code has already been taken.']]);
        }
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $data['brand_id'] = $data['brand_id'] == '' ? null : $data['brand_id'];
        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'coupons/'.$folder.'/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar.'.'.explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar.'.png';
            $data['ar']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'coupons/'.$folder.'/en/';
            $name = $request->content_type_en
                ? $request->uuid_en.'.'.explode('/', $request->content_type_en)[1]
                : $request->uuid_en.'.png';
            $data['en']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }

        $coupon->update($data);

        CouponDate::whereNotIn('date', $data['dates'])
            ->where('coupon_id', $id)
            ->delete();

        foreach ($data['dates'] as $date) {
            CouponDate::updateOrCreate(
                [
                    'date' => $date,
                    'coupon_id' => $id,
                ],
                [
                    'date' => $date,
                    'coupon_id' => $id,
                ]
            );
        }

        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                CouponBranch::updateOrCreate(
                    [
                        'branch_id' => $row,
                        'coupon_id' => $coupon->id,
                        'brand_id' => $coupon->brand_id,
                    ],
                    [
                        'branch_id' => $row,
                        'coupon_id' => $coupon->id,
                        'brand_id' => $coupon->brand_id,
                    ]
                );

                CouponBranch::whereNotIn('branch_id', $data['branch_id'])
                    ->where('coupon_id', $coupon->id)
                    ->delete();
            }
        }

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('coupons.index');
    }

    public function destroy(Request $request, $id)
    {
        Coupon::find($id)->delete();

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('coupons.index');
    }

    public function filterByBrand(Request $request, $id)
    {
        $search = $request->search;

        return Coupon::Recent()
            ->where('brand_id', $id)
            ->where(function ($q) use ($search) {
                if ($search != '') {
                    $q->where('id', 'like', '%'.$search.'%');
                    $q->orWhere('code', 'like', '%'.$search.'%');
                    $q->orWhere('discount', 'like', '%'.$search.'%');
                    $q->orWhereTranslationLike('title', '%'.$search.'%');
                }
            })
            ->take(10)
            ->get();
    }
}
