<?php

namespace App\Http\Controllers\Promotions;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\GroupCouponRequest;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\CustomGroup\CustomGroup;
use App\Models\Group\Group;
use App\Models\Ordering\Order;
use App\Models\Permission\RoleBranch;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Promotion\Coupon\CouponBranch;
use App\Models\Promotion\Coupon\CouponTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class GroupCouponsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:group-coupons.index')->only(['index']);
        $this->middleware('checkPermission:group-coupons.show')->only(['show']);
        $this->middleware('checkPermission:group-coupons.edit')->only(['edit']);
        $this->middleware('checkPermission:group-coupons.delete')->only(['destroy']);
        $this->middleware('checkPermission:group-coupons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        if (Cookie::get('role_brands')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = Coupon::where(function ($q) {
            $q->where('coupon_type', Constant::GroupCouponType)->orWhere(
                'coupon_type',
                Constant::CustomGroupCouponType
            );
        })->Recent();

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $coupons = $coupons->where('brand_id', Cookie::get('brandId'));
        }

        if (count($brand_ids) > 0) {
            $coupons = $coupons->whereIn('brand_id', $brand_ids);
        }

        if (isset($search) && $search != '') {
            $coupons = $coupons->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('code', 'like', '%'.$search.'%');
                $q->orWhere('discount', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->where(function ($q) use ($active) {
                if ($active == 1) {
                    $q->where('active', $active);
                    $q->whereDate('expired_at', '>=', now());
                    $q->whereDate('started_at', '<=', now());
                } else {
                    $q->where('active', $active);
                    $q->orWhere(function ($q1) {
                        $q1->whereDate('expired_at', '<', now());
                        $q1->whereDate('started_at', '<', now());
                    });
                }
            });
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->where('type', $type);
        }

        $coupons = $coupons->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Promotion/Coupons/Group/Index', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $brands = Brand::Recent()->get();
        $groups = Group::Recent()
            ->where('active', 1)
            ->take(30)
            ->get();

        $custom_groups = CustomGroup::Recent()
            ->active()
            ->take(30)
            ->get();
        $branches = Branch::where('brand_id', Cookie::get('brandId'))
            ->Recent()
            ->get();

        return Inertia::render('Promotion/Coupons/Group/Create', [
            'groups' => $groups,
            'custom_groups' => $custom_groups,
            'brands' => $brands,
            'group_type' => Constant::GroupCouponType,
            'custom_group_type' => Constant::CustomGroupCouponType,
            'data' => [
                'selectedDates' => [],
                'begin' => date('Y-m-d'),
                'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
            ],
            'branches' => $branches,
        ]);
    }

    public function store(GroupCouponRequest $request)
    {
        $data = $request->all();

        $data['is_bulk'] = 0;
        if (isset($data['brand_id'])) {
            $validateTitleEn = CouponTranslation::where('title', $request->title_en)
                ->where('locale', 'en')
                ->whereHas('coupon', function ($q) use ($data) {
                    $q->where('brand_id', $data['brand_id']);
                })
                ->count();

            $validateTitleAr = CouponTranslation::where('title', $request->title_ar)
                ->where('locale', 'ar')
                ->whereHas('coupon', function ($q) use ($data) {
                    $q->where('brand_id', $data['brand_id']);
                })
                ->count();

            $validateCode = Coupon::where('code', $request->code)
                ->where('brand_id', $data['brand_id'])
                ->count();

            if ($validateCode > 0) {
                $existCodes = Coupon::whereIn('code', $request->code)
                    ->where('brand_id', $data['brand_id'])
                    ->pluck('code')
                    ->toArray();

                return redirect()
                    ->back()
                    ->withErrors(['code' => ['The '.implode('-', $existCodes).' has already been taken.']]);
            }
            if ($validateTitleEn > 0) {
                return redirect()
                    ->back()
                    ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
            }

            if ($validateTitleAr > 0) {
                return redirect()
                    ->back()
                    ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
            }
        }

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $coupon = Coupon::create($data);
        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                CouponBranch::create([
                    'branch_id' => $row,
                    'coupon_id' => $coupon->id,
                    'brand_id' => $coupon->brand_id,
                ]);
            }
        }
        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('group-coupons.index');
    }

    public function edit(Coupon $group_coupon)
    {
        $groups = Group::Recent()
            ->where('active', 1)
            ->take(30)
            ->get();
        $branches = Branch::where('brand_id', $group_coupon->brand_id)
            ->Recent()
            ->get();
        $data = [
            'coupon' => $group_coupon,
            'title' => $group_coupon->title.' Edit',
            'begin' => date('Y-m-d'),
            'end' => date('Y-m-d', strtotime(date('Y-m-d').' +1 year')),
        ];

        $custom_groups = CustomGroup::Recent()
            ->active()
            ->take(30)
            ->get();

        return Inertia::render('Promotion/Coupons/Group/Edit', [
            'data' => $data,
            'brands' => Brand::Recent()->get(),
            'groups' => $groups,
            'custom_groups' => $custom_groups,
            'group_type' => Constant::GroupCouponType,
            'custom_group_type' => Constant::CustomGroupCouponType,
            'branches' => $branches,
            'branchIds' => $group_coupon->branch_ids,
        ]);
    }

    public function show($id)
    {
        $item = Coupon::with('brand', 'group', 'custom_group', 'branches')
            ->where('id', $id)
            ->first();
        $usage = Order::where('coupon_id', $item->id)->count();
        $data = ['item' => $item, 'usage' => $usage, 'title' => $item->title.' Info'];

        return Inertia::render('Promotion/Coupons/Group/Show', [
            'data' => $data,
        ]);
    }

    public function update(GroupCouponRequest $request, $id)
    {
        $coupon = Coupon::where('id', $id)->first();
        $data = $request->all();
        if (isset($data['brand_id'])) {
            $validateCode = Coupon::where('code', $request->code)
                ->where('id', '!=', $coupon->id)
                ->where('brand_id', $data['brand_id'])
                ->count();
            if ($validateCode > 0) {
                return redirect()
                    ->back()
                    ->withErrors(['code' => ['The code has already been taken.']]);
            }
        }

        $coupon->update($data);
        if ($data['branch_id'] && count($data['branch_id']) > 0) {
            foreach ($data['branch_id'] as $row) {
                CouponBranch::updateOrCreate(
                    [
                        'branch_id' => $row,
                        'coupon_id' => $coupon->id,
                        'brand_id' => $coupon->brand_id,
                    ],
                    [
                        'branch_id' => $row,
                        'coupon_id' => $coupon->id,
                        'brand_id' => $coupon->brand_id,
                    ]
                );

                CouponBranch::whereNotIn('branch_id', $data['branch_id'])
                    ->where('coupon_id', $coupon->id)
                    ->delete();
            }
        }
        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('group-coupons.index');
    }

    public function destroy(Request $request, $id)
    {
        Coupon::find($id)->delete();

        $pageTitle = __('theCoupon');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('group-coupons.index');
    }
}
