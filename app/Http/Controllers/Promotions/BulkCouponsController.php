<?php

namespace App\Http\Controllers\Promotions;

use App\Events\BulkCouponCreate;
use App\Exports\BulkCouponsExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Promotion\BulkCouponRequest;
use App\Jobs\ExportEmailJob;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Promotion\Coupon\BulkCoupon;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Promotion\Coupon\CouponTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class BulkCouponsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:bulk-coupons.index')->only(['index']);
        $this->middleware('checkPermission:bulk-coupons.show')->only(['show']);
        $this->middleware('checkPermission:bulk-coupons.edit')->only(['edit']);
        $this->middleware('checkPermission:bulk-coupons.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // $brandId = null;
        // ini_set('memory_limit', '-1');
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $active = $request->active;
        $type = $request->type;

        $coupons = BulkCoupon::Recent();
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            // $coupons = $coupons->whereHas('coupons', function ($q) {
            //     $q->where('brand_id', Cookie::get('brandId'));
            // });

            $coupons = $coupons->whereHas('coupons', function ($q) {
                $q->where('brand_id', Cookie::get('brandId'));
                $q->orWhereNull('brand_id');
            });
        }

        if (isset($search) && $search != '') {
            $coupons = $coupons->whereHas('coupons', function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('code', 'like', '%'.$search.'%');
                $q->orWhere('discount', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $coupons = $coupons->whereHas('coupons', function ($q) use ($fromDate) {
                $q->whereDate('created_at', '>=', $fromDate);
            });
        }
        if (isset($toDate) && $toDate != '') {
            $coupons = $coupons->whereHas('coupons', function ($q) use ($toDate) {
                $q->whereDate('created_at', '<=', $toDate);
            });
        }
        if (isset($active) && $active != '') {
            $coupons = $coupons->whereHas('coupons', function ($q) use ($active) {
                if ($active == 1) {
                    $q->where('active', $active);
                    $q->whereDate('expired_at', '>=', now());
                    $q->whereDate('started_at', '<=', now());
                } else {
                    $q->where('active', $active);
                    $q->orWhere(function ($q1) {
                        $q1->whereDate('expired_at', '<', now());
                        $q1->whereDate('started_at', '<', now());
                    });
                }
            });
        }
        if (isset($type) && $type != '') {
            $coupons = $coupons->whereHas('coupons', function ($q) use ($type) {
                $q->where('type', $type);
            });
        }

        $coupons = $coupons->paginate(env('PER_PAGE', 10));

        return Inertia::render('Promotion/Coupons/Bulk/Index', [
            'data' => $coupons,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'active' => $active,
            'type' => $type,
        ]);
    }

    public function create()
    {
        $brands = Brand::Recent()->get();
        $branches = Branch::where('brand_id', Cookie::get('brandId'))
        ->Recent()
        ->get();

        return Inertia::render('Promotion/Coupons/Bulk/Create', [
            'brands' => $brands,
            'branches' => $branches,
        ]);
    }

    public function unique_str()
    {
        $uniqueStr = '';
        $uniqueStr = Str::random(5);
        while (Coupon::where('code', $uniqueStr)->exists()) {
            $uniqueStr = Str::random(5);
        }

        return $uniqueStr;
    }

    public function store(BulkCouponRequest $request)
    {
        $data = $request->all();
        $validateTitleEn = CouponTranslation::where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereHas('coupon', function ($q) use ($data) {
                if (isset($data['brand_id'])) {
                    $q->where('brand_id', $data['brand_id']);
                }
            })
            ->count();
        $validateTitleAr = CouponTranslation::where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereHas('coupon', function ($q) use ($data) {
                if (isset($data['brand_id'])) {
                    $q->where('brand_id', $data['brand_id']);
                }
            })
            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }
        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'coupons/'.$folder.'/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar.'.'.explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar.'.png';
            $data['ar']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'coupons/'.$folder.'/en/';
            $name = $request->content_type_en
                ? $request->uuid_en.'.'.explode('/', $request->content_type_en)[1]
                : $request->uuid_en.'.png';
            $data['en']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }

        if ($data['no_coupons']) {
            $prefix = '';
            $brand = Brand::where('id', $data['brand_id'])->first();
            if ($brand && $brand->title_en) {
                $titles = explode(' ', $brand->title_en);
                $titles = array_filter($titles);
                foreach ($titles as $title) {
                    $prefix .= substr($title, 0, 1);
                }
                $prefix = strtoupper(trim($prefix));
            }
            $no_coupons = $data['no_coupons'];
            unset($data['no_coupons']);
            // generate bulk coupon
            // try {
            // generate coupons from event in background
            $bulkCoupon = BulkCoupon::create(['no_coupons' => $no_coupons]);
            $data['bulk_coupon_id'] = $bulkCoupon->id;
            $data['is_bulk'] = 1;
            $data['maximum_usage'] = 1;

            event(new BulkCouponCreate($no_coupons, $prefix, $data, $bulkCoupon, auth()->user()->email, $data['branch_id']));

            $pageTitle = __('theBulkCoupon');
            $request->session()->flash('success', __('BulkCouponCreateSuccess', ['title' => $pageTitle]));

            return redirect()->route('bulk-coupons.index');
            // } catch (\Throwable $th) {
            //     DB::rollback();
            //     $request->session()->flash('error', 'Something error');

            //     return redirect()->back();
            // }
        }

        return redirect()->route('bulk-coupons.index');
    }

    public function show($id)
    {
        $coupon = BulkCoupon::where('id', $id)->first();
        $data = ['item' => $coupon, 'title' => $coupon->title.' Info'];
        $coupons = Coupon::where('bulk_coupon_id', $id)->paginate(20);

        return Inertia::render('Promotion/Coupons/Bulk/Show', [
            'data' => $data,
            'coupons' => $coupons,
        ]);
    }

    // public function destroy(Request $request, $id)
    // {
    //     Coupon::find($id)->delete();
    //     $request->session()->flash('success', 'coupon deleted successfully!');

    //     return redirect()->route('coupons.index');
    // }

    public function filterByBrand($id)
    {
        return Coupon::Recent()
            ->where('brand_id', $id)
            ->get();
    }

    public function export(Request $request, $id)
    {
        $filename = 'exports/'.time().'-coupons.csv';
        (new BulkCouponsExport($id))
            ->queue($filename, 's3', null, [
                'visibility' => 'public-read',
                'ACL' => 'public-read',
            ])

            ->chain([new ExportEmailJob($filename, 'Export/coupons.csv')])
            ->onQueue('bulk-coupons');

        $request
            ->session()
            ->flash(
                'success',
                'the exported data is processing, please check your email after 1 hour and you will find the CSV!'
            );

        return redirect()->back();
    }
}
