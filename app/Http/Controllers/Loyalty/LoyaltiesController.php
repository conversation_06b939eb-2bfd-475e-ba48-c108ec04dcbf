<?php

namespace App\Http\Controllers\Loyalty;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Http\Requests\Loyalty\LoyaltyRequest;
use App\Models\AppSetting\AppSetting;
use App\Models\Brand\Branch;
use App\Models\Loyalty\Loyalty;
use App\Models\Loyalty\LoyaltyBranch;
use App\Models\Loyalty\LoyaltyRuleTranslation;
use Inertia\Inertia;

class LoyaltiesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:loyalties.index')->only(['index']);
        $this->middleware('checkPermission:loyalties.edit')->only(['store']);
    }

    public function index()
    {
        $loyalties = Loyalty::BrandType()
            ->with('branches')
            ->get();
        if (!empty($loyalties)) {
            foreach ($loyalties as $loyalty) {
                $loyalty->searchBranches = Branch::where('brand_id', $loyalty->brand_id)->get();
            }
        }
        $referral = Loyalty::firstOrCreate(
            ['type' => Constant::REFERRAL_LOYALTY_TYPE],
            ['type' => Constant::REFERRAL_LOYALTY_TYPE]
        );

        $branchIds = LoyaltyBranch::pluck('branch_id')->toArray();
        $setting = AppSetting::first();

        $translations = [];
        $loyaltyRule = LoyaltyRuleTranslation::all();
        foreach ($loyaltyRule as $rule) {
            $translations[$rule->locale] = $rule;
        }

        return Inertia::render('Loyalty/Calculation/Edit', [
            'loyaltyBrandItems' => $loyalties,
            'branchIds' => $branchIds,
            'referral' => $referral,
            'pay_rate' => !empty($loyalties) && !empty($loyalties[0]) ? $loyalties[0]->pay_rate : 0,
            'expiry_month' => !empty($setting) ? $setting->points_expiry_month : 0,
            'translations' => $translations,
        ]);
    }

    public function store(LoyaltyRequest $request)
    {
        $data = $request->all();
        $lang = [];
        foreach ($request->all() as $key => $val) {
            if (strpos($key, '_ar')) {
                $lang['ar'][str_replace('_ar', '', $key)] = $val;
            }
            if (strpos($key, '_en')) {
                $lang['en'][str_replace('_en', '', $key)] = $val;
            }
        }

        foreach ($lang as $key => $val) {
            LoyaltyRuleTranslation::updateOrCreate(['locale' => $key], $val);
        }

        $items = $data['loyaltyBrandItems'];
        $ids = [];
        if (!empty($items)) {
            foreach ($items as $item) {
                $loyalty = Loyalty::updateOrCreate(
                    [
                        'id' => $item['id'],
                    ],
                    [
                        'brand_id' => $item['brand_id'],
                        'active' => $item['active'],
                        'rate' => $item['rate'],
                        'type' => Constant::BRAND_LOYALTY_TYPE,
                        'pay_rate' => $data['pay_rate'],
                    ]
                );
                array_push($ids, $loyalty->id);

                $loyalty->branches()->sync($item['branch_id']);
                Loyalty::where('type', Constant::REFERRAL_LOYALTY_TYPE)->update([
                    'pay_rate' => $data['pay_rate'],
                ]);
            }
        }
        Loyalty::BrandType()
            ->whereNotIn('id', $ids)
            ->delete();
        if (!empty($data['referral'])) {
            Loyalty::updateOrCreate(
                [
                    'type' => Constant::REFERRAL_LOYALTY_TYPE,
                ],
                [
                    'type' => Constant::REFERRAL_LOYALTY_TYPE,
                    'active' => $data['referral']['active'],
                    'rate' => $data['referral']['rate'],
                    'pay_rate' => $data['referral']['pay_rate'],
                ]
            );
            Loyalty::where('type', Constant::BRAND_LOYALTY_TYPE)->update(['pay_rate' => $data['referral']['pay_rate']]);
        }

        AppSetting::first()->update(['points_expiry_month' => $data['expiry_month']]);

        $pageTitle = __('theLoyalty');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('loyalties.index');
    }
}
