<?php

namespace App\Http\Controllers\Corporate;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Http\Requests\CorporateRequest;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Corporate\Corporate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;
use App\Models\Permission\RoleBranch;

class GroupTicketRequestController extends Controller
{
    public function index(Request $request)
    {
        $brand_ids = [];
        $branch_ids = [];
        if (Cookie::get('role_brands')) {
            $brand_ids = unserialize(Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
        }
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $corporate_requests = Corporate::Recent()->Ticket();
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $orders = $corporate_requests->where('brand_id', Cookie::get('brandId'));
        }
        if (isset($search) && $search != '') {
            $corporate_requests = $corporate_requests->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhere('name', 'like', '%' . $search . '%');
                $q->orWhere('email', 'like', '%' . $search . '%');
                $q->orWhere('mobile', 'like', '%' . $search . '%');
                $q->orWhere('phone', 'like', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $corporate_requests = $corporate_requests->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $corporate_requests = $corporate_requests->whereDate('created_at', '<=', $toDate);
        }

        if (isset($request->status) && $request->status != '') {
            $corporate_requests = $corporate_requests->where('status', $request->status);
        }

        if (count($brand_ids) > 0) {
            $corporate_requests = $corporate_requests->whereIn('brand_id', $brand_ids);
        }

        $corporate_requests = $corporate_requests->paginate(15)->withQueryString();

        return Inertia::render('GroupTicket/Index', [
            'data' => $corporate_requests,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'status' => $request->status,
            'statuses' => Constant::GroupTicketStatus,
        ]);
    }

    public function edit(Corporate $group_ticket)
    {
        $brands = Brand::Recent()->get();
        $branches = Branch::where('brand_id', $group_ticket->brand_id)
            ->Recent()
            ->publish()
            ->get();

        $statuses = Constant::GroupTicketStatus;

        return Inertia::render('GroupTicket/Edit', [
            'data' => [
                'group_ticket' => $group_ticket,
            ],
            'branches' => $branches,
            'brands' => $brands,
            'statuses' => $statuses,
        ]);
    }

    public function show(Corporate $group_ticket)
    {
        return Inertia::render('GroupTicket/Show', ['data' => ['order' => $group_ticket]]);
    }

    public function update(CorporateRequest $request, Corporate $group_ticket)
    {
        $data = $request->all();
        $group_ticket->update($data);

        $pageTitle = __('theGroupRequest');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));
        return redirect()->route('group_tickets.show', $group_ticket->id);
    }
}