<?php

namespace App\Http\Controllers\Corporate;

use App\Http\Controllers\Controller;
use App\Models\Corporate\Corporate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class CorporateRequestController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:corporate.index')->only(['index']);
        $this->middleware('checkPermission:corporate.show')->only(['show']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $corporateRequests = Corporate::Recent()->Corporate();

        if (isset($search) && $search != '') {
            $corporateRequests = $corporateRequests->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('name', 'like', '%'.$search.'%');
                $q->orWhere('email', 'like', '%'.$search.'%');
                $q->orWhere('mobile', 'like', '%'.$search.'%');
                $q->orWhere('phone', 'like', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $corporateRequests = $corporateRequests->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $corporateRequests = $corporateRequests->whereDate('created_at', '<=', $toDate);
        }

        $corporateRequests = $corporateRequests->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Corporate/Index', [
            'data' => $corporateRequests,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function list(Request $request)
    {
        $corporateRequests = DB::table('corporates')
            ->select('id', 'name')
            ->get();

        return response()
            ->json(['corporate_requests' => $corporateRequests])
            ->header('Content-Type', 'application/json');
    }

    public function show(Request $request, $id)
    {
        $corporateRequest = Corporate::where('id', $id)->first();

        return Inertia::render('Corporate/Show', [
            'data' => ['item' => $corporateRequest],
        ]);
    }
}
