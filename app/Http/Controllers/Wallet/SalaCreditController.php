<?php

namespace App\Http\Controllers\Wallet;

use App\Http\Controllers\Controller;
use App\Http\Requests\Wallet\SalaCreditRequest;
use App\Models\Wallet\SalaCreditRule;
use Inertia\Inertia;

class SalaCreditController extends Controller
{
    //index function

    public function index()
    {
        $rules = SalaCreditRule::firstOrCreate(['id' => '1']);

        return Inertia::render('Wallet/Rules', [
            'data' => $rules,
            'enData' => $rules->translate('en'),
            'arData' => $rules->translate('ar'),
        ]);
    }

    public function store(SalaCreditRequest $request)
    {
        $data = $request->all();
        $data['en']['description'] = $request->lang_description_en;
        $data['en']['usage_policies'] = $request->lang_usage_policies_en;
        $data['en']['terms_and_conditions'] = $request->lang_terms_and_conditions_en;
        $data['en']['how_to_charge'] = $request->lang_how_to_charge_en;

        $data['ar']['description'] = $request->lang_description_ar;
        $data['ar']['usage_policies'] = $request->lang_usage_policies_ar;
        $data['ar']['terms_and_conditions'] = $request->lang_terms_and_conditions_ar;
        $data['ar']['how_to_charge'] = $request->lang_how_to_charge_ar;

        SalaCreditRule::updateOrCreate(['id' => '1'], $data);

        return redirect()->route('sala-credit.index');
    }
}
