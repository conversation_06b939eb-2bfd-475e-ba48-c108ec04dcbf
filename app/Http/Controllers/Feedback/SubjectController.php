<?php

namespace App\Http\Controllers\Feedback;

use App\Http\Controllers\Controller;
use App\Http\Requests\Feedback\SubjectRequest;
use App\Models\Feedback\Subject\Subject;
use App\Models\Feedback\Subject\SubjectBrand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class SubjectController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:subjects.index')->only(['index']);
        $this->middleware('checkPermission:subjects.show')->only(['show']);
        $this->middleware('checkPermission:subjects.edit')->only(['edit']);
        $this->middleware('checkPermission:subjects.delete')->only(['destroy']);
        $this->middleware('checkPermission:subjects.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $subjects = Subject::Ordered()->Recent();
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $subjects = $subjects->whereHas('brands', function ($q) {
                $q->where('brand_id', Cookie::get('brandId'));
            });
        }
        if (isset($search) && $search != '') {
            $subjects = $subjects->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $subjects = $subjects->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $subjects = $subjects->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $subjects = $subjects->where('active', $active);
        }
        $subjects = $subjects->paginate(env('PER_PAGE', 5))->withQueryString();

        return Inertia::render('Feedback/Subject/Index', [
            'data' => $subjects,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Feedback/Subject/Create');
    }

    public function store(SubjectRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        // $validateTitleEn = SubjectTranslation::where('title', $request->title_en)
        //     ->where('locale', 'en')
        //     ->whereHas('subject', function ($q) use ($data) {
        //         $q->where('brand_id', $data['brand_id'] ?? null);
        //     })
        //     ->count();
        // $validateTitleAr = SubjectTranslation::where('title', $request->title_ar)
        //     ->where('locale', 'ar')
        //     ->whereHas('subject', function ($q) use ($data) {
        //         $q->where('brand_id', $data['brand_id'] ?? null);
        //     })
        //     ->count();
        // if ($validateTitleEn > 0) {
        //     return redirect()
        //         ->back()
        //         ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        // }

        // if ($validateTitleAr > 0) {
        //     return redirect()
        //         ->back()
        //         ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        // }
        $subject = Subject::create($data);
        $brands = [];
        if ($data['brands'] && count($data['brands']) > 0) {
            foreach ($data['brands'] as $row) {
                array_push($brands, [
                    'brand_id' => $row,
                    'subject_id' => $subject->id,
                ]);
            }

            SubjectBrand::insert($brands);
        }
        $pageTitle = __('theSubject');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('subjects.index');
    }

    public function edit(Subject $subject)
    {
        $brand_ids = SubjectBrand::where('subject_id', $subject->id)
            ->pluck('brand_id')
            ->toArray();

        return Inertia::render('Feedback/Subject/Edit', [
            'data' => ['subject' => $subject, 'title' => $subject->title.' Edit', 'brand_ids' => $brand_ids],
        ]);
    }

    public function show(Subject $subject)
    {
        $data = ['subject' => $subject, 'title' => $subject->title.' Info'];

        return Inertia::render('Feedback/Subject/Show', ['data' => $data]);
    }

    public function update(SubjectRequest $request, Subject $subject)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        // if ($data['brand_id'] ?? null) {
        //     $validateTitleEn = SubjectTranslation::where('subject_id', '!=', $subject->id)
        //         ->where('title', $request->title_en)
        //         ->whereHas('subject', function ($q) use ($data) {
        //             $q->where('brand_id', $data['brand_id'] ?? null);
        //         })
        //         ->where('locale', 'en')
        //         ->count();
        //     $validateTitleAr = SubjectTranslation::where('subject_id', '!=', $subject->id)
        //         ->where('title', $request->title_ar)
        //         ->whereHas('subject', function ($q) use ($data) {
        //             $q->where('brand_id', $data['brand_id'] ?? null);
        //         })
        //         ->where('locale', 'ar')
        //         ->count();
        //     if ($validateTitleEn > 0) {
        //         return redirect()
        //             ->back()
        //             ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        //     }
        //     if ($validateTitleAr > 0) {
        //         return redirect()
        //             ->back()
        //             ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        //     }
        // }
        $subject->update($data);
        $pageTitle = __('theSubject');
        $brands = [];
        if ($data['brands'] && count($data['brands']) > 0) {
            foreach ($data['brands'] as $row) {
                array_push($brands, [
                    'brand_id' => $row,
                    'subject_id' => $subject->id,
                ]);
            }
            SubjectBrand::where('subject_id', $subject->id)->delete();
            SubjectBrand::insert($brands);
        }
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('subjects.index');
    }

    public function destroy(Request $request, $id)
    {
        $subject = Subject::find($id);

        $subject->delete();

        $pageTitle = __('theSubject');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('subjects.index');
    }
}
