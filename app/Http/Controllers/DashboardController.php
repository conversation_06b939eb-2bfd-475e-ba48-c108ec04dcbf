<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\Brand\Branch;
use Illuminate\Http\Request;
use App\Models\Ordering\Order;

use App\Models\Corporate\Corporate;
use App\Models\Booking\Ticket\Ticket;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Artisan;
use App\Models\Corporate\GroupTicket\CorporateTicket;

class DashboardController extends Controller
{
    //
    public function __construct()
    {
        $this->middleware('auth');
        // $this->middleware('checkPermission:dashboard');
    }

    public function logout(Request $request)
    {
        auth()
            ->guard('web')
            ->logout();

        app()->setLocale('en');
        return redirect()->route('login');
    }
    public function dashboard()
    {
        $orders = Order::orderBy('order_date', 'desc')
            ->where('is_parent', 0)
            ->where(function ($query) {
                if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                    $query->where('brand_id', Cookie::get('brandId'));
                }
            });
        $tickets = Ticket::orderBy('order_date', 'desc')->where(function ($query) {
            if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                $query->where('brand_id', Cookie::get('brandId'));
            }
        });

        $statistics['orders']['total'] = $orders->count();
        $statistics['orders']['upcoming'] = Order::orderBy('order_date', 'desc')
            ->where('is_parent', 0)
            ->where(function ($query) {
                if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                    $query->where('brand_id', Cookie::get('brandId'));
                }
            })
            ->where('status', 0)
            ->count();
        $statistics['orders']['confirmed'] = Order::orderBy('order_date', 'desc')
            ->where('is_parent', 0)
            ->where(function ($query) {
                if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                    $query->where('brand_id', Cookie::get('brandId'));
                }
            })
            ->where('status', 1)
            ->count();
        $statistics['orders']['cancelled'] = Order::orderBy('order_date', 'desc')
            ->where('is_parent', 0)
            ->where(function ($query) {
                if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                    $query->where('brand_id', Cookie::get('brandId'));
                }
            })
            ->where('status', 2)
            ->count();

        $statistics['tickets']['total'] = $tickets->count();
        $statistics['tickets']['active'] = Ticket::where(function ($query) {
            if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                $query->where('brand_id', Cookie::get('brandId'));
            }
        })
            ->where('active', 1)
            ->count();
        $statistics['tickets']['inActive'] = Ticket::where(function ($query) {
            if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                $query->where('brand_id', Cookie::get('brandId'));
            }
        })
            ->where('active', 0)
            ->count();

        $statistics['corporate']['tickets'] = CorporateTicket::count();
        $statistics['corporate']['requests'] = Corporate::count();
        $statistics['corporate']['total'] = Order::where(['is_bulk' => 1, 'is_parent' => 1])
            ->where(function ($query) {
                if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
                    $query->where('brand_id', Cookie::get('brandId'));
                }
            })
            ->count();

        $branches = Branch::where('brand_id', Cookie::get('brandId'))->get();

        return Inertia::render('Dashboard', [
            'statistics' => $statistics,
            'branches' => $branches,
        ]);
    }
}