<?php

namespace App\Http\Controllers\Offer;

use App\Http\Controllers\Controller;
use App\Http\Requests\OfferRequest;
use App\Http\Resources\OffersResource;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Governorate\Governorate;
use App\Models\Governorate\GovernorateTranslation;
use App\Models\Offer\Offer;
use App\Models\Offer\OfferTicket;
use App\Models\Permission\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class OffersController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:offers.index')->only(['index']);
        $this->middleware('checkPermission:offers.show')->only(['show']);
        $this->middleware('checkPermission:offers.edit')->only(['edit']);
        $this->middleware('checkPermission:offers.delete')->only(['destroy']);
        $this->middleware('checkPermission:offers.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // Start time
        $startTime = microtime(true);
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $offers = Offer::select(
            'offers.id',
            'price',
            'offers.brand_id',
            'offers.governorate_id',
            'active',
            'started_at',
            'expired_at',
            'offers.created_at',
            'type',
            'brand_translations.title as brand_title',
            'governorate_translations.title as governorate_title'
        )->Recent();

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $offers = $offers->where('offers.brand_id', Cookie::get('brandId'));
        }

        if (isset($search) && $search != '') {
            $offers = $offers->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $offers = $offers->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $offers = $offers->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $offers = $offers->where('active', $active);
        }
        $offers = $offers
            ->leftJoin('brand_translations', 'brand_translations.brand_id', '=', 'offers.brand_id')
            ->where('brand_translations.locale', \App::getLocale());
        $offers = $offers
            ->leftJoin(
                'governorate_translations',
                'governorate_translations.governorate_id',
                '=',
                'offers.governorate_id'
            )
            ->where('governorate_translations.locale', \App::getLocale());
        $offers = $offers->paginate(env('PER_PAGE', 10))->withQueryString();

        $responseData = [
            'data' => $offers,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ];
        $responseSize = strlen(json_encode($responseData));

        // End time and response time calculation
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

        return Inertia::render(
            'Offer/Index',
            array_merge($responseData, [
                'responseSize' => $responseSize,
                'responseTime' => $responseTime,
            ])
        );
    }

    public function create()
    {
        $governorates = Governorate::publish()->get();

        return Inertia::render('Offer/Create', ['governorates' => $governorates]);
    }

    public function store(OfferRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['en']['q_a_description'] = $request->q_a_description_en;
        $data['ar']['q_a_description'] = $request->q_a_description_ar;

        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'offers/' . $folder . '/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar . '.' . explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar . '.png';
            $data['ar']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'offers/' . $folder . '/en/';
            $name = $request->content_type_en
                ? $request->uuid_en . '.' . explode('/', $request->content_type_en)[1]
                : $request->uuid_en . '.png';
            $data['en']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }
        $offer = Offer::create($data);
        OfferTicket::create([
            'brand_id' => $data['brand_id'],
            'ticket_id' => $data['ticket_id'],
            'quantity' => $data['quantity'],
            'offer_id' => $offer->id,
            'default' => 1,
        ]);
        if (!empty($data['brandsItems'])) {
            foreach ($data['brandsItems'] as $row) {
                OfferTicket::create([
                    'brand_id' => $row['brand_id'],
                    'ticket_id' => $row['ticket_id'],
                    'quantity' => $row['quantity'],
                    'discount' => isset($row['discount']) ? $row['discount'] : 0,
                    'offer_id' => $offer->id,
                ]);
            }
        }
        $pageTitle = __('theOffer');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('offers.index');
    }

    public function edit(Offer $offer)
    {
        //searchTicketes
        $offerTickets = [];
        foreach ($offer->tickets as $ticket) {
            //get search ticket
            $ids = \App\Models\Brand\Branch::where('brand_id', $ticket->brand_id)
                ->where('governorate_id', $offer->governorate_id)
                ->pluck('id')
                ->toArray();

            $searchTicketes = \App\Models\Booking\Ticket\Ticket::whereHas('branches', function ($q) use ($ids) {
                $q->whereIn('branch_id', $ids);
            })
                ->where('brand_id', $ticket->brand_id)
                ->orderBy('id', 'desc')
                ->get();

            array_push($offerTickets, [
                'searchTicketes' => $searchTicketes,
                'ticket_id' => $ticket->ticket_id,
                'brand_id' => $ticket->brand_id,
                'id' => $ticket->id,
                'discount' => $ticket->discount,
                'quantity' => $ticket->quantity,
            ]);
        }
        $offer->quantity = $offer->defaultTicket->quantity ?? 1;

        $data = ['offer' => $offer, 'title' => $offer->title . ' Edit', 'tickets' => $offerTickets];

        $governorates = Governorate::publish()->get();
        $governorate = $offer->governorate_id;
        $allowedBranchIds = [];
        $user = Auth::user();
        $role = $user->roles->first();
        $roleInstance = Role::where('id', $role->id)->first();
        if (!empty($roleInstance)) {
            $brands = $roleInstance->theRoleBrands();
            if (!empty($brands)) {
                $brandIds = $roleInstance->theRoleBrands->pluck('brand_id')->toArray();
                if (!empty($brandIds)) {
                    $allowedBranchIds = Branch::query()
                        ->whereIn('brand_id', $brandIds)
                        ->pluck('id')
                        ->toArray();
                }
            }
        }

        if ($user->hasRole('super-admin')) {
            $brands = Brand::whereHas('branches', function ($q) use ($governorate) {
                $q->where('governorate_id', $governorate);
            })->get();
        } else {
            $brands = Brand::whereHas('branches', function ($q) use ($governorate, $allowedBranchIds) {
                $q->where('governorate_id', $governorate)->whereIn('id', $allowedBranchIds);
            })->get();
        }

        $tickets = \App\Models\Booking\Ticket\Ticket::whereHas('branches', function ($q) use ($allowedBranchIds) {
            $q->whereIn('branch_id', $allowedBranchIds);
        })
            ->where('brand_id', $offer->brand_id)
            ->orderBy('id', 'desc')
            ->get();

        return Inertia::render('Offer/Edit', [
            'data' => $data,
            'governorates' => $governorates,
            'brands' => $brands,
            'defaultTicket' => $offer->defaultTicket,
            'tickets' => $tickets,
        ]);
    }

    public function show(Offer $offer)
    {
        $offer->governorate_title = GovernorateTranslation::where('locale', \App::getLocale())
            ->where('governorate_id', $offer->governorate_id)
            ->first()->title;

        $offer->quantity = $offer->defaultTicket->quantity ?? 1;
        $data = [
            'item' => $offer,
            'title' => $offer->title . ' Info',
            'defaultTicket' => $offer->defaultTicket ? $offer->defaultTicket->ticket : null,
            'tickets' => $offer->tickets ? $offer->tickets : [],
        ];

        return Inertia::render('Offer/Show', ['data' => $data]);
    }

    public function update(OfferRequest $request, Offer $offer)
    {
        $data = $request->all();

        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['en']['q_a_description'] = $request->q_a_description_en;
        $data['ar']['q_a_description'] = $request->q_a_description_ar;

        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'offers/' . $folder . '/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar . '.' . explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar . '.png';
            $data['ar']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'offers/' . $folder . '/en/';
            $name = $request->content_type_en
                ? $request->uuid_en . '.' . explode('/', $request->content_type_en)[1]
                : $request->uuid_en . '.png';
            $data['en']['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }
        $offer->update($data);

        if (!empty($offer->defaultTicket)) {
            $offer->defaultTicket->update([
                'quantity' => $data['quantity'],
                'brand_id' => $data['brand_id'],
                'ticket_id' => $data['ticket_id'],
            ]);
        } else {
            OfferTicket::create([
                'brand_id' => $data['brand_id'],
                'ticket_id' => $data['ticket_id'],
                'quantity' => $data['quantity'],
                'offer_id' => $offer->id,
                'default' => 1,
            ]);
        }

        OfferTicket::where('offer_id', $offer->id)
            ->where('default', 0)
            ->delete();
        if (!empty($data['brandsItems'])) {
            foreach ($data['brandsItems'] as $row) {
                OfferTicket::create([
                    'brand_id' => $row['brand_id'],
                    'ticket_id' => $row['ticket_id'],
                    'quantity' => isset($row['quantity']) ? $row['quantity'] : 1,
                    'discount' => isset($row['discount']) ? $row['discount'] : 0,
                    'offer_id' => $offer->id,
                ]);
            }
        }

        $pageTitle = __('theOffer');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('offers.index');
    }

    public function destroy(Request $request, $id)
    {
        $offer = Offer::find($id);
        $pageTitle = __('theOffer');
        $offer->delete();

        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('offers.index');
    }

    public function filterByBrand(Request $request, $id, $branch_id)
    {
        $search = $request->search;
        $id = explode(',', $id);
        $branch = \App\Models\Brand\Branch::where('id', $branch_id)->first();
        $offers = Offer::query()
            ->Recent()
            ->whereIn('brand_id', $id)
            ->with('brand')
            ->where(function ($q) use ($search, $branch) {
                if ($search != '') {
                    $q->where('id', 'like', '%' . $search . '%');
                    $q->orWhereTranslationLike('title', '%' . $search . '%');
                    $q->orWhereHas('brand', function ($q1) use ($search) {
                        $q1->whereTranslationLike('title', '%' . $search . '%');
                    });
                }
                if (!empty($branch)) {
                    $q->where('governorate_id', $branch->governorate_id);
                }
            })
            ->take(25)
            ->get();

        return OffersResource::collection($offers)->resolve();
    }
}