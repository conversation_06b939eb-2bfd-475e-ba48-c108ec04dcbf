<?php

namespace App\Http\Controllers;

use App\Http\Requests\GovernorateRequest;
use App\Models\Governorate\Governorate;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GovernorateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:governorate.index')->only(['index']);
        $this->middleware('checkPermission:governorate.show')->only(['show']);
        $this->middleware('checkPermission:governorate.edit')->only(['edit']);
        $this->middleware('checkPermission:governorate.delete')->only(['destroy']);
        $this->middleware('checkPermission:governorate.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $governorates = Governorate::Ordered();
        if (isset($search) && $search != '') {
            $governorates = $governorates->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $governorates = $governorates->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $governorates = $governorates->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $governorates = $governorates->where('active', $active);
        }
        $governorates = $governorates->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Governorate/Index', [
            'data' => $governorates,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Governorate/Create');
    }

    public function store(GovernorateRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        Governorate::create($data);
        $pageTitle = __('theGovernorate');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('governorate.index');
    }

    public function edit(Governorate $governorate)
    {
        $data = ['governorate' => $governorate, 'title' => $governorate->title.' Edit'];

        return Inertia::render('Governorate/Edit', ['data' => $data]);
    }

    public function show(Governorate $governorate)
    {
        $data = ['governorate' => $governorate, 'title' => $governorate->title.' Info'];

        return Inertia::render('Governorate/Show', ['data' => $data]);
    }

    public function update(GovernorateRequest $request, Governorate $governorate)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $governorate->update($data);

        $pageTitle = __('theGovernorate');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('governorate.index');
    }

    public function destroy(Request $request, $id)
    {
        $governorate = Governorate::find($id);
        $pageTitle = __('theGovernorate');

        if ($governorate->branches()->exists()) {
            $request->session()->flash('success', __('NotDelete', ['title' => $pageTitle]));

            return redirect()->route('governorate.index');
        }
        $governorate->delete();

        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('governorate.index');
    }
}
