<?php

namespace App\Http\Controllers\Brand;

use App\Http\Controllers\Controller;
use App\Http\Requests\Brand\BranchRequest;
use App\Http\Resources\BranchesResource;
use App\Models\Booking\Ticket\TicketBranch;
use App\Models\Brand\Branch;
use App\Models\Brand\BranchTranslation;
use App\Models\Brand\Brand;
use App\Models\Governorate\Governorate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class BranchesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:branches.index')->only(['index']);
        $this->middleware('checkPermission:branches.show')->only(['show']);
        $this->middleware('checkPermission:branches.edit')->only(['edit']);
        $this->middleware('checkPermission:branches.delete')->only(['destroy']);
        $this->middleware('checkPermission:branches.create')->only(['create']);
    }

    public function index(Request $request)
    {
        ini_set('memory_limit', '-1');
        $id = $request->id;
        $brand = Brand::where('id', $id)
            ->select('id')
            ->first();
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $branches = [];
        if ($brand) {
            $branches = Branch::with('governorate')
                ->Ordered()
                ->Recent()
                ->select('id', 'created_at', 'active', 'brand_id', 'governorate_id', 'no_day_to_reschedule', 'ramadan')
                ->where('brand_id', $id);
            if (isset($search) && $search != '') {
                $branches = $branches->whereHas('governorate', function ($q) use ($search) {
                    $q->where('id', 'like', '%' . $search . '%');
                    $q->whereTranslationLike('title', '%' . $search . '%');
                });
            }

            if (isset($fromDate) && $fromDate != '') {
                $branches = $branches->whereDate('created_at', '>=', $fromDate);
            }
            if (isset($toDate) && $toDate != '') {
                $branches = $branches->whereDate('created_at', '<=', $toDate);
            }
            if (isset($active) && $active != '') {
                $branches = $branches->where('active', $active);
            }

            $branches = $branches->paginate(5)->withQueryString();
        }

        return Inertia::render('Brands/Branches/Index', [
            'data' => $branches,
            'brand' => $brand,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create(Request $request)
    {
        $id = $request->id;
        $brand = Brand::find($id);
        $governorate = Governorate::Ordered()->get();

        return Inertia::render('Brands/Branches/Create', [
            'brand' => $brand,
            'governorates' => $governorate,
            'foodicsUrl' => Config::get('app.foodics_Admin_url'),
        ]);
    }

    public function store(BranchRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $data['en']['working_hour'] = $request->working_hour_en;
        $data['ar']['working_hour'] = $request->working_hour_ar;

        $validateTitleEn = BranchTranslation::where('title', $request->title_en)
            ->where('locale', 'en')
            ->whereNull('deleted_at')
            ->whereHas('branch', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();
        $validateTitleAr = BranchTranslation::where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereNull('deleted_at')
            ->whereHas('branch', function ($q) use ($data) {
                $q->where('brand_id', $data['brand_id']);
            })
            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }
        $count = (Branch::latest()->first()->id ?? 0) + 1;
        if (count($request->image_en) > 0) {
            $i = 1;
            foreach ($request->image_en as $file) {
                if ($file['key']) {
                    $path = 'branches/' . $count . '/en/';
                    $name = $file['content_type']
                        ? $file['uuid'] . '.' . explode('/', $file['content_type'])[1]
                        : $file['uuid'] . '.png';
                    $data['en']['image' . $i] = $path . $name;
                    Storage::disk('s3')->copy($file['key'], $data['en']['image' . $i], 'public');
                    $i++;
                }
            }
        }

        if (count($request->image_ar) > 0) {
            $i = 1;
            foreach ($request->image_ar as $file) {
                if ($file['key']) {
                    $path = 'branches/' . $count . '/ar/';
                    $name = $file['content_type']
                        ? $file['uuid'] . '.' . explode('/', $file['content_type'])[1]
                        : $file['uuid'] . '.png';
                    $data['ar']['image' . $i] = $path . $name;
                    Storage::disk('s3')->copy($file['key'], $data['ar']['image' . $i], 'public');
                    $i++;
                }
            }
        }
        Branch::create($data);

        $pageTitle = __('theBranch');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('branches.index', ['id' => $request->brand_id]);
    }

    public function edit(Branch $branch)
    {
        $data = ['branch' => $branch, 'title' => $branch->title . ' Edit'];
        $governorates = Governorate::Ordered()->get();
        $brand = Brand::find($branch->brand_id);

        return Inertia::render('Brands/Branches/Edit', [
            'data' => $data,
            'governorates' => $governorates,
            'brand' => $brand,
            'foodicsUrl' => Config::get('app.foodics_Admin_url'),
        ]);
    }

    public function update(BranchRequest $request, Branch $branch)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['working_hour'] = $request->working_hour_en;
        $data['ar']['working_hour'] = $request->working_hour_ar;

        $validateTitleEn = BranchTranslation::where('branch_id', '!=', $branch->id)
            ->where('title', $request->title_en)
            ->whereNull('deleted_at')
            ->where('locale', 'en')
            ->whereHas('branch', function ($q) use ($branch) {
                $q->where('brand_id', $branch->brand_id);
            })
            ->count();
        $validateTitleAr = BranchTranslation::where('branch_id', '!=', $branch->id)
            ->where('title', $request->title_ar)
            ->where('locale', 'ar')
            ->whereNull('deleted_at')
            ->whereHas('branch', function ($q) use ($branch) {
                $q->where('brand_id', $branch->brand_id);
            })
            ->count();
        if ($validateTitleEn > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_en' => [__('EnglishTitleTaken')]]);
        }

        if ($validateTitleAr > 0) {
            return redirect()
                ->back()
                ->withErrors(['title_ar' => [__('ArabicTitleTaken')]]);
        }
        if (count($request->image_en) > 0) {
            $data['en']['image1'] = '';
            $data['en']['image2'] = '';
            $data['en']['image3'] = '';
            $i = 1;
            foreach ($request->image_en as $file) {
                if ($file['key']) {
                    $path = 'branches/' . $branch->id . '/en/';
                    $name = $file['content_type']
                        ? $file['uuid'] . '.' . explode('/', $file['content_type'])[1]
                        : $file['uuid'] . '.png';
                    $data['en']['image' . $i] = $path . $name;
                    Storage::disk('s3')->copy($file['key'], $data['en']['image' . $i], 'public');
                }
                $i++;
            }
        }

        if (count($request->image_ar) > 0) {
            $i = 1;
            foreach ($request->image_ar as $file) {
                if ($file['key']) {
                    $path = 'branches/' . $branch->id . '/ar/';
                    $name = $file['content_type']
                        ? $file['uuid'] . '.' . explode('/', $file['content_type'])[1]
                        : $file['uuid'] . '.png';
                    $data['ar']['image' . $i] = $path . $name;
                    Storage::disk('s3')->copy($file['key'], $data['ar']['image' . $i], 'public');
                }
                $i++;
            }
        }
        $branch->update($data);

        $pageTitle = __('theBranch');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('branches.index', ['id' => $branch->brand_id, 'page' => $data['currentPage']]);
    }

    public function destroy(Request $request, $id)
    {
        $branch = Branch::find($id);
        $brand_id = $branch->brand_id;
        $branch->delete();

        $pageTitle = __('theBranch');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('branches.index', ['id' => $brand_id]);
    }

    public function deleteImage(Request $request, $id)
    {
        $branch = Branch::find($id);
        if ($request->locale == 'en') {
            BranchTranslation::where('locale', 'en')
                ->where('branch_id', $branch->id)
                ->update(['image1' => '', 'image2' => '', 'image3' => '']);
        } elseif ($request->locale == 'ar') {
            BranchTranslation::where('locale', 'ar')
                ->where('branch_id', $branch->id)
                ->update(['image1' => '', 'image2' => '', 'image3' => '']);
        }

        // $request->session()->flash('success', 'Branch Images deleted successfully!');
        return redirect()->route('branches.edit', $id);
    }

    //filterGovernorateByBrand
    public function filterGovernorateByBrand(Request $request, $id)
    {
        $data = [];
        $id = explode(',', $id);
        $ids = Branch::where('brand_id', Cookie::get('brandId'))
            ->pluck('governorate_id')
            ->toArray();

        $cities = Governorate::whereIn('id', $ids)
            ->Recent()
            ->get();

        return $cities;
    }

    public function filterByBrand(Request $request, $id)
    {
        $data = [];
        $search = $request->search;
        $id = explode(',', $id);
        $branches = Branch::query()
            ->Recent()
            ->whereIn('brand_id', $id)
            ->with('brand')
            ->where(function ($q) use ($search) {
                if ($search != '') {
                    $q->where('id', 'like', '%' . $search . '%');
                    $q->orWhereTranslationLike('title', '%' . $search . '%');
                    $q->orWhereHas('brand', function ($q1) use ($search) {
                        $q1->whereTranslationLike('title', '%' . $search . '%');
                    });
                }
            })
            ->take(25)
            ->get();

        // foreach ($branches as $branch) {
        //     $title_en = (!empty($branch->brand) ? $branch->brand->title_en . ' - ' : '') . $branch->title_en;
        //     $title_ar = (!empty($branch->brand) ? $branch->brand->title_ar . ' - ' : '') . $branch->title_ar;
        //     $item = ['title_en' => $title_en, 'title_ar' => $title_ar, 'id' => $branch->id];
        //     array_push($data, $item);
        // }

        // return $data;
        return BranchesResource::collection($branches)->resolve();
    }

    public function filterByBrandTicket(Request $request, $id)
    {
        $data = [];
        $search = $request->search;
        $id = explode(',', $id);
        $branchIds = TicketBranch::distinct('branch_id')
            ->whereHas('branch')
            ->pluck('branch_id')
            ->toArray();
        $branches = Branch::query()
            ->Recent()
            ->whereIn('brand_id', $id)
            ->whereIn('id', $branchIds)
            ->with('brand')
            ->where(function ($q) use ($search) {
                if ($search != '') {
                    $q->where('id', 'like', '%' . $search . '%');
                    $q->orWhereTranslationLike('title', '%' . $search . '%');
                    $q->orWhereHas('brand', function ($q1) use ($search) {
                        $q1->whereTranslationLike('title', '%' . $search . '%');
                    });
                }
            })
            ->take(25)
            ->get();

        return BranchesResource::collection($branches)->resolve();
    }
}