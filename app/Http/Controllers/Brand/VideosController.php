<?php

namespace App\Http\Controllers\Brand;

use Inertia\Inertia;
use App\Models\Video\Video;
use App\Models\Brand\Branch;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Video\VideoGovernorate;
use Illuminate\Support\Facades\Cookie;
use App\Models\Governorate\Governorate;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\Brand\VideoRequest;

class VideosController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:videos.index')->only(['index']);
        $this->middleware('checkPermission:videos.show')->only(['show']);
        $this->middleware('checkPermission:videos.edit')->only(['edit']);
        $this->middleware('checkPermission:videos.delete')->only(['destroy']);
        $this->middleware('checkPermission:videos.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $videos = Video::Ordered()->Recent();
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $videos = $videos->where('brand_id', Cookie::get('brandId'));
        }
        if (isset($search) && $search != '') {
            $videos = $videos->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $videos = $videos->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $videos = $videos->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $videos = $videos->where('active', $active);
        }
        $videos = $videos->paginate(13)->withQueryString();

        return Inertia::render('Brands/Video/Index', [
            'data' => $videos,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        $ids = Branch::where('brand_id', Cookie::get('brandId'))
            ->pluck('governorate_id')
            ->toArray();

        $cities = Governorate::whereIn('id', $ids)
            ->Recent()
            ->get();

        return Inertia::render('Brands/Video/Create', ['cities' => $cities]);
    }

    public function store(VideoRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        if ($request->key) {
            $path = 'brands/videos/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        //check to unpublish

        $video = Video::create($data);
        if ($data['governorate_id'] && count($data['governorate_id']) > 0) {
            $governorate = [];

            foreach ($data['governorate_id'] as $row) {
                array_push($governorate, [
                    'governorate_id' => $row,
                    'video_id' => $video->id,
                ]);
            }
            //
            if ($request->active == 1) {
                $ids = VideoGovernorate::whereIn('governorate_id', $data['governorate_id'])
                    ->distinct('video_id')
                    ->pluck('video_id')
                    ->toArray();
                Video::where('brand_id', $video->brand_id)
                    ->whereIn('id', $ids)
                    ->update(['active' => 0]);
            }
            VideoGovernorate::insert($governorate);
        }
        $pageTitle = __('theVideo');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));
        return redirect()->route('videos.index');
    }

    public function edit(Video $video)
    {
        $governorateIds = VideoGovernorate::where('video_id', $video->id)
            ->pluck('governorate_id')
            ->toArray();
        $ids = Branch::where('brand_id', $video->brand_id)
            ->pluck('governorate_id')
            ->toArray();

        $cities = Governorate::whereIn('id', $ids)
            ->Recent()
            ->get();
        $data = ['video' => $video, 'title' => $video->title . ' Edit'];

        return Inertia::render('Brands/Video/Edit', [
            'data' => $data,
            'cities' => $cities,
            'governorateIds' => $governorateIds,
        ]);
    }

    public function show(Video $video)
    {
        $data = ['video' => $video, 'title' => $video->title . ' Info'];

        return Inertia::render('Brands/Video/Show', ['data' => $data]);
    }

    public function update(VideoRequest $request, Video $video)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        if ($request->key) {
            $path = 'brands/videos/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $video->update($data);

        if ($data['governorate_id'] && count($data['governorate_id']) > 0) {
            $governorate = [];

            foreach ($data['governorate_id'] as $row) {
                array_push($governorate, [
                    'governorate_id' => $row,
                    'video_id' => $video->id,
                ]);
            }

            if ($video->active == 1) {
                $ids = VideoGovernorate::whereIn('governorate_id', $data['governorate_id'])
                    ->distinct('video_id')
                    ->pluck('video_id')
                    ->toArray();
                $ids = array_diff($ids, [$video->id]);
                Video::where('brand_id', $video->brand_id)
                    ->whereIn('id', $ids)
                    ->update(['active' => 0]);
            }
            VideoGovernorate::where('video_id', $video->id)->delete();
            VideoGovernorate::insert($governorate);
        }

        $pageTitle = __('theVideo');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));
        return redirect()->route('videos.index');
    }

    public function destroy(Request $request, $id)
    {
        $video = Video::find($id);
        $pageTitle = __('theVideo');
        $video->delete();

        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('videos.index');
    }
}