<?php

namespace App\Http\Controllers\Brand;

use App\Helpers\Constant;
use App\Helpers\DateRange;
use App\Http\Controllers\Controller;
use App\Http\Requests\Brand\BranchSettingRequest;
use App\Models\Booking\Addon\BirthdaySetting;
use App\Models\Brand\Branch;
use App\Models\Brand\WorkHour;
use App\Models\Brand\WorkingDay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class BirthdaySettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');

        $this->middleware('checkPermission:branches.settings');
    }

    public function date_range($first, $last, $step = '+1 day', $output_format = 'Y-m-d')
    {
        $dates = [];
        $current = strtotime($first);
        $last = strtotime($last);

        while ($current <= $last) {
            $dates[] = date($output_format, $current);
            $current = strtotime($step, $current);
        }

        return $dates;
    }

    public function show($id)
    {
        $days = Constant::Days;
        $branch = Branch::with('brand')
            ->where('id', $id)
            ->first();
        $datesObjects = WorkHour::where('branch_id', $id)
            ->OffDates()
            ->select('date', 'id', 'from_time', 'to_time');
        $disabledDates = $datesObjects
            ->get()
            ->pluck('formate_date')
            ->toArray();
        $brandWorkingDay = WorkingDay::where('brand_id', $branch->brand_id)
            ->pluck('day_id')
            ->toArray();
        $daysId = [1, 2, 3, 4, 5, 6, 7];

        $disableWorkingDay = array_diff($daysId, $brandWorkingDay);
        $list = [];
        $day_ids = [];

        if (count($disableWorkingDay) > 0) {
            $day_arr = [];
            foreach ($disableWorkingDay as $key => $day_id) {
                $index = array_search($day_id, array_column(Constant::Days, 'id'));
                $title = Constant::Days[$index]['title_en'];
                array_push($day_arr, $title);
                array_push($day_ids, (string) $day_id);
            }
            $yearDates = DateRange::getDateRange(date('Y-m-d'), date('Y-m-d', strtotime(date('Y-m-d') . ' +1 year')));

            foreach ($yearDates as $time) {
                $date = date('Y-m-d', strtotime($time));
                if (in_array(date('l', strtotime($date)), $day_arr)) {
                    $list[] = date('Y-m-d', strtotime($time));
                    array_push($disabledDates, date('Y-n-j', strtotime($time)));
                }
            }
        }

        $branchWorkingObjects = WorkHour::where('branch_id', $id)
            ->where('type', 1)
            ->whereNotNull('day_id');

        $datesObjects = $datesObjects->whereDate('date', '>=', date('Y-m-d'))->get();
        $shifts = [];
        $daysId = [1, 2, 3, 4, 5, 6, 7];
        foreach ($daysId as $dayId) {
            $workingObjects = BirthdaySetting::where('branch_id', $id)
                ->where('type', 1)
                ->where('day_id', $dayId)
                ->whereNotNull('day_id');
            $index = array_search($dayId, array_column(Constant::Days, 'id'));
            $item = $workingObjects
                ->select('id', 'from_time', 'to_time', 'day_id', 'break_duration')
                ->orderBy('from_time', 'asc')
                ->get();
            if (count($item) < 1) {
                $item = [
                    'from_time' => ['HH' => '00', 'mm' => '00'],
                    'to_time' => ['HH' => '00', 'mm' => '00'],
                    'day_id' => $dayId,
                    'break_duration' => '',
                ];
                $item = [(object) $item];
            }

            array_push($shifts, [
                'id' => $dayId,
                'day_id' => $dayId,
                'title_ar' => Constant::Days[$index]['title_ar'],
                'title_en' => Constant::Days[$index]['title_en'],
                'rowspan' => $workingObjects->count() + 1,
                'data' => $item,
            ]);
        }

        $data = [
            'branch' => $branch,
            'days' => $days,
            'shifts' => $shifts,
            'selectedWorking' => $branchWorkingObjects
                ->distinct('day_id')
                ->pluck('day_id')
                ->toArray(),
            'disableWorkingDay' => $day_ids,
        ];

        return Inertia::render('Brands/Branches/Birthday-Settings', [
            'data' => $data,
        ]);
    }

    public function update(BranchSettingRequest $request, $id)
    {
        $branch = Branch::find($id);
        $data = $request->all();
        DB::beginTransaction();

        BirthdaySetting::whereNotNull('day_id')
            ->where('branch_id', $id)
            ->where('type', 1)
            ->delete();

        foreach ($data['shifts'] as $row) {
            if ($row['data'] && count($row['data']) > 0) {
                foreach ($row['data'] as $shift) {
                    if ($shift['from_time'] != '' && $shift['to_time'] != '') {
                        $object = [
                            'day_id' => $row['day_id'],
                            'break_duration' => $shift['break_duration'] ?? '',
                            'type' => 1,
                            'branch_id' => $branch->id,
                            'from_time' => isset($shift['from_time']['HH']) ? $shift['from_time']['HH'] . ':' . $shift['from_time']['mm'] : $shift['from_time'],
                            'to_time' => isset($shift['to_time']['HH']) ? $shift['to_time']['HH'] . ':' . $shift['to_time']['mm'] : $shift['to_time'],
                        ];

                        $setting = BirthdaySetting::create($object);
                    }
                }
            }
        }

        DB::commit();
        //create work our

        $pageTitle = __('messages.theBirthdaySettings');
        $request->session()->flash('success', __('messages.UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('birthday-settings.show', $branch);
    }

    public function destroy(Request $request, $id)
    {
        $offDate = WorkHour::where('id', $id)->first();
        if ($offDate) {
            $branch = $offDate->branch_id;
            $offDate->delete();
            $request->session()->flash('success', 'Branch off date deleted successfully!');
        }

        return redirect()->route('birthday-settings.show', $branch);
    }
}