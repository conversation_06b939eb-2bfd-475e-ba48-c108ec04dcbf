<?php

namespace App\Http\Controllers\Brand;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Http\Requests\Brand\BrandRequest;
use App\Models\AppSetting\SocialLink\SocialLink;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Brand\WorkingDay;
use App\Models\Permission\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class BrandsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:brands.index')->only(['index']);
        $this->middleware('checkPermission:brands.show')->only(['show']);
        $this->middleware('checkPermission:brands.edit')->only(['edit']);
        $this->middleware('checkPermission:brands.delete')->only(['destroy']);
        $this->middleware('checkPermission:brands.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $brands = Brand::Ordered()
            ->Recent()
            ->with('branches');

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $brands = $brands->where('id', Cookie::get('brandId'));
        }
        if (isset($search) && $search != '') {
            $brands = $brands->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $brands = $brands->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $brands = $brands->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $brands = $brands->where('active', $active);
        }

        $brands = $brands->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Brands/Index', [
            'data' => $brands,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        $days = Constant::Days;
        $data = [
            'days' => $days,
        ];
        $socialLinkItems = [];

        return Inertia::render('Brands/Create', [
            'data' => $data,
            'socialLinkItems' => $socialLinkItems,
        ]);
    }

    public function store(BrandRequest $request)
    {
        $old = Brand::Recent()->first();
        $count = !$old ? 1 : $old->id + 1;
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['en']['working_hour'] = $request->working_hour_en;
        $data['ar']['working_hour'] = $request->working_hour_ar;
        $data['en']['terms_condition'] = $request->terms_condition_en;
        $data['ar']['terms_condition'] = $request->terms_condition_ar;

        $data['en']['waiver'] = $request->waiver_en;
        $data['ar']['waiver'] = $request->waiver_ar;

        //waiver
        if ($request->key) {
            $path = 'brands/' . $count . '/logo/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $brand = Brand::create($data);

        if ($data['days']) {
            foreach ($data['days'] as $row) {
                WorkingDay::create(['day_id' => $row, 'brand_id' => $brand->id]);
            }
        }

        if (!empty($data['socialLinkItems'])) {
            $this->saveSocialLink($data['socialLinkItems'], $brand->id);
        }

        $pageTitle = __('theBrand');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('brands.index');
    }

    public function saveSocialLink($socialLinkItems, $id)
    {
        $links = [];
        foreach ($socialLinkItems as $item) {
            $data['ar']['title'] = $item['title_ar'];
            $data['en']['title'] = $item['title_en'];
            $data['link'] = $item['link'];
            $data['brand_id'] = $id;
            $data['icon'] = $item['icon'];
            $data['color'] = $item['color'];
            SocialLink::create($data);
        }
    }

    public function edit(Brand $brand)
    {
        $workingObjects = WorkingDay::where('brand_id', $brand->id)
            ->pluck('day_id')
            ->toArray();
        $days = Constant::Days;
        $data = [
            'days' => $days,
            'brand' => $brand,
            'workingObjects' => $workingObjects,
            'title' => $brand->title . ' Edit',
        ];
        $socialLinkItems = SocialLink::where('brand_id', $brand->id)->get();

        return Inertia::render('Brands/Edit', [
            'data' => $data,
            'socialLinkItems' => $socialLinkItems,
        ]);
    }

    public function show(Brand $brand)
    {
        $workingObjects = WorkingDay::where('brand_id', $brand->id)

            ->pluck('day_id')
            ->toArray();
        $days = Constant::Days;
        $data = [
            'item' => $brand,
            'days' => $days,
            'workingObjects' => $workingObjects,
            'title' => $brand->title . ' Info',
        ];

        return Inertia::render('Brands/Show', [
            'data' => $data,
        ]);
    }

    public function update(BrandRequest $request, Brand $brand)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;

        $data['en']['working_hour'] = $request->working_hour_en;
        $data['ar']['working_hour'] = $request->working_hour_ar;

        $data['en']['waiver'] = $request->waiver_en;
        $data['ar']['waiver'] = $request->waiver_ar;

        $data['en']['terms_condition'] = $request->terms_condition_en;
        $data['ar']['terms_condition'] = $request->terms_condition_ar;
        if ($request->key) {
            $path = 'brands/' . $brand->id . '/logo/';
            $name = $request->content_type
                ? $request->uuid . '.' . explode('/', $request->content_type)[1]
                : $request->uuid . '.png';
            $data['image'] = $path . $name;
            Storage::disk('s3')->copy($request->key, $data['image'], 'public');
        }
        $status = $brand->update($data);

        WorkingDay::where('brand_id', $brand->id)
            ->whereNotIn('day_id', $data['days'])
            ->delete();
        if ($data['days']) {
            foreach ($data['days'] as $row) {
                WorkingDay::updateOrCreate(
                    ['day_id' => $row, 'brand_id' => $brand->id],
                    ['day_id' => $row, 'brand_id' => $brand->id]
                );
            }
        }
        //save social links
        if (!empty($data['socialLinkItems'])) {
            SocialLink::where('brand_id', $brand->id)->delete();
            $this->saveSocialLink($data['socialLinkItems'], $brand->id);
        }
        $pageTitle = __('theBrand');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('brands.index');
    }

    public function destroy(Request $request, $id)
    {
        Brand::find($id)->delete();

        $pageTitle = __('theBrand');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('brands.index');
    }

    public function saveFilteredBrand(Request $request)
    {
        Cookie::forget('brandId');
        Cookie::queue('brandId', $request->id);

        if ($request->url && $request->url == '/branches' && $request->old_branch != null) {
            return redirect()->route('branches.index', ['id' => $request->id]);
        }

        return redirect()->back();
    }

    //get brand by governorate id
    public function list($governorate)
    {
        $allowedBranchIds = [];
        $user = Auth::user();
        $role = $user->roles->first();
        $roleInstance = Role::where('id', $role->id)->first();
        if (!empty($roleInstance)) {
            $brands = $roleInstance->theRoleBrands();
            if (!empty($brands)) {
                $brandIds = $roleInstance->theRoleBrands->pluck('brand_id')->toArray();
                if (!empty($brandIds)) {
                    $allowedBranchIds = Branch::query()
                        ->whereIn('brand_id', $brandIds)
                        ->pluck('id')
                        ->toArray();
                }
            }
        }

        if ($user->hasRole('super-admin')) {
            return Brand::whereHas('branches', function ($q) use ($governorate) {
                $q->where('governorate_id', $governorate);
            })->get();
        } else {
            return Brand::whereHas('branches', function ($q) use ($governorate, $allowedBranchIds) {
                $q->where('governorate_id', $governorate)->whereIn('id', $allowedBranchIds);
            })->get();
        }
    }
}