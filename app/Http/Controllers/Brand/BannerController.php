<?php

namespace App\Http\Controllers\Brand;

use App\Http\Controllers\Controller;
use App\Http\Requests\Brand\BannerRequest;
use App\Models\Banner\Banner;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Promotion\Bundle\Bundle;
use App\Models\Promotion\Coupon\Coupon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class BannerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:banners.index')->only(['index']);
        $this->middleware('checkPermission:banners.show')->only(['show']);
        $this->middleware('checkPermission:banners.edit')->only(['edit']);
        $this->middleware('checkPermission:banners.delete')->only(['destroy']);
        $this->middleware('checkPermission:banners.create')->only(['create']);
    }

    public function index(Request $request)
    {
        // Start time
        $startTime = microtime(true);

        $active = $request->active;
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $banners = Banner::select('id', 'created_at', 'expired_at', 'active', 'redirect_type')
            ->Ordered()
            ->Recent()
            ->where(function ($q) {
                $q->whereNull('brand_id');
                $q->orWhereHas('brand');
            });

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $banners = $banners->where('brand_id', Cookie::get('brandId'));
        } else {
            $banners = $banners->where(function ($q) {
                $q->where('in_home', 1);
                $q->orWhereNull('brand_id');
            });
        }

        if (isset($search) && $search != '') {
            $banners = $banners->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereHas('coupon', function ($q1) use ($search) {
                    $q1->orWhereTranslationLike('title', '%'.$search.'%');
                });
                $q->orWhereHas('bundle', function ($q1) use ($search) {
                    $q1->orWhereTranslationLike('title', '%'.$search.'%');
                });
            });
        }

        if (isset($fromDate) && $fromDate != '') {
            $banners = $banners->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $banners = $banners->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            date_default_timezone_set('Asia/Riyadh');
            $time = date('Y-m-d');
             if ( $active == 1) {
                 $banners = $banners->where('active', $active);
                 $banners = $banners->Valid();
             }elseif ( $active == 0) {
                 $banners = $banners->where(function($q) use($active,$time){
                     $q->whereDate('expired_at', '<', $time)->orWhere('active', $active);
                 });
             }
        }

        $banners = $banners->paginate(env('PER_PAGE', 10))->withQueryString();

        date_default_timezone_set('Asia/Riyadh');
        $time = date('Y-m-d');

        // Calculate the size of the data returned to Inertia
        $responseData = [
            'data' => $banners,
            'active' => $active,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'now' => $time,
        ];

        $responseSize = strlen(json_encode($responseData));

        // End time and response time calculation
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

        // Pass response size and time to Inertia
        return Inertia::render(
            'Brands/Banners/Index',
            array_merge($responseData, [
                'responseSize' => $responseSize,
                'responseTime' => $responseTime, // response time in milliseconds
            ])
        );
    }

    public function create()
    {
        $brands = Brand::Recent();
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $brands = $brands->where('id', Cookie::get('brandId'));
        }

        $brands = $brands->get();
        $branches = [];
        $coupons = [];
        $bundles = [];

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $branches = Branch::Recent()
                ->where('brand_id', Cookie::get('brandId'))
                ->take(10)
                ->get();
            $coupons = Coupon::Single()
                ->Recent()
                ->where('brand_id', Cookie::get('brandId'))
                ->take(5)
                ->get();
            $bundles = Bundle::Recent()
                ->where('brand_id', Cookie::get('brandId'))
                ->take(5)
                ->get();
        }

        return Inertia::render('Brands/Banners/Create', [
            'brands' => $brands,
            'branches' => $branches,
            'coupons' => $coupons,
            'bundles' => $bundles,
        ]);
    }

    public function store(BannerRequest $request)
    {
        $data = $request->all();

        $data['en']['redirect_url'] = $request->redirect_url_en;
        $data['ar']['redirect_url'] = $request->redirect_url_ar;
        $data['brand_id'] = $data['brand_id'] == '' ? null : $data['brand_id'];
        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'banners/'.$folder.'/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar.'.'.explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar.'.png';
            $data['ar']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'banners/'.$folder.'/en/';
            $name = $request->content_type_en
                ? $request->uuid_en.'.'.explode('/', $request->content_type_en)[1]
                : $request->uuid_en.'.png';
            $data['en']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }

        Banner::create($data);

        $pageTitle = __('theBanner');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('banners.index');
    }

    public function edit(Banner $banner)
    {
        $branches = [];
        $coupons = [];
        $bundles = [];
        $brands = Brand::Recent();
        $data = ['banner' => $banner, 'title' => $banner->title.' Edit'];
        $branches = Branch::Recent()->get();
        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $branches = Branch::Recent()
                ->where('brand_id', Cookie::get('brandId'))
                ->take(10)
                ->get();
            $coupons = Coupon::Single()
                ->Recent()
                ->where('brand_id', Cookie::get('brandId'))
                ->where(function ($q) use ($banner) {
                    if (! $banner->object_id && $banner->redirect_type == 4) {
                        $q->where('id', $banner->object_id);
                    }
                })
                ->take(5)
                ->get();
            $bundles = Bundle::Recent()
                ->where('brand_id', Cookie::get('brandId'))
                ->where(function ($q) use ($banner) {
                    if (! $banner->bundle_id && $banner->redirect_type == 3) {
                        $q->where('id', $banner->bundle_id);
                    }
                })
                ->take(5)
                ->get();
        }

        if (Cookie::get('brandId') && Cookie::get('brandId') != '') {
            $brands = $brands->where('id', Cookie::get('brandId'));
        }

        $brands = $brands->get();

        return Inertia::render('Brands/Banners/Edit', [
            'data' => $data,
            'branches' => $branches,
            'brands' => $brands,
            'coupons' => $coupons,
            'bundles' => $bundles,
        ]);
    }

    public function show(Banner $banner)
    {
        $banner = Banner::where('id', $banner->id)
            ->with('branch', 'offer')
            ->first();
        date_default_timezone_set('Asia/Riyadh');
        $time = date('Y-m-d');
        $data = ['item' => $banner, 'title' => $banner->title.' Info'];

        return Inertia::render('Brands/Banners/Show', [
            'data' => $data,
            'now' => $time,
        ]);
    }

    public function update(BannerRequest $request, $id)
    {
        $banner = Banner::where('id', $id)->first();
        $data = $request->all();
        $data['en']['redirect_url'] = $request->redirect_url_en;
        $data['ar']['redirect_url'] = $request->redirect_url_ar;
        $data['brand_id'] = $data['brand_id'] == '' ? null : $data['brand_id'];
        $folder = $data['brand_id'] ?? 'home';
        if ($request->key_ar) {
            $path = 'banners/'.$folder.'/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar.'.'.explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar.'.png';
            $data['ar']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'banners/'.$folder.'/en/';
            $name = $request->content_type_en
                ? $request->uuid_en.'.'.explode('/', $request->content_type_en)[1]
                : $request->uuid_en.'.png';
            $data['en']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }
        $banner->update($data);

        $pageTitle = __('theBanner');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('banners.index');
    }

    public function destroy(Request $request, $id)
    {
        Banner::find($id)->delete();
        $pageTitle = __('theBanner');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('banners.index');
    }
}
