<?php

namespace App\Http\Controllers;

use App\Helpers\Constant;
use App\Http\Requests\QuestionRequest;
use App\Models\Question\Question;
use Illuminate\Http\Request;
use Inertia\Inertia;

class QuestionsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:questions.index')->only(['index']);
        $this->middleware('checkPermission:questions.show')->only(['show']);
        $this->middleware('checkPermission:questions.edit')->only(['edit']);
        $this->middleware('checkPermission:questions.delete')->only(['destroy']);
        $this->middleware('checkPermission:questions.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $questions = Question::Faq()->Ordered();
        if (isset($search) && $search != '') {
            $questions = $questions->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhereTranslationLike('title', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $questions = $questions->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $questions = $questions->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $questions = $questions->where('active', $active);
        }
        $questions = $questions->paginate(env('PER_PAGE', 10))->withQueryString();

        return Inertia::render('Questions/Index', [
            'data' => $questions,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Questions/Create');
    }

    public function store(QuestionRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['type'] = Constant::FaqQuestion;

        Question::create($data);

        $pageTitle = __('theQuestion');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('questions.index');
    }

    public function edit(Question $question)
    {
        $data = ['question' => $question, 'title' => $question->title.' Edit'];

        return Inertia::render('Questions/Edit', ['data' => $data]);
    }

    public function show(Question $question)
    {
        $data = ['item' => $question, 'title' => $question->title.' Info'];

        return Inertia::render('Questions/Show', ['data' => $data]);
    }

    public function update(QuestionRequest $request, Question $question)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $question->update($data);

        $pageTitle = __('theQuestion');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('questions.index');
    }

    public function destroy(Request $request, $id)
    {
        $question = Question::find($id);
        $question->delete();

        $pageTitle = __('theQuestion');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('questions.index');
    }
}
