<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PaymentMethods\PaymentMethod;
use Inertia\Inertia;

class PaymentMethodsController extends Controller
{
    //index function
    public function index()
    {
        $paymentMethods = PaymentMethod::all();
        return Inertia::render('PaymentMethods/Index', [
            'data' => $paymentMethods,
        ]);
    }

    public function edit(PaymentMethod $paymentMethod)
    {
        if ($paymentMethod->configurable == 0) {
            return redirect()
                ->route('payment-methods.index')
                ->with('error', __('This payment method is not configurable'));
        }
        $iv = base64_decode($paymentMethod->encryption_key);

        // Decrypt the keys
        $paymentMethod->live_public_key = isset($paymentMethod->live_public_key)
            ? decryptThis($paymentMethod->live_public_key, base64_decode($paymentMethod->lpk_encryption_key))
            : null;
        $paymentMethod->live_secret_key = isset($paymentMethod->live_secret_key)
            ? decryptThis($paymentMethod->live_secret_key, base64_decode($paymentMethod->lsk_encryption_key))
            : null;
        $paymentMethod->sandbox_public_key = isset($paymentMethod->sandbox_public_key)
            ? decryptThis($paymentMethod->sandbox_public_key, base64_decode($paymentMethod->spk_encryption_key))
            : null;
        $paymentMethod->sandbox_secret_key = isset($paymentMethod->sandbox_secret_key)
            ? decryptThis($paymentMethod->sandbox_secret_key, base64_decode($paymentMethod->ssk_encryption_key))
            : null;
        return Inertia::render('PaymentMethods/Edit', [
            'paymentMethod' => $paymentMethod,
        ]);
    }

    public function update(Request $request, PaymentMethod $paymentMethod)
    {
        if ($paymentMethod->configurable == 0) {
            return redirect()
                ->route('payment-methods.index')
                ->with('error', __('This payment method is not configurable'));
        }
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'active' => 'required|boolean',
            'sandbox' => 'required|boolean',
            'sandbox_url' => 'nullable|string|url',
            'sandbox_public_key' => 'nullable|string',
            'sandbox_secret_key' => 'nullable|string',
            'live_url' => 'nullable|string|url',
            'live_public_key' => 'nullable|string',
            'live_secret_key' => 'nullable|string',
            'limit' => 'required|integer|min:0',
        ]);

        $key = '';
        if (isset($validated['live_public_key']) && isset($request->live_public_key)) {
            $live_public_key_encryption = encryptThis($validated['live_public_key']);
            $validated['live_public_key'] = $live_public_key_encryption['data'];
            $validated['lpk_encryption_key'] = base64_encode($live_public_key_encryption['iv']);
        } else {
            $validated['live_public_key'] = null;
            $validated['lpk_encryption_key'] = null;
        }
        if (isset($validated['live_secret_key']) && isset($request->live_secret_key)) {
            $live_secret_key_encryption = encryptThis($validated['live_secret_key']);
            $validated['live_secret_key'] = $live_secret_key_encryption['data'];
            $validated['lsk_encryption_key'] = base64_encode($live_secret_key_encryption['iv']);
        } else {
            $validated['live_secret_key'] = null;
            $validated['lsk_encryption_key'] = null;
        }
        if (isset($validated['sandbox_public_key']) && isset($request->sandbox_public_key)) {
            $sandbox_public_key_encryption = encryptThis($validated['sandbox_public_key']);
            $validated['sandbox_public_key'] = $sandbox_public_key_encryption['data'];
            $validated['spk_encryption_key'] = base64_encode($sandbox_public_key_encryption['iv']);
        } else {
            $validated['sandbox_public_key'] = null;
            $validated['spk_encryption_key'] = null;
        }
        if (isset($validated['sandbox_secret_key']) && isset($request->sandbox_secret_key)) {
            $sandbox_secret_key_encryption = encryptThis($validated['sandbox_secret_key']);
            $validated['sandbox_secret_key'] = $sandbox_secret_key_encryption['data'];
            $validated['ssk_encryption_key'] = base64_encode($sandbox_secret_key_encryption['iv']);
        } else {
            $validated['sandbox_secret_key'] = null;
            $validated['ssk_encryption_key'] = null;
        }

        $paymentMethod->update($validated);

        return redirect()
            ->route('payment-methods.index')
            ->with('success', __('Payment method updated successfully'));
    }
}