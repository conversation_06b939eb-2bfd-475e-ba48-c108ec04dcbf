<?php

namespace App\Http\Controllers\User;

use App\Helpers\Constant;
use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Inertia\Inertia;

class LogsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:logs.index')->only(['index']);
        $this->middleware('checkPermission:logs.show')->only(['show']);
        $this->middleware('checkPermission:logs.delete')->only(['destroy']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $userLog = $request->user;
        $action = $request->action;
        $module = $request->module;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $logs = ActivityLog::orderBy('created_at', 'DESC');
        if (isset($search) && $search != '') {
            $logs = $logs->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('causer_id', 'like', '%'.$search.'%');
                $q->orWhere('subject_id', 'like', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $logs = $logs->whereDate('created_at', '>=', $fromDate);
        }

        if (isset($toDate) && $toDate != '') {
            $logs = $logs->whereDate('created_at', '<=', $toDate);
        }
        if (isset($module) && $module != '') {
            $logs = $logs->where('subject_type', 'like', '%'.$module);
        }
        if (isset($userLog) && $userLog != '') {
            $logs = $logs->where('causer_id', $userLog);
        }

        if (isset($action) && $action != '') {
            $logs = $logs->where('event', $action);
        }

        $logs = $logs->paginate(env('PER_PAGE_LOG', 10))->withQueryString();

        $activityActions = ActivityLog::distinct('event')
            ->pluck('event')
            ->toArray();

        return Inertia::render('Log/Index', [
            'data' => $logs,
            'users' => [],
            'activityActions' => $activityActions,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'userLog' => $userLog,
            'action' => $action,
            'module' => $module,
        ]);
    }

    public function show($id)
    {
        $statuses = [];
        $paymentStatuses = [];
        $data = ['item' => ActivityLog::find($id)];
        if ($data['item']->subject_type == 'App\Models\Ordering\Order') {
            $paymentStatuses = Constant::PaymentStatus;
            $statuses = Constant::OrderStatus;
        }

        return Inertia::render('Log/Show', [
            'data' => $data,
            'statuses' => $statuses,
            'paymentStatuses' => $paymentStatuses,
        ]);
    }

    public function getAllLogModule()
    {
        $modules = ActivityLog::distinct('subject_type')
            ->get()
            ->pluck('subject')
            ->toArray();

        return array_unique($modules);
    }

    public function destroy(Request $request, $id)
    {
        $question = ActivityLog::find($id);
        $question->delete();
        $request->session()->flash('success', 'Log deleted successfully!');

        return redirect()->route('logs.index');
    }

    public function deleteAll(Request $request)
    {
        Schema::disableForeignKeyConstraints();
        ActivityLog::orderBy('id', 'ASC')->truncate();
        Schema::enableForeignKeyConstraints();

        $request->session()->flash('success', 'All Logs deleted successfully!');

        return redirect()->route('logs.index');
    }
}
