<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Helpers\Constant;
use Illuminate\Http\Request;
use App\Models\Question\Question;
use App\Http\Requests\QuestionRequest;
use App\Http\Requests\ReferralQuestionRequest;

class ReferralQuestionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:referral-questions.index')->only(['index']);
        $this->middleware('checkPermission:referral-questions.show')->only(['show']);
        $this->middleware('checkPermission:referral-questions.edit')->only(['edit']);
        $this->middleware('checkPermission:referral-questions.delete')->only(['destroy']);
        $this->middleware('checkPermission:referral-questions.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $questions = Question::Referral()->Ordered();
        if (isset($search) && $search != '') {
            $questions = $questions->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $questions = $questions->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $questions = $questions->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $questions = $questions->where('active', $active);
        }
        $questions = $questions->paginate(15)->withQueryString();

        return Inertia::render('Questions/Referral/Index', [
            'data' => $questions,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Questions/Referral/Create');
    }

    public function store(QuestionRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['type'] = Constant::ReferralQuestion;
        Question::create($data);

        $pageTitle = __('theQuestion');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('referral-questions.index');
    }

    public function edit(Question $referral_question)
    {
        $data = ['referral_question' => $referral_question, 'title' => $referral_question->title . ' Edit'];

        return Inertia::render('Questions/Referral/Edit', ['data' => $data]);
    }

    public function show(Question $referral_question)
    {
        $data = ['item' => $referral_question, 'title' => $referral_question->title . ' Info'];

        return Inertia::render('Questions/Referral/Show', ['data' => $data]);
    }

    public function update(ReferralQuestionRequest $request, Question $referral_question)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $referral_question->update($data);

        $pageTitle = __('theQuestion');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('referral-questions.index');
    }

    public function destroy(Request $request, $id)
    {
        $question = Question::find($id);
        $question->delete();

        $pageTitle = __('theQuestion');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('referral-questions.index');
    }
}