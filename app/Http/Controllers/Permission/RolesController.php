<?php

namespace App\Http\Controllers\Permission;

use App\Http\Controllers\Controller;
use App\Http\Requests\RoleRequest;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Permission\Permission;
use App\Models\Permission\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Inertia;

class RolesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:roles.index')->only(['index']);
        $this->middleware('checkPermission:roles.show')->only(['show']);
        $this->middleware('checkPermission:roles.edit')->only(['edit']);
        $this->middleware('checkPermission:roles.delete')->only(['destroy']);
        $this->middleware('checkPermission:roles.create')->only(['create']);
        $this->middleware('checkPermission:roles.manageUser')->only(['manageUser']);
        $this->middleware('checkPermission:roles.assignUser')->only(['assignUser']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $roles = Role::orderBy('id', 'DESC');

        if (isset($search) && $search != '') {
            $roles = $roles->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('name', 'like', '%'.$search.'%');
                $q->orWhereHas('users', function ($q1) use ($search) {
                    $q1->where('name', 'like', '%'.$search.'%');
                    $q1->orWhere('email', 'like', '%'.$search.'%');
                    $q1->orWhere('mobile', 'like', '%'.$search.'%');
                });
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $roles = $roles->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $roles = $roles->whereDate('created_at', '<=', $toDate);
        }

        $roles = $roles->paginate(env('PER_PAGE', 10));

        return Inertia::render('Permission/Roles/Index', [
            'data' => $roles,
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        // get main permission
        $mainPermissions = Permission::Parent()->get();
        $brand_ids = unserialize(Cookie::get('role_brands'));

        $brands = Brand::Publish()
            ->where(function ($q) use ($brand_ids) {
                if (count($brand_ids) > 0) {
                    $q->whereIn('id', $brand_ids);
                }
            })
            ->get();

        return Inertia::render('Permission/Roles/Create', [
            'mainPermissions' => $mainPermissions,
            'brands' => $brands,
            'branches' => [],
        ]);
    }

    public function store(RoleRequest $request)
    {
        $data = $request->all();
        $role = Role::create($data);
        $permissions = array_values(array_filter($request->permissions));
        $brands = $request['brands'];
        $branches = $request['branches'];
        if (! empty($permissions)) {
            $role->permissions()->sync($permissions);
        }
        if (! empty($brands)) {
            $role->brands()->sync($brands);
        } else {
            Cookie::queue('role_brands', '');
        }

        if (! empty($branches)) {
            $role->branches()->sync($branches);
        }

        $pageTitle = __('theUserGroup');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('roles.index');
    }

    public function edit(Role $role)
    {
        $dataBranches = [];
        $mainPermissions = Permission::Parent()->get();
        $brand_ids = unserialize(Cookie::get('role_brands'));

        $brands = Brand::Publish()
            ->where(function ($q) use ($brand_ids) {
                if (count($brand_ids) > 0) {
                    $q->whereIn('id', $brand_ids);
                }
            })
            ->get();
        $branches = Branch::whereIn('id', $role->branches_ids)
            ->with('brand')
            ->get();

        foreach ($branches as $branch) {
            $title_en = (! empty($branch->brand) ? $branch->brand->title_en.' - ' : '').$branch->title_en;
            $title_ar = (! empty($branch->brand) ? $branch->brand->title_ar.' - ' : '').$branch->title_ar;
            $item = ['title_en' => $title_en, 'title_ar' => $title_ar, 'id' => $branch->id];
            array_push($dataBranches, $item);
        }

        $data = ['role' => $role, 'title' => $role->title.' Edit'];

        return Inertia::render('Permission/Roles/Edit', [
            'data' => $data,
            'mainPermissions' => $mainPermissions,
            'brands' => $brands,
            'branches' => $dataBranches,
        ]);
    }

    public function manageUser($roleId)
    {
        $role = Role::find($roleId);
        $users = User::where('active', 1)
            ->take(10)
            ->get();
        $role->users_ids =
            ! empty($role->users) && count($role->users) > 0
            ? $role
                ->users()
                ->pluck('id')
                ->toArray()
            : [];

        $data = ['role' => $role, 'title' => $role->title.' Manage User'];

        return Inertia::render('Permission/Roles/Manage', [
            'data' => $data,
            'users' => $users,
        ]);
    }

    public function assignUser(Request $request, $roleId)
    {
        $role = Role::find($roleId);

        $role->users()->sync($request->users);

        $pageTitle = __('theUserGroup');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('roles.index');
    }

    public function show($id)
    {
        $role = Role::where('id', $id)
            ->with('list_brands', 'list_branches')
            ->first();

        $mainPermissions = Permission::Parent()->get();
        $data = ['item' => $role, 'title' => $role->title.' Info'];

        return Inertia::render('Permission/Roles/Show', ['data' => $data, 'mainPermissions' => $mainPermissions]);
    }

    public function update(RoleRequest $request, Role $role)
    {
        $data = $request->all();
        $role->update($data);
        $permissions = array_values(array_filter($request->permissions));
        $brands = $request['brands'];
        $branches = $request['branches'];
        $role->brands()->sync($brands);
        $role->branches()->sync($branches);
        $role->permissions()->sync($permissions);

        $pageTitle = __('theUserGroup');
        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('roles.index');
    }

    public function destroy(Request $request, $id)
    {
        $role = Role::find($id);
        $role->delete();
        $pageTitle = __('theUserGroup');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('roles.index');
    }
}
