<?php

namespace App\Http\Controllers\Invitation;

use App\Http\Controllers\Controller;
use App\Http\Requests\Invitation\FriendRequest;
use App\Models\Invitation\Friend;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class FriendsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:friends.index')->only(['index']);
        $this->middleware('checkPermission:friends.show')->only(['show']);
        $this->middleware('checkPermission:friends.edit')->only(['edit']);
        $this->middleware('checkPermission:friends.delete')->only(['destroy']);
        $this->middleware('checkPermission:friends.create')->only(['create']);
    }

    public function index(Request $request)
    {
        //Friends
        $search = $request->search;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;
        $status = $request->status;
        $id = $request->id;

        $items = Friend::Recent()->with('user', 'sender');

        if (isset($search) && $search != '') {
            $items = $items->whereHas('user', function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%');
                $q->orWhere('email', '%'.$search.'%');
                $q->orWhere('mobile', '%'.$search.'%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $items = $items->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $items = $items->whereDate('created_at', '<=', $toDate);
        }

        if (isset($id) && $id != '') {
            if (isset($status) && $status != '') {
                if ($status == 1) {
                    $items = $items->whereStatus(1)->Friend($id);
                } elseif ($status == 0) {
                    $items = $items->pending()->where('sender_id', $id);
                } else {
                    //received
                    $items = $items->pending()->where('user_id', $id);
                }
            } else {
                $items = $items->Friend($id);
            }
        } else {
            if (isset($status) && $status != '') {
                $items = $items->whereStatus($status);
            }
        }

        $items = $items->paginate(env('PER_PAGE', 10));

        return Inertia::render('Invitation/Friend/Index', [
            'data' => $items->withQueryString(),
            'search' => $search,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
            'status' => $status,
        ]);
    }

    public function create()
    {
        return Inertia::render('Invitation/Friend/Create');
    }

    public function store(FriendRequest $request)
    {
        $data = $request->all();

        $friend = Friend::create($data);

        $request->session()->flash('success', 'Friend created successfully!');

        return redirect()->route('friends.index');
    }

    public function edit(Friend $friend)
    {
        $data = [
            'friend' => $friend,
            'title' => $friend->name.' Edit',
        ];

        return Inertia::render('Invitation/Friend/Edit', [
            'data' => $data,
        ]);
    }

    public function show($id)
    {
        $friend = Friend::where('id', $id)
            ->with('user', 'sender')
            ->first();
        $data = [
            'item' => $friend,
            'title' => $friend->title.' Info',
        ];

        return Inertia::render('Invitation/Friend/Show', [
            'data' => $data,
        ]);
    }

    public function update(FriendRequest $request, Friend $friend)
    {
        $data = $request->all();
        try {
            DB::beginTransaction();
            $friend->update($data);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        $request->session()->flash('success', 'Friend updated successfully!');

        return redirect()->route('friends.index');
    }

    public function destroy(Request $request, $id)
    {
        Friend::find($id)->delete();
        $request->session()->flash('success', 'Friend deleted successfully!');

        return redirect()->route('friends.index');
    }
}
