<?php

namespace App\Http\Controllers\Invitation;

use App\Http\Controllers\Controller;
use App\Http\Requests\Invitation\FamilyRelationRequest;
use App\Models\Invitation\FamilyRelation;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FamilyRelationsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:family-relations.index')->only(['index']);
        $this->middleware('checkPermission:family-relations.show')->only(['show']);
        $this->middleware('checkPermission:family-relations.edit')->only(['edit']);
        $this->middleware('checkPermission:family-relations.delete')->only(['destroy']);
        $this->middleware('checkPermission:family-relations.create')->only(['create']);
    }

    public function index(Request $request)
    {
        $search = $request->search;
        $active = $request->active;
        $fromDate = $request->fromDate;
        $toDate = $request->toDate;

        $relations = FamilyRelation::Ordered()->Recent();

        if (isset($search) && $search != '') {
            $relations = $relations->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%');
                $q->orWhereTranslationLike('title', '%' . $search . '%');
            });
        }
        if (isset($fromDate) && $fromDate != '') {
            $relations = $relations->whereDate('created_at', '>=', $fromDate);
        }
        if (isset($toDate) && $toDate != '') {
            $relations = $relations->whereDate('created_at', '<=', $toDate);
        }
        if (isset($active) && $active != '') {
            $relations = $relations->where('active', $active);
        }
        $relations = $relations->paginate(5)->withQueryString();

        return Inertia::render('Invitation/FamilyRelation/Index', [
            'data' => $relations,
            'search' => $search,
            'active' => $active,
            'fromDate' => $fromDate,
            'toDate' => $toDate,
        ]);
    }

    public function create()
    {
        return Inertia::render('Invitation/FamilyRelation/Create');
    }

    public function store(FamilyRelationRequest $request)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;
        $relation = FamilyRelation::create($data);

        $pageTitle = __('theFamilyRelation');
        $request->session()->flash('success', __('CreateSuccess', ['title' => $pageTitle]));

        return redirect()->route('family-relations.index');
    }

    public function edit(FamilyRelation $family_relation)
    {
        return Inertia::render('Invitation/FamilyRelation/Edit', [
            'data' => ['family_relation' => $family_relation, 'title' => $family_relation->title . ' Edit'],
        ]);
    }

    public function show(FamilyRelation $family_relation)
    {
        $data = ['family_relation' => $family_relation, 'title' => $family_relation->title . ' Info'];

        return Inertia::render('Invitation/FamilyRelation/Show', ['data' => $data]);
    }

    public function update(FamilyRelationRequest $request, FamilyRelation $family_relation)
    {
        $data = $request->all();
        $data['en']['title'] = $request->title_en;
        $data['ar']['title'] = $request->title_ar;

        $family_relation->update($data);
        $pageTitle = __('therelation');

        $request->session()->flash('success', __('UpdateSuccess', ['title' => $pageTitle]));

        return redirect()->route('family-relations.index');
    }

    public function destroy(Request $request, $id)
    {
        $relation = FamilyRelation::find($id);

        $relation->delete();

        $pageTitle = __('therelation');
        $request->session()->flash('success', __('DeleteSuccess', ['title' => $pageTitle]));

        return redirect()->route('family-relations.index');
    }
}
