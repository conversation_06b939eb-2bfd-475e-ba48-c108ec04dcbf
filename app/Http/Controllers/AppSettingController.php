<?php

namespace App\Http\Controllers;

use App\Http\Requests\AppSettingRequest;
use App\Models\AppSetting\AppSetting;
use App\Models\AppSetting\Region\Region;
use App\Models\AppSetting\SocialLink\SocialLink;
use App\Models\PaymentMethods\PaymentMethod;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class AppSettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkPermission:app-settings.index')->only(['index']);
        $this->middleware('checkPermission:app-settings.show')->only(['show']);
        $this->middleware('checkPermission:app-settings.edit')->only(['update']);
    }

    public function index()
    {
        $setting = AppSetting::first();
        $regionPhoneItems = Region::get();
        $socialLinkItems = SocialLink::whereNull('brand_id')->get();
        $payments = PaymentMethod::where('external', 1)
            ->where('can_update', 1)
            ->get();

        $updatedPayment = PaymentMethod::where('external', 1)
        ->where('editable', 1)
        ->first();

        return Inertia::render('AppSetting/Edit', [
            'data' => ['setting' => $setting],
            'regionPhoneItems' => $regionPhoneItems,
            'socialLinkItems' => $socialLinkItems,
            'payments' => $payments,
            'updatedPayment' => $updatedPayment,
            'active_payment' => PaymentMethod::where('external', 1)
                ->where('can_update', 1)
                ->where('active', 1)
                ->first()->id ?? '',
        ]);
    }

    public function update(AppSettingRequest $request, $id)
    {
        $setting = AppSetting::where('id', $id)->first();
        $data = $request->all();

        if (isset($data['active_payment'])) {
            PaymentMethod::where('external', 1)->update(['active' => 0]);
            PaymentMethod::where('external', 1)
                ->where('id', $data['active_payment'])
                ->update(['active' => 1]);
        }

        // updatedPayment{
        if (isset($data['updatedPayment'])) {
            PaymentMethod::where('external', 1)
                ->where('id', $data['updatedPayment']['id'])
                ->update(['active' => $data['updatedPayment']['active'], 'limit' => $data['updatedPayment']['limit']]);
        }
        $data['en']['description'] = $request->description_en;
        $data['ar']['description'] = $request->description_ar;
        $data['en']['terms_condition'] = $request->terms_condition_en;
        $data['ar']['terms_condition'] = $request->terms_condition_ar;
        if ($request->logo_key) {
            $path = 'logo/';
            $name = $request->logo_content_type
                ? $request->logo_uuid.'.'.explode('/', $request->logo_content_type)[1]
                : $request->logo_uuid.'.png';
            $data['logo'] = $path.$name;
            Storage::disk('s3')->copy($request->logo_key, $data['logo'], 'public');
        }
        if ($request->favicon_key) {
            $path = 'favicon/';
            $name = $request->favicon_content_type
                ? $request->favicon_uuid.'.'.explode('/', $request->favicon_content_type)[1]
                : $request->favicon_uuid.'.png';
            $data['favicon'] = $path.$name;
            Storage::disk('s3')->copy($request->favicon_key, $data['favicon'], 'public');
        }

        if ($request->key_ar) {
            $path = 'about/ar/';
            $name = $request->content_type_ar
                ? $request->uuid_ar.'.'.explode('/', $request->content_type_ar)[1]
                : $request->uuid_ar.'.png';
            $data['ar']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_ar, $data['ar']['image'], 'public');
        }

        if ($request->key_en) {
            $path = 'about/en/';
            $name = $request->content_type_en
                ? $request->uuid_en.'.'.explode('/', $request->content_type_en)[1]
                : $request->uuid_en.'.png';
            $data['en']['image'] = $path.$name;
            Storage::disk('s3')->copy($request->key_en, $data['en']['image'], 'public');
        }
        $setting->update($data);
        if (!empty($data['regionPhoneItems'])) {
            $this->saveRegionPhone($data['regionPhoneItems']);
        }

        if (!empty($data['socialLinkItems'])) {
            $this->saveSocialLink($data['socialLinkItems']);
        }
        $request->session()->flash('success', 'App Setting updated successfully!');

        return redirect()->route('app_settings.index');
    }

    public function saveRegionPhone($regionPhoneItems)
    {
        Region::where('id', '>', 0)->delete();
        foreach ($regionPhoneItems as $item) {
            $data['ar']['title'] = $item['title_ar'];
            $data['en']['title'] = $item['title_en'];
            $data['phone'] = $item['phone'];
            Region::create($data);
        }
    }

    public function saveSocialLink($socialLinkItems)
    {
        $links = [];
        SocialLink::AppSetting()->delete();
        foreach ($socialLinkItems as $item) {
            $data['ar']['title'] = $item['title_ar'];
            $data['en']['title'] = $item['title_en'];
            $data['link'] = $item['link'];
            $data['icon'] = $item['icon'];
            $data['color'] = $item['color'];

            // image
            SocialLink::AppSetting()->updateOrCreate(['link' => $item['link']], $data);
            array_push($links, $item['link']);
        }
    }
}
