<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;

class ReferralController extends Controller
{
    public function getAppLink()
    {
        $iPod = strpos($_SERVER['HTTP_USER_AGENT'], 'iPod');
        $mac = strpos($_SERVER['HTTP_USER_AGENT'], 'Mac');
        $iPhone = strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone');
        $iPad = strpos($_SERVER['HTTP_USER_AGENT'], 'iPad');
        $macintosh = strpos($_SERVER['HTTP_USER_AGENT'], 'Macintosh');

        if ($iPad || $iPhone || $iPod || $mac || $macintosh) {
            return redirect(config('app.download_app_ios'));
        } else {
            return redirect(config('app.download_app_android'));
        }
    }
}
