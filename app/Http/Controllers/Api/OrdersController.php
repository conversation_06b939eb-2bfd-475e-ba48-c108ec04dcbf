<?php

namespace App\Http\Controllers\Api;

use App\Events\GroupSpendingData;
use App\Events\OrderNotification;
use App\Events\ZatcaCreateInvoice;
use App\Http\Controllers\Controller;
use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Models\Ordering\PaymentStatusLog;
use App\Models\Ordering\ZatcaInvoice;
use App\Models\PaymentMethods\PaymentMethod;
use App\Models\Wallet\SalaCreditRule;
use App\Models\Wallet\WalletTransaction;
use App\Scopes\NonDraftScope;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

class OrdersController extends Controller
{
    public function returnPaymentSuccess()
    {
        return view('payment-return');
    }

    public function returnPaymentFailure()
    {
        return view('payment-return');
    }

    private function SuccessPayment($order)
    {
        $payment_status = 1;
        // booking order notification

        if ($order->old_coupon_id != null) {
            $order->update([
                'old_coupon_id' => null,
                'coupon_id' => $order->old_coupon_id,
            ]);
        }

        // check if the order is offer order
        // then make is draft 0 for all its sub orders too

        if ($order->offer_id != null) {
            $subOrders = $order->offer_sub_orders;
            if (empty($subOrders) || count($subOrders) == 0) {
                $subOrders = $order->draft_offer_sub_orders;
            }
            foreach ($subOrders as $subOrder) {
                $subOrder->update(['is_draft' => '0']);
            }
        }
        // if payment success then make this order non draft
        $order->update(['is_draft' => '0']);
        event(new OrderNotification($order, 4));

        event(new GroupSpendingData($order->id));
    }

    private function FailPayment($order)
    {
        if ($order->is_draft == '0') {
            // failed
            $payment_status = 2;
            // check if coupon exist
            $checkPayment = Payment::where('order_id', $order->id)
                ->where('method_type', '1')
                ->first();
            if ($order->coupon_id != null) {
                $order->update([
                    'old_coupon_id' => $order->coupon_id,
                    'coupon_id' => null,
                    'to_be_paid' => $checkPayment ? $checkPayment->amount : $order->to_be_paid,
                ]);
            }
        }
    }

    // tamara payment gateway webhook
    public function tamaraWebhook($request, $payment, $payment_status)
    {
        // tamara payment gateway webhook

        /*{
            "order_id": "4fdb781f-5e13-4ae2-9dc6-3ee49e3878a3",
            "order_reference_id": "4464602579098",
            "order_number": "90001860",
            "event_type": "order_approved",
            "data": []
        }*/
        $order = Order::withoutGlobalScope(NonDraftScope::class)
            ->where('id', $request->order_reference_id)
            ->first();

        $paymentStatusToNumbers = [
            'order_approved' => 1,
            'order_refunded' => 3,
        ];

        if (! empty($order) && $order->payment_status != 1 && ! empty($paymentStatusToNumbers[$request->event_type])) {
            // && $request->response_message == 'Success'
            if ($request->event_type == 'order_approved') {
                if ($this->fullyCaptureOrder($order, $request->order_id)) {
                    $this->SuccessPayment($order);
                }
            }

            $order->update([
                'fort_id' => $request->order_id,
                'merchant_reference' => $request->order_number,
                'fort_status' => $request->event_type,
                'payment_status' => $paymentStatusToNumbers[$request->event_type],
                'digital_wallet' => isset($request->digital_wallet) ? 'APPLE_PAY' : '',
            ]);
        }

        if (! empty($order) && ! empty($paymentStatusToNumbers[$request->event_type])) {
            // PaymentStatusLog
            $log = PaymentStatusLog::updateOrCreate(
                [
                    'fort_id' => $request->order_id,
                    'order_id' => $order->id,
                    'fort_status' => $request->event_type,
                ],
                [
                    'fort_id' => $request->order_id,
                    'order_id' => $order->id,
                    'merchant_reference' => $request->order_number,
                    'fort_status' => $request->event_type,
                    'payment_status' => $payment_status,
                    'data' => serialize($request->all()),
                ]
            );

            // the webhook must set the payements->status as well as payment_status_log->payment_status

            // add payment only on first try to $
            // then update it with the webhook for first pay
            // or even on the repay

            // add payment record only if order is non draft
            // to skip card failure
            $isRefund = 0;
            if ($request->event_type == 'order_refunded') {
                $isRefund = 1;
            }
            if ($order->is_draft == '0') {
                Payment::updateOrCreate(
                    [
                        'order_id' => $order->id,
                        'method_type' => '2',
                        'user_id' => $order->user_id,
                        'is_refund' => $isRefund,
                    ],
                    [
                        'method_type' => '2',
                        'status' => $paymentStatusToNumbers[$request->event_type],
                        'method_id' => $log->id,
                        'amount' => $order->to_be_paid ?? $order->total_price,
                        'reference_number' => $this->generateUniqueReferenceNumber(),
                    ]
                );
            }
        }
    }

    public function fullyCaptureOrder($order, $order_id)
    {
        $payment = PaymentMethod::where('name', 'Tamara')->first();
        if (! empty($payment)) {
            $url = $payment->sandbox_url;
            $tamara_secret = $payment->sandbox_secret_key;
        } else {
            $url = Config::get('app.tamara_url');
            $tamara_secret = Config::get('app.tamara_secret');
        }
        $order = Order::where('id', $order->id)->withoutGlobalScope(NonDraftScope::class)->first();

        if (! empty($order)) {
            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => $url.'/orders/'.$order_id.'/authorise',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode([]), // or '{}' as before
                CURLOPT_HTTPHEADER => [
                    'Accept: application/json',
                    'Authorization: Bearer '.$tamara_secret,
                ],
            ]);

            $response = curl_exec($curl);

            curl_close($curl);

            $result = json_decode($response, true);

            PaymentStatusLog::create([
                'order_id' => $order->id,
                'data' => json_encode($result),
            ]);
            if (isset($result['status']) && $result['status'] == 'fully_captured') {
                return true;
            }

            return false;
        } else {
            PaymentStatusLog::create([
                'data' => 'outer : order not found',
            ]);
        }
    }

    public function payfortWebhook($request, $payment, $payment_status)
    {
        if ($request->merchant_extra1 == 'wallet') {
            $payment = Payment::where('id', $request->merchant_extra1)
                ->orWhere('id', $request->merchant_reference)
                ->orWhere('id', $request->merchant_extra)
                ->first();
        } else {
            // get the order withoug global scope
            $order = Order::withoutGlobalScope(NonDraftScope::class)
                ->where('id', $request->merchant_extra1)
                ->orWhere('id', $request->merchant_reference)
                ->orWhere('id', $request->merchant_extra)
                ->first();
        }

        if (! empty($order) && $request->command == 'PURCHASE' && $order->payment_status != 1) {
            // && $request->response_message == 'Success'
            if ($request->status == '14') {
                $payment_status = 1;
                $this->SuccessPayment($order);
            } else {
                $payment_status = 2;
                $this->FailPayment($order);
            }
            if ($order->is_draft == '0') {
                $order->update([
                    'fort_id' => $request->fort_id,
                    'merchant_reference' => $request->merchant_reference,
                    'fort_status' => $request->status,
                    'payment_status' => $payment_status,
                    'digital_wallet' => isset($request->digital_wallet) ? 'APPLE_PAY' : '',
                ]);
            }
            if ($payment_status == 1) {
                event(new ZatcaCreateInvoice($order->id));
            }
        }
        if (! empty($order)) {
            // PaymentStatusLog
            $log = PaymentStatusLog::updateOrCreate(
                [
                    'fort_id' => $request->fort_id,
                    'order_id' => $order->id,
                    'fort_status' => $request->status,
                ],
                [
                    'fort_id' => $request->fort_id,
                    'order_id' => $order->id,
                    'merchant_reference' => $request->merchant_reference,
                    'fort_status' => $request->status,
                    'payment_status' => $payment_status,
                    'data' => serialize($request->all()),
                ]
            );
            // the webhook must set the payements->status as well as payment_status_log->payment_status
            // the webhook must set the payements->status as well as payment_status_log->payment_status

            // add payment only on first try to $
            // then update it with the webhook for first pay
            // or even on the repay

            // add payment record only if order is non draft
            // to skip card failure

            if ($order->is_draft == '0') {
                Payment::updateOrCreate(
                    [
                        'order_id' => $order->id,
                        'method_type' => '1',
                        'user_id' => $order->user_id,
                    ],
                    [
                        'status' => $payment_status,
                        'method_id' => $log->id,
                        'amount' => $order->to_be_paid ?? $order->total_price,
                        'reference_number' => $this->generateUniqueReferenceNumber(),
                    ]
                );
            }
        }

        if (! empty($payment)) {
            $payment_status = null;
            $transaction = null;
            if ($request->command == 'PURCHASE' && $payment->status != 1) {
                if ($request->status == '14') {
                    $payment_status = 1;
                    // create transaction
                    //
                    $salaCreditRules = SalaCreditRule::find(1);
                    $arr = [];
                    $arr['transaction_type'] = '1';
                    $arr['transaction_amount'] =
                        ($payment->amount * $salaCreditRules->charging_coins) / $salaCreditRules->charging_money;
                    $arr['transaction_reason'] = 'Sala credit charge';
                    $arr['user_id'] = $payment->user_id;
                    $arr['payment_id'] = $payment->id;
                    $arr['currency_id'] = 1;
                    $transaction = WalletTransaction::firstOrCreate(
                        ['payment_id' => $payment->id, 'user_id' => $payment->user_id],
                        $arr
                    );
                } else {
                    // failed
                    // failed
                    $payment_status = 2;
                }
                $payment->update([
                    'status' => $payment_status,
                    'transaction_id' => ! empty($transaction) ? $transaction->id : null,
                ]);
            }

            PaymentStatusLog::updateOrCreate(
                [
                    'fort_id' => $request->fort_id,
                    'transaction_id' => ! empty($transaction) ? $transaction->id : null,
                    'payment_id' => $payment->id,
                    'merchant_reference' => $request->merchant_reference,
                    'fort_status' => $request->status,
                    'payment_status' => $payment_status,
                ],
                [
                    'fort_id' => $request->fort_id,
                    'transaction_id' => ! empty($transaction) ? $transaction->id : null,
                    'payment_id' => $payment->id,
                    'merchant_reference' => $request->merchant_reference,
                    'fort_status' => $request->status,
                    'payment_status' => $payment_status,
                    'data' => serialize($request->all()),
                ]
            );
        }
    }

    public function updatePayment($request)
    {
        $order = null;
        $payment = null;
        $payment_status = null;
        if ($request->merchant_extra1) {
            $this->payfortWebhook($request, $payment, $payment_status);
        } elseif ($request->order_reference_id) {
            $this->tamaraWebhook($request, $payment, $payment_status);
        }
    }

    public function returnPayment(Request $request)
    {
        if ($request->method == 'GET') {
            return true;
        }
        $this->updatePayment($request);

        if ($request->status == '14' || $request->event_type == 'order_approved') {
            return redirect()->route('api.payment.success');
        } else {
            return redirect()->route('api.payment.failure');
        }
    }

    public function update(Request $request)
    {
        $this->updatePayment($request);

        return $request->all();
    }

    public function generateInvoice(Request $request, $locale, $uuid)
    {
        if ($locale == 'ar') {
            App::setLocale($locale);
        }

        $order = Order::with('offer')
            ->where('order_uuid', $uuid)
            ->first();

        if (! $order) {
            return response()->json(['error' => 'Order not found'], 404);
        }

        $zatcak = ZatcaInvoice::where('order_id', $order->id)->first();
        if (! $zatcak || ($zatcak && ! $zatcak->request_payload)) {
            return response()->json(['error' => 'Zatca invoice not generated yet'], 404);
        }

        $xml_string = trim($zatcak->request_payload);
        $xml = simplexml_load_string($xml_string);

        $namespaces = $xml->getNamespaces(true); // Get all namespaces
        foreach ($namespaces as $prefix => $ns) {
            $xml->registerXPathNamespace($prefix, $ns);
        }
        // Extract all InvoiceLine elements
        $invoiceLines = $xml->xpath('//cac:InvoiceLine');
        // LegalMonetaryTotal
        $result = [];

        foreach ($invoiceLines as $line) {
            // ✅ Extract TaxAmount
            $tax_total_node = $line->xpath('cac:TaxTotal/cbc:TaxAmount');
            $tax_total = ! empty($tax_total_node) ? (string) $tax_total_node[0] : 'N/A';

            // ✅ Extract RoundingAmount Safely
            $rounding_amount_node = $line->xpath('cac:TaxTotal/cbc:RoundingAmount');
            $rounding_amount = ! empty($rounding_amount_node) ? (string) $rounding_amount_node[0] : 'N/A';
            $id = (string) $line->xpath('cbc:ID')[0];
            $type = (string) $line->xpath('cbc:Note')[0];
            if ($type == 'addon') {
                $addon = $order
                    ->addons()
                    ->where('addon_id', $id)
                    ->first();
                $result[] = [
                    'ID' => (string) $line->xpath('cbc:ID')[0],
                    'quantity' => (string) $line->xpath('cac:Price/cbc:BaseQuantity')[0],
                    'price' => (string) $line->xpath('cac:Price/cbc:PriceAmount')[0],
                    'LineExtensionAmount' => (string) $line->xpath('cbc:LineExtensionAmount')[0],
                    'taxTotal' => $tax_total,
                    'total_price' => $rounding_amount,
                    'title_en' => ! empty($addon->addon) ? $addon->addon->title_en : (string) $line->xpath('cac:Item/cbc:Name')[0],
                    'title_ar' => ! empty($addon->addon) ? $addon->addon->title_ar : (string) $line->xpath('cac:Item/cbc:Name')[0],
                ];
            } else {
                $ticket = $order
                    ->tickets()
                    ->where('ticket_id', $id)
                    ->first();
                $result[] = [
                    'ID' => (string) $line->xpath('cbc:ID')[0],
                    'quantity' => (string) $line->xpath('cac:Price/cbc:BaseQuantity')[0],
                    'price' => (string) $line->xpath('cac:Price/cbc:PriceAmount')[0],
                    'LineExtensionAmount' => (string) $line->xpath('cbc:LineExtensionAmount')[0],
                    'taxTotal' => $tax_total,
                    'total_price' => $rounding_amount,
                    'title_en' => ! empty($ticket) ? $ticket->title_en : (string) $line->xpath('cac:Item/cbc:Name')[0],
                    'title_ar' => ! empty($ticket) ? $ticket->title_ar : (string) $line->xpath('cac:Item/cbc:Name')[0],
                ];
            }
        }

        // Print the extracted data

        $coupon_discount = $order->coupon_discount;
        $price = $order->price_attr;

        if (! $order->parent_offer_order_id && $order->offer_id) {
            // get offer price
            // $order -> subOrders-> tickets
            $price = 0;

            foreach ($order->offer_sub_orders as $subOrder) {
                foreach ($subOrder->tickets as $ticket) {
                    $price += $ticket->total_price;
                }
            }

            $coupon_discount = $order->total_price - $price;
        }

        $date_ar = date('Y/n/j', strtotime($order->order_date));
        $date_en = date('j/n/Y', strtotime($order->order_date));
        // if ($order->offer) {
        //     if ($order->parent_offer_order_id != null) {
        //         $order = $order->parentOfferOrder;
        //     }
        //     $tickets = [];
        //     foreach ($order->offer_sub_orders as $subOrder) {
        //         $tickets = array_merge(
        //             $tickets,
        //             $subOrder
        //                 ->tickets()
        //                 ->get()
        //                 ->toArray()
        //         );
        //     }
        //     $totalTicketsPrices = 0;
        //     foreach ($tickets as &$ticket) {
        //         $ticket = (object) $ticket;
        //         $ticket_before_vat = round($ticket->price / 1.15);
        //         $ticket->ticket_before_vat = $ticket_before_vat;
        //         $ticket->vat_amount = $ticket->price - $ticket_before_vat;
        //         $ticket->total_with_vat = $ticket->price;
        //         $totalTicketsPrices += $ticket->price;
        //     }

        //     $total_price_before_vat = round($order->price / 1.15);
        //     $total_after_discount =
        //         $total_price_before_vat > $order->coupon_discount
        //             ? $total_price_before_vat - $order->coupon_discount
        //             : $total_price_before_vat;
        //     $vat_amount = $order->price - $total_price_before_vat;
        //     $date_ar = date('Y/n/j', strtotime($order->order_date));
        //     $date_en = date('j/n/Y', strtotime($order->order_date));

        //     $data = [
        //         'locale' => $locale,
        //         'invoice_number' => $order->id,
        //         'qrCode' => $order->invoice_qr_code,
        //         'date' => $locale == 'ar' ? $date_ar : $date_en,
        //         'vat_number' => '',
        //         'total_before_discount' => $total_price_before_vat,
        //         'discount' => $order->coupon_discount,
        //         'total_after_discount' => $total_after_discount,
        //         'vat_amount' => $vat_amount,
        //         'total_with_vat' => $total_after_discount + $vat_amount,
        //         'tickets' => $tickets,
        //         'coupon_name' => $order->coupon ? $order->coupon->code : '',
        //         'totalTicketsPrices' => $totalTicketsPrices,
        //         'offer' => $order->offer,
        //     ];
        // } else {
        //     $tickets = $order->tickets()->get();

        //     foreach ($tickets as $ticket) {
        //         $ticket_before_vat = round($ticket->price / 1.15);
        //         $ticket->ticket_before_vat = $ticket_before_vat;
        //         $ticket->vat_amount = $ticket->price - $ticket_before_vat;
        //         $ticket->total_with_vat = $ticket->price;
        //     }

        //     $total_price_before_vat = round($order->price / 1.15);
        //     $total_after_discount =
        //         $total_price_before_vat > $order->coupon_discount
        //             ? $total_price_before_vat - $order->coupon_discount
        //             : $total_price_before_vat;
        //     $vat_amount = $order->price - $total_price_before_vat;
        //     $date_ar = date('Y/n/j', strtotime($order->order_date));
        //     $date_en = date('j/n/Y', strtotime($order->order_date));

        //     $data = [
        //         'locale' => $locale,
        //         'invoice_number' => $order->id,
        //         'qrCode' => $order->invoice_qr_code,
        //         'date' => $locale == 'ar' ? $date_ar : $date_en,
        //         'vat_number' => '',
        //         'total_before_discount' => $total_price_before_vat,
        //         'discount' => $order->coupon_discount,
        //         'total_after_discount' => $total_after_discount,
        //         'vat_amount' => $vat_amount,
        //         'total_with_vat' => $order->price/ 1.15,
        //         'tickets' => $tickets,
        //         'coupon_name' => $order->coupon ? $order->coupon->code : '',
        //     ];
        // }

        $data = [
            'locale' => $locale,
            'invoice_number' => $order->id,
            'qrCode' => $order->invoice_qr_code,
            'date' => $locale == 'ar' ? $date_ar : $date_en,
            'vat_number' => '',
            'total_before_discount' => $price,
            'discount' => $coupon_discount,
            'total_after_discount' => $order->total_price,
            'vat_amount' => $order->vat_value,
            'total_with_vat' => $order->total_price,
            'tickets' => $result,
            'coupon_name' => $order->coupon ? $order->coupon->code : '',
            'totalTicketsPrices' => $price,
            'offer' => $order->offer,
        ];

        return view('invoice', $data);
    }

    public function generateUniqueReferenceNumber(): string
    {
        $prefix = 'REF-';
        $timestamp = Carbon::now()->format('YmdHis'); // e.g., 20240812123000
        $randomString = Str::random(6); // e.g., 'A1B2C3'

        return $prefix.$timestamp.'-'.$randomString;
    }
}
