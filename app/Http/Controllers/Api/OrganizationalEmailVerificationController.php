<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Group\Group;
use App\Models\User;
use App\Models\VerifyOrganizationalEmail;
use Carbon\Carbon;

class OrganizationalEmailVerificationController extends Controller
{
    public function verify($token)
    {
        $organizationalEmail = VerifyOrganizationalEmail::where('token', $token)->first();
        $setting = \App\Models\AppSetting\AppSetting::first();

        if ($organizationalEmail) {
            if ($organizationalEmail->created_at->diffInHours(Carbon::now()) > 24) {
                $result['message'] = __('Your URL is expired');

                return view('verify-account', compact('result', 'setting'));
            }
            $organizationalEmail->update([
                'expire_date' => $organizationalEmail->created_at->addYear(),
                'token' => null,
                'verify' => true,
            ]);

            $user = User::where('id', $organizationalEmail->user_id)->first();
            $user->update(['expire_email_date' => $organizationalEmail->expire_date]);

            $search = trim(explode('@', $user->organizational_email)[1]);
            $fixed_group = Group::where('domain', 'like', '%'.$search.'%')->first();
            if (
                ! (
                    empty($fixed_group) &&
                    ($user->groups() &&
                        $fixed_group ==
                            $user
                                ->groups()
                                ->where('domain', 'like', '%'.$search.'%')
                                ->first())
                )
            ) {
                $user->groups()->attach($fixed_group->id);
            }
            //return view

            $result['message'] = __('messages.OrganizationalEmailVerifiedSuccessfully');

            // return response()->json(['message' => __('messages.OrganizationalEmailVerifiedSuccessfully')], 200);
        } else {
            // return response()->json(['message' => __('messages.InvalidURL')], 400);
            $result['message'] = __('messages.InvalidURL');
        }

        return view('verify-account', compact('result', 'setting'));
    }
}
