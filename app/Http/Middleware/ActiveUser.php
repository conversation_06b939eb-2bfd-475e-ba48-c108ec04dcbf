<?php

namespace App\Http\Middleware;

use Closure;

class ActiveUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        $admin = auth()->user();
        if ($admin && $admin->active == 0) {
            $request->session()->flash('error', 'Your account is deactivated');
            auth()
                ->guard('web')
                ->logout();

            return redirect()->route('login');
        }

        return $next($request);
    }
}