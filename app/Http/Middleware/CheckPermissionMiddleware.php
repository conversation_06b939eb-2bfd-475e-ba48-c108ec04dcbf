<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class CheckPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        if (
            auth()
                ->user()
                ->hasRole('super-admin')
        ) {
            return $next($request);
        }

        $canProceed = true;
        $roles = auth()
            ->user()
            ->getRoles();
        $searchword = 'report';

        $matches = array_filter(auth()->user()->roles->pluck('name')->toArray(), function ($var) use ($searchword) {
            return preg_match("/\b$searchword\b/i", $var);
        });

        if (! $matches) {
            foreach ($roles->get() as $role) {
                if (count($role->role->list_brands) > 0) {
                    $canProceed = false;
                    $brand = $role->role->list_brands->where('brand_id', Cookie::get('brandId'))->first();
                    if ($brand) {
                        $canProceed = true;
                        break;
                    }
                }
            }
        }

        if ($canProceed) {
            $permissionsByRoles = auth()
                ->user()
                ->getPermissionsViaRoles()
                ->pluck('name');
            $permissionsByRolesArray = $permissionsByRoles->toArray();
            $canProceed = in_array($permission, $permissionsByRolesArray);
        }

        if (! $canProceed) {
            $request->session()->flash('error', 'Access denied! you don\'t have access to do this action');

            return redirect()->route('dashboard');
        }

        return $next($request);
    }
}
