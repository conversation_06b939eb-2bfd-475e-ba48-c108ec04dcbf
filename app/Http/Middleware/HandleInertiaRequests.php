<?php

namespace App\Http\Middleware;

use App\Helpers\Constant;
use App\Models\AppSetting\AppSetting;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Permission\RoleBranch;
use App\Models\Permission\RoleBrand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     *
     * @return string|null
     */
    public function version(Request $request)
    {
        return parent::version($request);
    }

    /**
     * Defines the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array
     */
    public function share(Request $request)
    {
        $brand_ids = [];
        $ids = [];
        $branch_ids = [];
        if (auth()->user()) {
            $ids = auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $brand_ids = RoleBrand::whereIn('role_id', $ids)
                ->pluck('brand_id')
                ->toArray();
            Cookie::queue('role_brands', \serialize($brand_ids));
            $branch_ids = RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
            if (count($branch_ids) < 1) {
                $branch_ids = Branch::whereIn('brand_id', $brand_ids)
                    ->pluck('id')
                    ->toArray();
            }
        }

        return array_merge(parent::share($request), [
            'sharedBrands' => Brand::where('active', 1)
                ->where('in_navbar', 1)
                ->select('id', 'position', 'active', 'in_navbar')
                ->where(function ($q) use ($brand_ids) {
                    if (count($brand_ids) > 0) {
                        $q->whereIn('id', $brand_ids);
                    }
                })
                ->get(),

            'sharedBranches' =>
                Cookie::get('brandId') != ''
                    ? Branch::where('active', 1)
                        ->where('brand_id', Cookie::get('brandId'))
                        ->whereIn('id', $branch_ids)
                        ->get()
                    : [],
            'sharedBranchIds' => $branch_ids,
            'sharedActiveBrand' =>
                Cookie::get('brandId') != '' ? Brand::where('id', Cookie::get('brandId'))->first() : '',
            'orderStatuses' => Constant::OrderStatus,
            'sharedBrandItem' => Cookie::get('brandId') ?? '',
            'appSetting' => AppSetting::first(),
        ]);
    }
}