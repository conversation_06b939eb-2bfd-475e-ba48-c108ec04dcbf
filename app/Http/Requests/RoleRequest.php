<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;

class RoleRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'name' => ['required', 'unique:roles', 'max:50', 'min:2'],
                    'permissions' => ['required', 'array'],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'name' => ['required', Rule::unique('roles', 'id')->ignore($this->role->id), 'max:50', 'min:2'],
                    'permissions' => ['required', 'array'],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }
}