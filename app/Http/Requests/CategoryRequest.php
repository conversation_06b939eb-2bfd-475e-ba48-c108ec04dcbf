<?php

namespace App\Http\Requests;

use App\Rules\ArabicChar;
use App\Rules\EnglishChar;
use Illuminate\Validation\Rule;

class CategoryRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'position' => 'required|numeric|min:0',
                    'active' => 'required',
                    'title_ar' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        Rule::unique('category_translations', 'title')->where(function ($query) {
                            return $query->where('locale', 'ar');
                        }),
                        new ArabicChar(),
                    ],

                    'title_en' => [
                        Rule::unique('category_translations', 'title')->where(function ($query) {
                            return $query->where('locale', 'en');
                        }),
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        new EnglishChar(),
                    ],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'position' => 'required|numeric|min:0',
                    'title_ar' => [
                        // Rule::unique('category_translations', 'title')
                        //     ->where(function ($query) {
                        //         return $query->where('locale', 'ar');
                        //     })
                        //     ->ignore($this->category, 'category_id'),
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        new ArabicChar(),
                    ],

                    'title_en' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',

                        // Rule::unique('category_translations', 'title')
                        //     ->where(function ($query) {
                        //         return $query->where('locale', 'en');
                        //     })
                        //     ->ignore($this->category, 'category_id'),
                        new EnglishChar(),
                    ],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Only Arabic letters are allowed',
            'title_en.required' => 'Only English letters are allowed',
        ];
    }
}