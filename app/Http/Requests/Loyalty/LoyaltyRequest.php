<?php

namespace App\Http\Requests\Loyalty;

use App\Http\Requests\Request;

class LoyaltyRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'loyaltyBrandItems' => 'array|nullable',
                    'loyaltyBrandItems.*.active' => 'required',
                    'loyaltyBrandItems.*.brand_id' => 'required|exists:brands,id',
                    'loyaltyBrandItems.*.branch_id.*' => 'distinct|exists:branches,id',
                ];
            // UPDATE |unique:loyalty_branches,branch_id
            case 'PUT':
            case 'PATCH':
                return [];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [];
    }
}