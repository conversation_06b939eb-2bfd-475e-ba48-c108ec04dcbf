<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;

class UserRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'city' => 'required|max:50|min:2',
                    'password' => 'required|min:8|confirmed|regex:/^.*(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}.*$/',

                    'nationality' => 'required',
                    'mobile' => ['required', 'phone:auto', Rule::unique('users', 'mobile')->whereNull('deleted_at')],
                    'email' => ['required', 'email', Rule::unique('users', 'email')->whereNull('deleted_at')],
                    'organizational_email' => [
                        'nullable',
                        'email',
                        Rule::unique('users', 'organizational_email')->whereNull('deleted_at'),
                    ],
                    'first_name' => 'required|max:50|min:2',
                    'last_name' => 'required|max:50|min:2',
                    'date_of_birth' => 'required|before:tomorrow',
                    'country' => 'required',
                ];
                // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'city' => 'required|max:50|min:2',
                    'nationality' => 'required',
                    'mobile' => [
                        'required',
                        'phone:auto',
                        Rule::unique('users', 'mobile')
                            ->ignore($this->user->id)
                            ->whereNull('deleted_at'),
                    ],
                    'email' => [
                        'required',
                        'email',
                        Rule::unique('users', 'email')
                            ->ignore($this->user->id)
                            ->whereNull('deleted_at'),
                    ],
                    'organizational_email' => [
                        'nullable',
                        'email',
                        Rule::unique('users', 'organizational_email')
                            ->ignore($this->user->id)
                            ->whereNull('deleted_at'),
                    ],
                    'first_name' => 'required|max:50|min:2',
                    'last_name' => 'required|max:50|min:2',
                    'date_of_birth' => 'required|before:tomorrow',
                    'country' => 'required',
                    'password' => 'nullable|confirmed',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'mobile.required' => 'Make Sure You Enter Valid Mobile Number',
            'mobile.phone' => 'Make Sure You Enter Valid Mobile Number',
            'mobile.unique' => 'This Mobile Number Is Linked To Another Account',
        ];
    }
}
