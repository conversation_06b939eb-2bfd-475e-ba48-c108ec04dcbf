<?php

namespace App\Http\Requests;

class AppSettingRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'support_email' => ['required', 'email'],
                    'birthday_event_email' => ['required', 'email'],
                ];
                // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'birthday_event_email' => ['required', 'email'],
                    'support_email' => ['required', 'email'],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [];
    }
}