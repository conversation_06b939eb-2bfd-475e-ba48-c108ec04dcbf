<?php

namespace App\Http\Requests\Promotion;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;

class BulkCouponRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'started_at' => 'required|date|before:expired_at',
                    'expired_at' => 'date|after:started_at',
                    'position' => 'required|numeric|min:0',
                    'no_coupons' => 'required|numeric|min:1',
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'started_at' => 'required|date|before:expired_at',
                    'expired_at' => 'date|after:started_at',
                    'position' => 'required|numeric|min:0',
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Please enter the coupon title in arabic',
            'title_en.required' => 'Please enter the coupon title in english',
            'image_ar.required' => 'Only Arabic letters are allowed',
            'image_en.required' => 'Only English letters are allowed',
        ];
    }
}
