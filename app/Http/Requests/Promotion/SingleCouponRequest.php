<?php

namespace App\Http\Requests\Promotion;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;

class SingleCouponRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'active' => 'required',
                    'code' => 'required|array',
                    'code.*' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
                // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'active' => 'required',
                    'code' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Please enter the coupon title in arabic',
            'title_en.required' => 'Please enter the coupon title in english',
        ];
    }
}
