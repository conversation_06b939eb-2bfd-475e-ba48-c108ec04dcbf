<?php

namespace App\Http\Requests\Promotion;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;

class BankOfferCouponRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'active' => 'required',
                    'code' => 'required',
                    'discount' => 'required',
                    'bank_offer_id' => 'required|exists:bank_offers,id|unique:coupons,bank_offer_id',
                    // 'bank_offer_id' =>
                    //     'required|exists:bank_offers,id|unique:coupons,bank_offer_id,NULL,id,brand_id,' .
                    //     request()->brand_id,
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'active' => 'required',
                    'code' => 'required',
                    'discount' => 'required',
                    'bank_offer_id' =>
                     'required|exists:bank_offers,id|unique:coupons,bank_offer_id,' . $this->bank_offer_coupon->id,

                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Please enter the coupon title in arabic',
            'title_en.required' => 'Please enter the coupon title in english',
        ];
    }
}