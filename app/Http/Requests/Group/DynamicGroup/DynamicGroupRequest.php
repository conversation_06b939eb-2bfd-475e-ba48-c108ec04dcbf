<?php

namespace App\Http\Requests\Group\DynamicGroup;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;
use Illuminate\Validation\Rule;

class DynamicGroupRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'active' => 'required',
                    'start_spending_thresholds' => 'required|numeric|between:0,99999',
                    'end_spending_thresholds' => 'required|numeric|between:0,99999',
                    //                    'period_spending_evaluation' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],

                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                    'tier_name_ar' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        Rule::unique('group_translations', 'tier_name')
                            ->where(function ($query) {
                                return $query->where('locale', 'ar');
                            })
                            ->whereNull('deleted_at'),

                        new ArabicChar(),
                    ],

                    'tier_name_en' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        Rule::unique('group_translations', 'tier_name')
                            ->where(function ($query) {
                                return $query->where('locale', 'en');
                            })
                            ->whereNull('deleted_at'),

                        new EnglishChar(),
                    ],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                    'tier_name_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'tier_name_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                    'start_spending_thresholds' => 'required|numeric|between:0,99999',
                    'end_spending_thresholds' => 'required|numeric|between:0,99999',
                    //                    'period_spending_evaluation' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }
}