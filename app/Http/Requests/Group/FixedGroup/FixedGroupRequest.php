<?php

namespace App\Http\Requests\Group\FixedGroup;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;
use Illuminate\Validation\Rule;

class FixedGroupRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],

                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                    'domain' => ['required', Rule::unique('groups', 'domain')->whereNull('deleted_at')],
                    'account_reactivation' => 'required',
                ];
                // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                    'domain' => 'required',
                    'account_reactivation' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }
}
