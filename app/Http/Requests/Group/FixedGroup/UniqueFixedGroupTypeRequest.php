<?php

namespace App\Http\Requests\Group\FixedGroup;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Group\Group;

class UniqueFixedGroupTypeRequest extends FormRequest
{
    public function rules()
    {
        return [
            'new_users' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    // Assuming $value is an array of user IDs
                    $groupType = Group::whereHas('users', function ($query) use ($value) {
                        $query->whereIn('users.id', $value);
                    })
                        ->where('type', 'fixed')
                        ->exists();

                    if ($groupType) {
                        $fail('One or more users have already been assigned to a group with type fixed.');
                    }
                },
            ],
        ];
    }
}
