<?php

namespace App\Http\Requests;

use App\Rules\ArabicChar;
use App\Rules\EnglishChar;
use Illuminate\Validation\Rule;

class QuestionRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'position' => 'required|numeric|min:0',
                    'active' => 'required',
                    'title_ar' => [
                        'required',
                        'string',
                        'max:150',
                        'min:2',
                        Rule::unique('question_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'ar');
                            })
                            ->whereNull('deleted_at'),
                        new ArabicChar(),
                    ],

                    'title_en' => [
                        Rule::unique('question_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'en');
                            })
                            ->whereNull('deleted_at'),
                        'required',
                        'string',
                        'max:150',
                        'min:2',
                        new EnglishChar(),
                    ],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'position' => 'required|numeric|min:0',
                    'title_ar' => [
                        Rule::unique('question_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'ar');
                            })
                            ->ignore($this->question->id, 'question_id')
                            ->whereNull('deleted_at'),
                        'required',
                        'string',
                        'max:150',
                        'min:2',
                        new ArabicChar(),
                    ],

                    'title_en' => [
                        'required',
                        'string',
                        'max:150',
                        'min:2',

                        Rule::unique('question_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'en');
                            })
                            ->ignore($this->question->id, 'question_id')
                            ->whereNull('deleted_at'),
                        new EnglishChar(),
                    ],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Only Arabic letters are allowed',
            'title_en.required' => 'Only English letters are allowed',
        ];
    }
}
