<?php

namespace App\Http\Requests;

use App\Rules\ArabicChar;
use App\Rules\EnglishChar;
use Illuminate\Validation\Rule;

class CorporateRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    // 'position' => 'required|numeric|min:0',
                    'status' => 'required',
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'status' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Only Arabic letters are allowed',
            'title_en.required' => 'Only English letters are allowed',
        ];
    }
}