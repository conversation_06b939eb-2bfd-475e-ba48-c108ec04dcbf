<?php

namespace App\Http\Requests\Feedback;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;

class SubjectRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'position' => 'required|numeric|min:0',
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],

                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'position' => 'required|numeric|min:0',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],

                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Only Arabic letters are allowed',
            'title_en.required' => 'Only English letters are allowed',
        ];
    }
}
