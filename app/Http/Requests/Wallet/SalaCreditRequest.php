<?php

namespace App\Http\Requests\Wallet;

use App\Http\Requests\Request;

class SalaCreditRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'charging_money' => 'required',
                    'charging_coins' => 'required',

                    'consumption_money' => 'required',
                    'consumption_coins' => 'required',

                    'lang_description_en' => 'required',
                    'lang_description_ar' => 'required',
                    'lang_usage_policies_en' => 'required',
                    'lang_usage_policies_ar' => 'required',
                    'lang_terms_and_conditions_en' => 'required',
                    'lang_terms_and_conditions_ar' => 'required',
                    'lang_how_to_charge_en' => 'required',
                    'lang_how_to_charge_ar' => 'required',
                ];
                // UPDATE
            case 'PUT':
            case 'PATCH':
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [];
    }
}
