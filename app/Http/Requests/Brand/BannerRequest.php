<?php

namespace App\Http\Requests\Brand;

use App\Http\Requests\Request;

class BannerRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'position' => 'required|numeric|min:0',
                    'active' => 'required',
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'position' => 'required|numeric|min:0',
                    'active' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'image_ar.required' => 'Only Arabic letters are allowed',
            'image_en.required' => 'Only English letters are allowed',
        ];
    }
}
