<?php

namespace App\Http\Requests\Brand;

use App\Http\Requests\Request;

class SettingRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'steps' => 'required|array',
                    'days' => 'required|array',
                    'images' => 'required|array',
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'steps' => 'required|array',
                    'days' => 'required|array',
                    // 'images' => 'required|array',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [];
    }
}
