<?php

namespace App\Http\Requests\Brand;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;
use Illuminate\Validation\Rule;

class BrandRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'position' => 'required|numeric|min:0',
                    'vat' => 'required|numeric|min:0',
                    'active' => 'required',
                    'title_ar' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        Rule::unique('brand_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'ar');
                            })
                            ->ignore($this->brand)
                            ->whereNull('deleted_at'),
                        new ArabicChar(),
                    ],

                    'title_en' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        Rule::unique('brand_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'en');
                            })
                            ->ignore($this->brand)
                            ->whereNull('deleted_at'),
                        new EnglishChar(),
                    ],

                    'description_ar' => ['nullable', new ArabicChar()],

                    'description_en' => ['nullable', new EnglishChar()],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'position' => 'required|numeric|min:0',
                    'vat' => 'required|numeric|min:0',
                    'title_ar' => [
                        Rule::unique('brand_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'ar');
                            })
                            ->ignore($this->brand->id, 'brand_id')
                            ->whereNull('deleted_at'),
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        new ArabicChar(),
                    ],

                    'title_en' => [
                        'required',
                        'string',
                        'max:50',
                        'min:2',
                        Rule::unique('brand_translations', 'title')
                            ->where(function ($query) {
                                return $query->where('locale', 'en');
                            })
                            ->ignore($this->brand->id, 'brand_id')
                            ->whereNull('deleted_at'),
                        new EnglishChar(),
                    ],
                    'description_ar' => ['nullable', new ArabicChar()],

                    'description_en' => ['nullable', new EnglishChar()],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Please enter the brand title in arabic',
            'title_en.required' => 'Please enter the brand title in english',

            'description_ar.required' => 'Please enter the brand description in arabic',
            'description_en.required' => 'Please enter the brand description in english',
        ];
    }
}
