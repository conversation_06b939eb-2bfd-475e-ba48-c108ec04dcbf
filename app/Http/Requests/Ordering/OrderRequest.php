<?php

namespace App\Http\Requests\Ordering;

use App\Http\Requests\Request;

class OrderRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'user_id' => 'required|exists:users,id',
                    'branch_id' => 'required|exists:branches,id',
                    'brand_id' => 'required|exists:brands,id',
                    'status' => 'required',
                    // 'price' => 'required',
                    'total_price' => 'required',
                    'order_date' => 'required',
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'brand_id' => 'required|exists:brands,id',
                    'branch_id' => 'required|exists:branches,id',
                    // 'price' => 'required',
                    'total_price' => 'required',
                    'order_date' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [];
    }
}
