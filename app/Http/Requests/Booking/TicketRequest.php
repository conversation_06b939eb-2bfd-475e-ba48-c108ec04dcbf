<?php

namespace App\Http\Requests\Booking;

use App\Http\Requests\Request;
use App\Rules\ArabicChar;
use App\Rules\EnglishChar;

class TicketRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'brand_id' => 'required|exists:brands,id',
                    'position' => 'required|numeric|min:0',
                    'price' => 'required|numeric|min:0',
                    'discount' => 'nullable|numeric|min:0',
                    'active' => 'required',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],

                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'brand_id' => 'required|exists:brands,id',
                    'price' => 'required|numeric|min:0',
                    'discount' => 'nullable|numeric|min:0',
                    'position' => 'required|numeric|min:0',
                    'title_ar' => ['required', 'string', 'max:50', 'min:2', new ArabicChar()],
                    'title_en' => ['required', 'string', 'max:50', 'min:2', new EnglishChar()],
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'title_ar.required' => 'Please enter the ticket title in arabic',
            'title_en.required' => 'Please enter the ticket title in english',
        ];
    }
}
