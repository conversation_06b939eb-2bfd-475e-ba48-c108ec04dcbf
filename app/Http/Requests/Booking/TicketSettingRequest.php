<?php

namespace App\Http\Requests\Booking;

use App\Http\Requests\Request;

class TicketSettingRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':

            // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                        // 'dates' => 'required|array',
                        // 'days' => 'required|array',
                    ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [];
    }
}