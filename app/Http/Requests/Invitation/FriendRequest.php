<?php

namespace App\Http\Requests\Invitation;

use App\Http\Requests\Request;

class FriendRequest extends Request
{
    public function rules()
    {
        switch ($this->method()) {
            // CREATE
            case 'POST':
                return [
                    'email' => 'required_without:mobile|exists:users,email|email',
                    'mobile' => 'required_without:email|exists:users,mobile|phone:auto',
                ];
                // UPDATE
            case 'PUT':
            case 'PATCH':
                return [
                    'reason' => 'required|string',
                    'status' => 'required',
                ];
            case 'GET':
            case 'DELETE':
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'email.required' => 'E-mail address is required',
            'status.required' => 'Status is required',
            'reason.required' => 'Reason is required',
        ];
    }
}
