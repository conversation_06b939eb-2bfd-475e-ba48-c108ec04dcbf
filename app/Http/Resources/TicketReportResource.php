<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketReportResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'Brand' => $this->object->brand ? $this->object->brand->title_en : '',
            'Location' => $this->object->locations,
            'Governorate' => $this->object->governorate,
            'Item' => $this->object->title_en,
            'Price' => $this->changes['attributes']['price'] ?? '',
            'Create Date' => $this->date,
            'Create Time' => $this->time,
            'Create User' => $this->user ? $this->user->name : '',
        ];
    }
}