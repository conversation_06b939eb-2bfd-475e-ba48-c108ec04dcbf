<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BranchesResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'brand_id' => $this->brand_id,
            'title_en' => (!empty($this->brand) ? $this->brand->title_en . ' - ' : '') . $this->title_en,
            'title_ar' => (!empty($this->brand) ? $this->brand->title_ar . ' - ' : '') . $this->title_ar,
        ];
    }
}