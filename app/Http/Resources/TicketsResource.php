<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketsResource extends JsonResource
{
    // public static $wrap = null;

    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'price' => $this->price,
            'brand_id' => $this->brand_id,
            'title_en' =>
                $this->title_en .
                ' - ' .
                (!empty($this->brand) ? $this->brand->title_en : '') .
                ' - ' .
                $this->ticket_locations,
            'title_ar' =>
                $this->title_ar .
                ' - ' .
                (!empty($this->brand) ? $this->brand->title_ar : '') .
                ' - ' .
                $this->ticket_locations,
        ];
    }
}