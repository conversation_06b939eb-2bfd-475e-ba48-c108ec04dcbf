<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PriceHistoryResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'Brand' => $this->object->brand ? $this->object->brand->title_en : '',
            'Location' => $this->object->locations,
            'Governorate' => $this->object->governorate,
            'Item' => $this->object->title_en,
            'Price' => $this->changes['attributes']['price'] ?? ($this->changes['old']['price'] ?? '-'),
            'Updated Date' => $this->date,
            'Updated Time' => $this->time,
            'Update by  User' => $this->user ? $this->user->name : '',
        ];
    }
}