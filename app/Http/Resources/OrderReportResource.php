<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderReportResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'Order Reference ID' => $this->order_number ?? '',
            'Brand' => !empty($this->brand) ? $this->brand->title_en : '',
            'Branch' => !empty($this->branch) ? $this->branch->title_en : '',
            'Governorate' =>
                !empty($this->branch) && !empty($this->branch->governorate) ? $this->branch->governorate->title_en : '',
            'Customer Name' => $this->name ? $this->name : ($this->corporate ? $this->corporate->name : ''),
            'Customer Mobile' => $this->mobile ? $this->mobile : ($this->corporate ? $this->corporate->mobile : ''),
            'Customer Email' => $this->email ? $this->email : ($this->corporate ? $this->corporate->email : ''),
            'No.Tickets' => $this->no_tickets,
            'No.Products' => $this->no_addons,
            'Order amount' => $this->price,
            'Discount Value' =>
                $this->discount + ($this->coupon_discount > $this->price ? $this->price : $this->coupon_discount) ??
                '0.00',
            'Order amount after discount' => $this->total_price == 0 ? '0.00' : $this->total_price,
            'Coupon Code' => $this->coupon ? $this->coupon->code : '',
            'Order Status' => $this->status_text ?? '',
            'Order Date' => $this->order_date ?? '',
            'Order Time' => $this->order_time ?? '',
            'Payment Method' => $this->payment_method_text ?? '',
            'Payment Status' => $this->payment_status_text ?? '',
            'Creation Date' => $this->created_date ?? '',
            'Claim Date' => $this->claimed_date ?? '',
            'Claim By' => !empty($this->claimed_user) ? $this->claimed_user->name : '',
            'Booked By' => 'App',
            'Payfort Referance' => $this->fort_id ?? '',
            'Foodices Referance' => $this->foodics_id ?? '',
        ];
    }
}