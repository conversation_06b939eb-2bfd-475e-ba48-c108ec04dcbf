<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketsReportResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'Order Reference ID' => $this->order->order_number ?? '',
            'Brand' => !empty($this->order->brand) ? $this->order->brand->title_en : '',
            'Branch' => !empty($this->order->branch) ? $this->order->branch->title_en : '',
            'Governorate' =>
                !empty($this->order->branch) && !empty($this->order->branch->governorate)
                    ? $this->order->branch->governorate->title_en
                    : '',
            'Customer Name' => $this->order->name
                ? $this->order->name
                : ($this->order->corporate
                    ? $this->order->corporate->name
                    : ''),
            'Customer Mobile' => $this->order->mobile
                ? $this->order->mobile
                : ($this->order->corporate
                    ? $this->order->corporate->mobile
                    : ''),
            'Customer Email' => $this->order->email
                ? $this->order->email
                : ($this->order->corporate
                    ? $this->order->corporate->email
                    : ''),
            'Product' => $this->title_en,
            'Quantity' => $this->quantity,
            'Price' => $this->price,
            'Order Status' => $this->order->status_text ?? '',
            'Order Date' => $this->order->order_date ?? '',
            'Order Time' => $this->order->order_time ?? '',
            'Payment Method' => $this->order->payment_method_text ?? '',
            'Payment Status' => $this->order->payment_status_text ?? '',
            'Creation Date' => $this->order->created_date ?? '',
            'Claim Date' => $this->order->claimed_date ?? '',
            'Claim By' => !empty($this->order->claimed_user) ? $this->order->claimed_user->name : '',
            'Booked By' => 'App',
            'Payfort Referance' => $this->order->fort_id ?? '',
            'Foodices Referance' => $this->order->foodics_id ?? '',
        ];
    }
}