<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UsersReportResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'User ID' => $this->id,
            'First Name' => $this->first_name,
            'Last Name' => $this->last_name,
            'Mobile' => $this->mobile,
            'Email' => $this->email,
            'Gender' => $this->gender,
            'Country' => $this->country,
            'City' => $this->city,
            'Nationality' => $this->nationality,
            'Birth Date' => $this->date_of_birth,
            'User Status' => $this->status_text,
            'Creation Date' => $this->date,
        ];
    }
}