<?php

namespace App\Listeners;

use App\Events\OrganizeEmailVerified;
use App\Mail\OrganizeEmailVerifyMail;
use App\Models\VerifyOrganizationalEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendOrganizeEmailVerified implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(OrganizeEmailVerified $event): void
    {
        try {
            $token = Str::random(60);
            if ($event->user) {
                VerifyOrganizationalEmail::create([
                    'user_id' => $event->user->id,
                    'token' => $token,
                ]);

                $verificationUrl = route('organizational-email.verify', $token);

                Mail::to($event->user->organizational_email)->send(
                    new OrganizeEmailVerifyMail($verificationUrl, $event->user)
                );
            }
        } catch (\Throwable $th) {
            //throw $th;
        }
    }
}
