<?php

namespace App\Listeners;

use App\Events\ZatcaRefundInvoice;
use App\Services\ZATCA\ZatcaService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendZatcaRefundInvoice implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(ZatcaRefundInvoice $event)
    {
        $service = new ZatcaService();
        $service->refundInvoice($event->order_id);
    }
}
