<?php

namespace App\Listeners;

use App\Events\OrderStatusUpdate;
use Illuminate\Contracts\Queue\ShouldQueue;

class ListenOrderStatus implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(OrderStatusUpdate $event)
    {
        //
        $order = $event->order;
    }
}
