<?php

namespace App\Listeners;

use App\Events\OrderNotification;
use App\Helpers\DynamicLink;
use App\Helpers\FCM;
use App\Helpers\SMS;
use App\Mail\OrderNotificationMail;
use App\Models\Corporate\Corporate;
use App\Models\Device;
use App\Models\Notification\Notification;
use App\Models\Ordering\Order;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendOrderNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(OrderNotification $event)
    {
        //
        $type = $event->type;
        $order = $event->order;
        $titleSMSKey = ' ';
        $titleKey = ' ';
        if ($type != 10) {
            $user = User::where('id', $order->user_id)->first();
        } else {
            $user = User::where('id', $event->user)->first();
        }
        $order = Order::where('id', $order->id)
            ->with('brand', 'branch', 'parentOrderTransferObj')
            ->first();

        try {
            if (!empty($user) && !empty($order)) {
                $locale = $user->lang ?? '';
                $title = '';
                $message = '';
                $titleSMS = '';

                $tokens = Device::where('user_id', $user->id)
                    ->where('status', 1)
                    ->pluck('fcm_token')
                    ->toArray();

                $order_link = DynamicLink::generate('https://salahub.ninja/order-detail/id=' . $order->id);
                if ($type == 1) {
                    // rescheduling order booking date
                    $title = __('messages.UpdateBookingDateTitle', [], $locale);
                    $titleSMS = __('messages.UpdateBookingDateInfo', [], $locale);

                    $titleSMSKey = 'UpdateBookingDateInfo';
                    $titleKey = 'UpdateBookingDateTitle';
                    $message = __(
                        'messages.UpdateBookingDateMessage',
                        [
                            'username' => $user->first_name . ' ',
                            $user->last_name,
                            'qrCode' => $order->qr_code,
                            'order_number' => $order->order_number,
                            'order_link' => $order_link,
                            'brand' => !empty($order->brand) ? $order->brand->{'title_' . $locale} : '',
                            'branch' => !empty($order->branch) ? $order->branch->{'title_' . $locale} : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                            'order_day' => date('l', strtotime($order->order_date)),
                            'order_time' => $order->order_time ? $order->order_time : '-',
                        ],
                        $locale
                    );
                } elseif ($type == 2) {
                    // cancel order
                    $title = __('messages.CancelOrderTitle', [], $locale);
                    $titleSMS = __('messages.CancelOrderInfo', [], $locale);

                    $titleSMSKey = 'CancelOrderInfo';
                    $titleKey = 'CancelOrderTitle';
                    $message = __(
                        'messages.CancelOrderMessage',
                        [
                            'username' => $user->first_name . ' ',
                            $user->last_name,
                            'qrCode' => $order->qr_code,
                            'order_number' => $order->order_number,
                            'order_link' => $order_link,
                            'brand' => !empty($order->brand) ? $order->brand->{'title_' . $locale} : '',
                            'branch' => !empty($order->branch) ? $order->branch->{'title_' . $locale} : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                            'order_day' => date('l', strtotime($order->order_date)),
                            'order_time' => $order->order_time ? $order->order_time : '-',
                        ],
                        $locale
                    );
                } elseif ($type == 3) {
                    // reminder before order
                    $title = __('messages.ReminderOrderTitle', [], $locale);
                    $titleSMS = __(
                        'messages.ReminderOrderInfo',
                        ['order_date' => date('m/d/Y', strtotime($order->order_date))],
                        $locale
                    );

                    $titleSMSKey = 'ReminderOrderInfo';
                    $titleKey = 'ReminderOrderTitle';
                    $message = __(
                        'messages.ReminderOrderMessage',
                        [
                            'username' => $user->first_name . ' ',
                            $user->last_name,
                            'qrCode' => $order->qr_code,
                            'order_number' => $order->order_number,
                            'order_link' => $order_link,
                            'brand' => !empty($order->brand) ? $order->brand->{'title_' . $locale} : '',
                            'branch' => !empty($order->branch) ? $order->branch->{'title_' . $locale} : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                            'order_day' => date('l', strtotime($order->order_date)),
                            'order_time' => $order->order_time ? $order->order_time : '-',
                        ],
                        $locale
                    );
                } elseif ($type == 4) {
                    // booking order
                    $title = __('messages.BookOrderTitle', [], $locale);
                    $titleSMS = __(
                        'messages.BookOrderInfo',
                        ['order_date' => date('m/d/Y', strtotime($order->order_date))],
                        $locale
                    );
                    $titleSMSKey = 'BookOrderTitle';
                    $titleKey = 'BookOrderTitle';
                    $message = __(
                        'messages.BookOrderMessage',
                        [
                            'username' => $user->first_name . ' ',
                            $user->last_name,
                            'reference_number' => $order->qr_code,
                            'qrCode' => $order->qr_code,
                            'order_number' => $order->order_number,
                            'order_link' => $order_link,
                            'brand' => !empty($order->brand) ? $order->brand->{'title_' . $locale} : '',
                            'branch' => !empty($order->branch) ? $order->branch->{'title_' . $locale} : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                            'order_day' => date('l', strtotime($order->order_date)),
                            'order_time' => $order->order_time ? $order->order_time : '-',
                            'payment_amount' => $order->total_price,
                            'payment_method' => $order->payment_method_text,
                        ],
                        $locale
                    );
                } elseif ($type == 5) {
                    // corporate order status
                    $corporateRequest = Corporate::find($order->corporate_request);
                    $order_link = 'corporate-requests/preview/' . $order->id;
                    $title = __('messages.CorporateOrderTitle', [], $locale);

                    $titleSMSKey = 'CorporateOrderTitle';
                    $titleKey = 'CorporateOrderTitle';
                    $message = __(
                        'messages.CorporateOrderMessage',
                        [
                            'corporate' => $corporateRequest->name,
                            'reference_number' => $order->order_number,
                            'qrCode' => $order->qr_code,
                            'order_link' => $order_link,
                            'brand' => !empty($order->brand) ? $order->brand->translate($locale)->title : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                        ],
                        $locale
                    );
                } elseif ($type == 8) {
                    // refund order
                    $title = __('messages.RefundOrderTitle', [], $locale);
                    $titleSMS = __('messages.RefundOrderInfo', [], $locale);

                    $titleSMSKey = 'RefundOrderInfo';
                    $titleKey = 'RefundOrderTitle';
                    $message = __(
                        'messages.RefundOrderMessage',
                        [
                            'username' => $user->first_name . ' ',
                            $user->last_name,
                            'qrCode' => $order->qr_code,
                            'order_number' => $order->order_number,
                            'order_link' => $order_link,
                            'brand' => !empty($order->brand) ? $order->brand->{'title_' . $locale} : '',
                            'branch' => !empty($order->branch) ? $order->branch->{'title_' . $locale} : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                            'order_day' => date('l', strtotime($order->order_date)),
                            'order_time' => $order->order_time ? $order->order_time : '-',
                            'refund_amount' => $order->refund_payment_text,
                        ],
                        $locale
                    );
                } elseif ($type == 10) {
                    // order transfer tickets
                    $title = __('messages.ReceivedTicketsFromOrderTransferTitle', [], $locale);
                    $titleSMS = __('messages.ReceivedTicketsFromOrderTransferTitle', [], $locale);

                    $titleSMSKey = 'ReceivedTicketsFromOrderTransferTitle';
                    $titleKey = 'ReceivedTicketsFromOrderTransferTitle';
                    $message = __(
                        'messages.ReceivedTicketsFromOrderTransferMsg',
                        [
                            'sender' => $order->parentOrderTransferObj->name ?? '',
                            'brand' => !empty($order->brand) ? $order->brand->{'title_' . $locale} : '',
                            'branch' => !empty($order->branch) ? $order->branch->{'title_' . $locale} : '',
                            'order_date' => date('m/d/Y', strtotime($order->order_date)),
                            'order_day' => date('l', strtotime($order->order_date)),
                            'order_time' => $order->order_time ? $order->order_time : '-',
                            'order_number' => $order->order_number,
                            'username' => $user->first_name . ' ',
                            $user->last_name,
                        ],
                        $locale
                    );
                }
                // notify email
                if ($order->email) {
                    $email = $order->email;
                    Mail::to($email)->send(new OrderNotificationMail($title, $message, $user));
                }
                // notify notifications

                if ($type != 4) {
                    (new FCM())->send($tokens, $titleSMS, $title, [
                        'order_id' => $order->id,
                        'type' => 'order',
                    ]);

                    Notification::create([
                        'en' => [
                            'title' => __('messages.' . $titleSMSKey, [], 'en'),
                            'message' => __(
                                'messages.' . $titleKey,
                                ['order_date' => date('m/d/Y', strtotime($order->order_date))],
                                'en'
                            ),
                        ],
                        'ar' => [
                            'title' => __('messages.' . $titleSMSKey, [], 'ar'),
                            'message' => __(
                                'messages.' . $titleKey,
                                ['order_date' => date('m/d/Y', strtotime($order->order_date))],
                                'ar'
                            ),
                        ],

                        'status' => 0,
                        'type' => 'order',
                        'data' => [
                            'id' => $order->id,
                            'status' => $order->status,
                        ],
                        'user_id' => $user->id,
                    ]);
                }

                // notify mobile
                if ($order->mobile != null) {
                    $mobile = $order->mobile;
                    SMS::send($mobile, $titleSMS);
                }
            }

            if ($type == 6 && !empty($order)) {
                // booking order
                $locale = 'en';
                $title = __('messages.FreeBookOrderTitle', [], $locale);
                $titleSMS = __(
                    'messages.FreeBookOrderInfo',
                    ['order_date' => date('m/d/Y', strtotime($order->order_date))],
                    $locale
                );
                $uuid = $order->order_uuid;
                $message = __(
                    'messages.FreeBookOrderMessage',
                    [
                        'reference_number' => $order->qr_code,
                        'qrCode' => $order->qr_code,
                        'order_number' => $order->order_number,
                        'order_link' => route('orders.preview', $order->order_uuid),
                        'brand' => !empty($order->brand) ? $order->brand->translate($locale)->title : '',
                        'branch' => !empty($order->branch) ? $order->branch->translate($locale)->title : '',
                        'order_date' => date('m/d/Y', strtotime($order->order_date)),
                        'order_day' => date('l', strtotime($order->order_date)),
                        'order_time' => $order->order_time ? $order->order_time : '-',
                        'invoice_link' => url($locale . '/generate-invoice/' . $uuid),
                    ],
                    $locale
                );

                Mail::to($order->email)->send(new OrderNotificationMail($title, $message, null));
            }
            if ($type == 7) {
                $locale = 'en';
                $title = __('messages.ReceiverOrderTicketTitle', [], $locale);
                $message = __(
                    'messages.ReceiverOrderTicketMessage',
                    [
                        'username' => $user->first_name . ' ',
                        $user->last_name,
                        'ticket' => $event->ticket->id,
                        'order' => $order->order_number,
                    ],
                    $locale
                );

                Mail::to($event->user->email)->send(new OrderNotificationMail($title, $message, null));
            }
        } catch (\Throwable $th) {
            // throw $th;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(OrderNotification $event, $exception)
    {
        //
        Log::error('Order Notification Error', ['exception' => $exception]);
    }
}
