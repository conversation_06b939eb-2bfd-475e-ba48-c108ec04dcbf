<?php

namespace App\Listeners;

use App\Events\GroupSpendingData;
use App\Helpers\Constant;
use App\Models\Group\Group;
use App\Models\Ordering\Order;
use Illuminate\Contracts\Queue\ShouldQueue;

class UpdateGroupSpendingData implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(GroupSpendingData $event)
    {
        $order = Order::find($event->order_id);
        if (!empty($order)) {
            $totalSpending = Order::where('user_id', $order->user_id)
                ->where('payment_status', Constant::PaidOrder) // Filter by paid orders
                ->where(function ($query) {
                    $query->where('status', Constant::ClaimOrder)->orWhere('status', Constant::NoShowOrder);
                })
                ->sum('total_price');

            $current_group = $order->user->groups->where('type', 'dynamic')->first();
            if (!empty($current_group)) {
                if ($current_group->end_spending_thresholds < $totalSpending) {
                    $next_group = Group::where('start_spending_thresholds', '<', $totalSpending)
                        ->where('end_spending_thresholds', '>', $totalSpending)
                        ->first();
                    if ($next_group) {
                        $order->user->groups()->detach($current_group->id);
                        $order->user->groups()->attach($next_group->id);
                    }
                }
            }
        }
    }
}