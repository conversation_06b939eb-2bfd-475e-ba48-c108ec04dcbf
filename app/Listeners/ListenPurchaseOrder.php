<?php

namespace App\Listeners;

use App\Events\PurchaseOrder;
use App\Helpers\Signature;
use App\Models\Ordering\Order;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Config;

class ListenPurchaseOrder implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(PurchaseOrder $event)
    {
        /*
        $status_codes =[
            '01' =>'Order Stored',
            '04' =>'Capture Success',
            '06' =>'Refund Success',
            '07' =>'Refund Failed',
            '10' =>'Incomplete',
        ]
        */
        $url = Config::get('app.payFort_url');
        $order = Order::where('id', $event->order_id)->first();
        if (! empty($order)) {
            $arrData = (new Signature())->calculateSignature(
                $order,
                $order->merchant_reference,
                $order->fort_id,
                $order->order_description,
                'PURCHASE',
                $order->total_price
            );

            $ch = curl_init($url);
            $data = json_encode($arrData);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $result = curl_exec($ch);
            curl_close($ch);
            $result = json_decode($result, true);

            $payment_status = null;
            if ($result['status'] == '04') {
                $payment_status = 1;
            } else {
                $payment_status = 2;
            }
            $order->update([
                'fort_id' => $result['fort_id'],
                'fort_status' => $result['status'],
                'payment_status' => $payment_status,
            ]);

            return $result;
        }
    }
}
