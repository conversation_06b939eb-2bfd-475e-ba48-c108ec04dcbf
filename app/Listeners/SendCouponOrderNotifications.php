<?php

namespace App\Listeners;

use App\Events\CouponOrderNotifications;
use App\Helpers\CouponCode;
use App\Helpers\FCM;
use App\Helpers\SMS;
use App\Mail\CouponNotificationMail;
use App\Models\Brand\Brand;
use App\Models\Device;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Promotion\Coupon\CouponBrand;
use App\Models\Promotion\Coupon\CouponDate;
use App\Models\Promotion\Coupon\ToCouponBrand;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendCouponOrderNotifications implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(CouponOrderNotifications $event)
    {
        //
        $order = $event->order;
        $user = $event->user;
        $coupon = $event->coupon;
        $tokens = [];
        $locale = $user->lang;
        $title = '';
        $message = '';
        $titleSMS = '';
        $brand_ids = CouponBrand::where('coupon_id', $coupon->id)
            ->pluck('brand_id')
            ->toArray();
        $userCoupon = null;
        $to_brand_ids = [];
        if (! empty($coupon) && in_array($order->brand_id, $brand_ids) && $coupon->active == 1) {
            // create order for one user
            try {
                DB::beginTransaction();
                //code...
                $userCoupon = Coupon::create([
                    'user_id' => $user->id,
                    'active' => $coupon->active,
                    'type' => $coupon->type,
                    'discount' => $coupon->discount,
                    'maximum_usage' => $coupon->maximum_usage,
                    'code' => CouponCode::generate(),
                    'is_bulk' => 0,
                    'max_value_discount' => $coupon->max_value_discount,
                    'total_order_amount' => $coupon->total_order_amount,
                    'condition' => $coupon->condition,
                    'coupon_type' => $coupon->coupon_type,
                    'notify_type' => $coupon->notify_type,
                    'notify_days' => $coupon->notify_days,
                    'validate_type' => $coupon->validate_type,
                    'no_active_days' => $coupon->no_active_days,

                    'expired_at' => $coupon->validate_type == 1 && $coupon->no_active_days > 0
                            ? date('Y-m-d', strtotime(date('Y-m-d').' +'.$coupon->no_active_days.' day'))
                            : null,

                    'en' => [
                        'title' => $coupon->translate('en')->title,
                        'description' => $coupon->translate('en')->description,
                    ],
                    'ar' => [
                        'title' => $coupon->translate('ar')->title,
                        'description' => $coupon->translate('ar')->description,
                    ],
                ]);
                //coupon after order create

                $coupon_brands = CouponBrand::where('coupon_id', $coupon->id)
                    ->where('brand_id', $order->brand_id)
                    ->get();

                foreach ($coupon_brands as $coupon_brand) {
                    //
                    $brandCoupons = ToCouponBrand::where('coupon_id', $coupon->id)
                        ->where('coupon_brand_id', $coupon_brand->id)
                        ->get();

                    $createdCouponBrand = CouponBrand::create([
                        'brand_id' => $coupon_brand->brand_id,
                        'coupon_id' => $userCoupon->id,
                    ]);

                    if (! empty($brandCoupons)) {
                        foreach ($brandCoupons as $to_brand) {
                            $to_coupon_brand = ToCouponBrand::create([
                                'brand_id' => $to_brand->brand_id,
                                'coupon_brand_id' => $createdCouponBrand->id,
                                'coupon_id' => $userCoupon->id,
                                'max_value_discount' => $to_brand->max_value_discount,
                                'total_order_amount' => $to_brand->total_order_amount,
                                'condition' => $to_brand->condition,
                                'type' => $to_brand->type,
                                'discount' => $to_brand->discount,
                            ]);
                        }
                    }
                    $coupon_dates = CouponDate::whereNotNull('day_id')
                        ->where('coupon_id', $coupon->id)
                        ->get();

                    if ($coupon->validate_type == 0 && ! empty($coupon_dates)) {
                        foreach ($coupon_dates as $coupon_date) {
                            CouponDate::create([
                                'coupon_id' => $userCoupon->id,
                                'day_id' => $coupon_date->day_id,
                            ]);
                        }
                    }
                }

                DB::commit();

                if (! empty($userCoupon)) {
                    $to_brand_ids = ToCouponBrand::where('coupon_id', $userCoupon->id)
                        ->pluck('brand_id')
                        ->toArray();
                    $brands = Brand::whereIn('id', $to_brand_ids)
                        ->get()
                        ->pluck('title_'.$locale)
                        ->toArray();
                    $brandTitles = implode(' - '.PHP_EOL, $brands);

                    $title = __('messages.OrderCouponTitle', [], $locale);
                    $contentSMS = __(
                        'messages.OrderCouponContentSMS',
                        ['brands' => $brandTitles, 'code' => $userCoupon->code],
                        $locale
                    );
                    $days = [];
                    if ($userCoupon->validate_type == 0) {
                        $allDays = \App\Helpers\Constant::Days;
                        $coupon_days = CouponDate::where('coupon_id', $userCoupon->id)
                            ->whereNotNull('day_id')
                            ->pluck('day_id')
                            ->toArray();

                        foreach ($allDays as $array) {
                            if (in_array($array['id'], $coupon_days)) {
                                array_push($days, $array['title_'.$locale]);
                            }
                        }

                        $conditions = __(
                            'messages.OrderCouponSpecificDaysMessage',
                            ['days' => implode(' - ', $days)],
                            $locale
                        );
                    }
                    if ($userCoupon->validate_type == 1) {
                        $conditions = __(
                            'messages.OrderCouponDaysAfterMessage',
                            ['days' => $userCoupon->no_active_days],
                            $locale
                        );
                    }
                    $message = __(
                        'messages.OrderCouponMessage',
                        [
                            'username' => $user->first_name.' '.$user->last_name,
                            'code' => $userCoupon->code,
                            'brands' => $brandTitles,
                            'conditions' => $conditions,
                        ],
                        $locale
                    );
                    $tokens = Device::where('user_id', $user->id)
                        ->where('status', 1)
                        ->whereNotNull('fcm_token')
                        ->pluck('fcm_token')
                        ->toArray();

                    // notify notifications
                    if (! empty($tokens)) {
                        (new FCM())->send($tokens, $title, $contentSMS, ['type' => 'coupon']);
                    }

                    // notify email
                    if ($order->email) {
                        Mail::to($order->email)->send(new CouponNotificationMail($title, $message, $user));
                    }
                    //notify mobile
                    if ($order->mobile != null) {
                        $smsConfirm = SMS::send($order->mobile, $contentSMS);
                    }
                }
            } catch (\Throwable $th) {
                //throw $th;
                DB::rollBack();
            }
        }
    }
}
