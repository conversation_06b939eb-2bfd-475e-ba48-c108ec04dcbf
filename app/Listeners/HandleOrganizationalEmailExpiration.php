<?php

namespace App\Listeners;

use App\Events\OrganizationalEmailExpired;
use App\Mail\ExpireOrganizeEmailMail;
use App\Models\User;
use App\Models\VerifyOrganizationalEmail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class HandleOrganizationalEmailExpiration
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(OrganizationalEmailExpired $event): void
    {
        $currentDate = Carbon::now();
        $organizationalEmails = User::whereDate('expire_email_date', '<', $currentDate)->get();

        // Dispatch event for each expired email
        foreach ($organizationalEmails as $user) {
            if (!empty($user)) {
                $locale = $user->lang;
                $title = __('Organizational Email Inactivation', [], $locale);
                $message = __(
                    'Because Your organizational email has been inactive for one year. Please verify your email to reactivate it.',
                    [],
                    $locale
                );

                $hasToken = VerifyOrganizationalEmail::where('user_id', $user->id)
                    ->whereNotNull('token')
                    ->exists();

                // Notify via email
                if (!$hasToken && $user->organizational_email) {
                    $token = Str::random(60);
                    VerifyOrganizationalEmail::create([
                        'user_id' => $user->id,
                        'token' => $token,
                    ]);

                    $verificationUrl = url('/api/verify-organizational-email/' . $token);
                    $mailDataView = new ExpireOrganizeEmailMail($verificationUrl, $user, $title, $message);
                    Mail::to($user->organizational_email)->send($mailDataView);
                }
            }
        }
    }
}