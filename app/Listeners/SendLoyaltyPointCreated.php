<?php

namespace App\Listeners;

use App\Events\LoyaltyPointCreated;
use App\Helpers\FCM;
use App\Models\Device;
use App\Models\Loyalty\Loyalty;
use App\Models\Notification\Notification;
use App\Models\Ordering\Order;
use App\Models\User;
use App\Models\Wallet\WalletTransaction;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendLoyaltyPointCreated implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LoyaltyPointCreated $event): void
    {
        //
        $arr = [];
        $points = 0;
        $referral_points = 0;
        $title = '';
        $message = '';
        $referral_user = null;
        $wallet = $event->wallet ?? null;

        if ($event->type == 'order') {
            $orderId = $event->data->id;
            $order = Order::Paid()
                ->where('id', $orderId)
                ->first();
            if (!empty($order)) {
                $setting = Loyalty::Publish()
                    ->BrandType()
                    ->where('brand_id', $order->brand_id)
                    ->where(function ($query) use ($order) {
                        $query->whereHas('branches', function ($q) use ($order) {
                            $q->where('branch_id', $order->branch_id);
                        });
                        $query->orWhereDoesntHave('branches');
                    })
                    ->first();

                if (!empty($setting)) {
                    $points = $setting->rate * $order->total_price;
                }

                $setting = Loyalty::Publish()
                    ->ReferralType()
                    ->first();

                if (!empty($setting)) {
                    $referral_points = $setting->rate * $order->total_price;
                }
                $user = User::where('id', $order->user_id)->first();
                $referral_user = User::where('id', $order->referral_user_id)->first();

                $walletTrasactionType = '1';
                $currencyId = '2';

                $brand = !empty($order->brand) ? $order->brand->{'title_' . 'en'} : '';
                $branch = !empty($order->branch) ? $order->branch->{'title_' . 'en'} : '';
                if (!empty($user) && $points > 0) {
                    $arr['transaction_type'] = $walletTrasactionType;
                    $arr['transaction_amount'] = (int) $points;
                    $arr['transaction_reason'] = 'Earn from paid order in ' . $brand . '( ' . $branch . ' )';
                    $arr['user_id'] = $user->id;
                    $arr['order_id'] = $order->id;
                    $arr['currency_id'] = $currencyId;
                    $wallet = WalletTransaction::create($arr);
                }

                if (!empty($referral_user) && $referral_points > 0 && !$order->parent_order_transfer) {
                    // 'is_parent_order_transfer', 'parent_order_transfer',
                    $arr['transaction_type'] = $walletTrasactionType;
                    $arr['transaction_amount'] = (int) $referral_points;
                    $arr['transaction_reason'] =
                        'Earn from referral code by order in ' . $brand . '( ' . $branch . ' )' . ' by ' . $order->name;
                    $arr['user_id'] = $referral_user->id;
                    $arr['order_id'] = $order->id;
                    $arr['currency_id'] = $currencyId;
                    $referral_wallet = WalletTransaction::create($arr);
                }
            }
        }

        if ($event->type == 'notify') {
            $user = User::where('id', $wallet->user_id)->first();
            $wallet = WalletTransaction::where('id', $wallet->id)->first();
        }

        try {
            if (!empty($referral_user) && !empty($referral_wallet)) {
                $locale = $referral_wallet->lang ?? '';

                $title = __('messages.LoyaltyPointTitle', [], $locale);
                $message = __(
                    'messages.ReferralPointAddedMessage',
                    [
                        'referral' => $order->user->name,
                        'points' => $referral_user->loyalityTotal(),
                    ],
                    $locale
                );

                $data['en'] = [
                    'title' => __('messages.LoyaltyPointTitle', [], 'en'),
                    'message' => __(
                        'messages.ReferralPointAddedMessage',
                        [
                            'referral' => $order->user->name,
                            'points' => $referral_user->loyalityTotal(),
                        ],
                        'en'
                    ),
                ];
                $data['ar'] = [
                    'title' => __('messages.LoyaltyPointTitle', [], 'ar'),
                    'message' => __(
                        'messages.ReferralPointAddedMessage',
                        [
                            'referral' => $order->user->name,
                            'points' => $referral_user->loyalityTotal(),
                        ],
                        'ar'
                    ),
                ];

                $data['user_id'] = $referral_user->id;
                $data['status'] = 0;
                $data['type'] = 'loyalty-point';
                $data['data'] = [
                    'id' => $referral_wallet->id,
                    'transaction_type' => $referral_wallet->transaction_type,
                ];

                Notification::create($data);

                $tokens = Device::where('user_id', $referral_user->id)
                    ->where('status', 1)
                    ->pluck('fcm_token')
                    ->toArray();

                if (!empty($tokens)) {
                    (new FCM())->send($tokens, $title, $message, [
                        'type' => 'loyalty-point',
                        'id' => $referral_wallet->id,
                        'transaction_type' => $referral_wallet->transaction_type,
                    ]);
                }
            }
            if (!empty($user) && !empty($wallet)) {
                $data = [];
                $locale = $user->lang ?? '';
                $type = $wallet->transaction_type;
                if ($type === '0') {
                    //
                    $data['en'] = [
                        'title' => __('messages.LoyaltyPointTitle', [], 'en'),
                        'message' => __(
                            'messages.LoyaltyPointDeductedMessage',
                            [
                                'reason' => $wallet->transaction_reason,
                                'points' => $user->loyalityTotal(),
                            ],
                            'en'
                        ),
                    ];
                    $data['ar'] = [
                        'title' => __('messages.LoyaltyPointTitle', [], 'ar'),
                        'message' => __(
                            'messages.LoyaltyPointDeductedMessage',
                            [
                                'reason' => $wallet->transaction_reason,
                                'points' => $user->loyalityTotal(),
                            ],
                            'ar'
                        ),
                    ];

                    $title = __('messages.LoyaltyPointTitle', [], $locale);
                    $message = __(
                        'messages.LoyaltyPointDeductedMessage',
                        [
                            'reason' => $wallet->transaction_reason,
                            'points' => $user->loyalityTotal(),
                        ],
                        $locale
                    );
                } elseif ($type === '1') {
                    //
                    $title = __('messages.LoyaltyPointTitle', [], $locale);
                    $message = __(
                        'messages.LoyaltyPointAddedMessage',
                        [
                            'points' => $user->loyalityTotal(),
                        ],
                        $locale
                    );

                    $data['en'] = [
                        'title' => __('messages.LoyaltyPointTitle', [], 'en'),
                        'message' => __(
                            'messages.LoyaltyPointAddedMessage',
                            [
                                'reason' => $wallet->transaction_reason,
                                'points' => $user->loyalityTotal(),
                            ],
                            'en'
                        ),
                    ];
                    $data['ar'] = [
                        'title' => __('messages.LoyaltyPointTitle', [], 'ar'),
                        'message' => __(
                            'messages.LoyaltyPointAddedMessage',
                            [
                                'reason' => $wallet->transaction_reason,
                                'points' => $user->loyalityTotal(),
                            ],
                            'ar'
                        ),
                    ];
                }

                $data['user_id'] = $user->id;
                $data['status'] = 0;
                $data['type'] = 'loyalty-point';
                $data['data'] = [
                    'id' => $wallet->id,
                    'transaction_type' => $wallet->transaction_type,
                ];

                Notification::create($data);

                $tokens = Device::where('user_id', $user->id)
                    ->where('status', 1)
                    ->pluck('fcm_token')
                    ->toArray();

                if (!empty($tokens)) {
                    (new FCM())->send($tokens, $title, $message, [
                        'type' => 'loyalty-point',
                        'id' => $wallet->id,
                        'transaction_type' => $wallet->transaction_type,
                    ]);
                }
            }
        } catch (\Throwable $th) {
            //throw $th;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(LoyaltyPointCreated $event, $exception)
    {
        //
        \Log::error('Point Notification Error', ['exception' => $exception]);
    }
}