<?php

namespace App\Listeners;

use App\Events\BirthdayEventSubmitted;
use App\Mail\BirthdayEventRequestMail;
use App\Models\AppSetting\AppSetting;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

class SendBirthdayEventSubmitted implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BirthdayEventSubmitted $event): void
    {
        //
        $setting = AppSetting::first();

        if ($setting && $setting->birthday_event_email != '') {
            Mail::to($setting->birthday_event_email)->send(new BirthdayEventRequestMail($event->birthdayEvent));
        }
        if ($event && $event->birthdayEvent->email != '') {
            Mail::to($event->birthdayEvent->email)->send(new BirthdayEventRequestMail($event->birthdayEvent));
        }
    }
}