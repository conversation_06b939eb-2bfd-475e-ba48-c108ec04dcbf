<?php

namespace App\Listeners;

use App\Events\CreateFoodicsOrder;
use App\Services\Foodics\FoodicsService;
use Illuminate\Contracts\Queue\ShouldQueue;

class ListenCreateFoodicsOrder implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(CreateFoodicsOrder $event)
    {
        $service = new FoodicsService();
        $service->createOrder($event->order_id);
    }
}
