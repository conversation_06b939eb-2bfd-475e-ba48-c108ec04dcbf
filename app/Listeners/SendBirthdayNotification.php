<?php

namespace App\Listeners;

use App\Events\BirthdayCouponNotification;
use App\Helpers\Constant;
use App\Helpers\CouponCode;
use App\Helpers\FCM;
use App\Helpers\SMS;
use App\Mail\CouponNotificationMail;
use App\Models\Device;
use App\Models\Promotion\Coupon\Coupon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

class SendBirthdayNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(BirthdayCouponNotification $event)
    {
        //
        $coupon = $event->coupon;
        $user = $event->user;

        if (! empty($user) && ! empty($coupon)) {
            $locale = $user->lang;
            $title = '';
            $message = '';
            $contentSMS = '';

            $tokens = Device::where('user_id', $user->id)
                ->where('status', 1)
                ->pluck('fcm_token')
                ->toArray();

            //check if notify with coupon or not
            if ($coupon->notify_type == Constant::NotifyOnly) {
                $title = __('messages.HappyBirthDayTitle', [], $locale);

                $contentSMS = __('messages.HappyBirthDayInfoWithoutCode', [], $locale);
                $message = __(
                    'messages.HappyBirthDayMessageWithoutCode',
                    ['username' => $user->first_name.' '.$user->last_name],
                    $locale
                );
                (new FCM())->send($tokens, $title, $contentSMS, ['type' => 'coupon']);
            } elseif ($coupon->notify_type == Constant::NotifyCoupon) {
                //create new coupon
                $checkCoupon = Coupon::where([
                    'user_id' => $user->id,
                    'coupon_type' => $coupon->coupon_type,
                ])
                    ->whereYear('created_at', date('Y'))
                    ->first();
                if (empty($checkCoupon)) {
                    $userCoupon = Coupon::create([
                        'user_id' => $user->id,
                        'active' => $coupon->active,
                        'type' => $coupon->type,
                        'discount' => $coupon->discount,
                        'maximum_usage' => $coupon->maximum_usage,
                        'code' => CouponCode::generate(),
                        'is_bulk' => 0,
                        'max_value_discount' => $coupon->max_value_discount,
                        'total_order_amount' => $coupon->total_order_amount,
                        'condition' => $coupon->condition,
                        'coupon_type' => $coupon->coupon_type,
                        'notify_type' => $coupon->notify_type,
                        'notify_days' => $coupon->notify_days,
                        'en' => [
                            'title' => $coupon->translate('en')->title,
                            'description' => $coupon->translate('en')->description,
                        ],
                        'ar' => [
                            'title' => $coupon->translate('ar')->title,
                            'description' => $coupon->translate('ar')->description,
                        ],
                    ]);

                    $title = __(
                        'messages.HappyBirthDayTitle',
                        ['code' => $userCoupon->code, 'username' => $user->first_name.' '.$user->last_name],
                        $locale
                    );
                    $contentSMS = __('messages.HappyBirthDayInfo', ['code' => $userCoupon->code], $locale);
                    $message = __(
                        'messages.HappyBirthDayMessage',
                        [
                            'username' => $user->first_name.' '.$user->last_name,
                            'code' => $userCoupon->code,
                        ],
                        $locale
                    );

                    // notify notifications
                    (new FCM())->send($tokens, $title, $contentSMS, ['coupon' => $userCoupon, 'type' => 'coupon']);
                }
            }

            // notify email
            if ($user->email && $title != '') {
                Mail::to($user->email)->send(new CouponNotificationMail($title, $message, $user));
            }
            //notify mobile
            if ($user->mobile != null && $contentSMS != '') {
                $smsConfirm = SMS::send($user->mobile, $contentSMS);
            }
        }
    }
}
