<?php

namespace App\Listeners;

use App\Events\RefundOrder;
use App\Models\Ordering\Order;
use App\Services\Payment\PayFortService;
use App\Services\Payment\TamaraService;
use Illuminate\Contracts\Queue\ShouldQueue;

class ListenRefundOrder implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(RefundOrder $event)
    {
        $order = Order::where('id', $event->order_id)->first();
        if (!empty($order) && $event->amount > 0) {
            if ($event->payment_method == 'payfort') {
                $payFortService = new PayFortService();
                $payFortService->refund($event->order_id, $event->amount);
            } elseif ($event->payment_method == 'tamara') {
                // tamara refund
                $payFortService = new TamaraService();
                $payFortService->refund($event->order_id, $event->amount);
            }
        }
    }
}
