<?php

namespace App\Listeners;

use App\Events\InvitationNotification;
use App\Helpers\FCM;
use App\Models\Device;
use App\Models\Invitation\Friend;
use App\Models\Notification\Notification;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class SendInvitationNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(InvitationNotification $event)
    {
        //
        $invitationId = $event->invitation;
        $userId = $event->user;
        $type = $event->type;
        $invitation = Friend::where('id', $invitationId)->first();

        if (! empty($invitation)) {
            if ($type == 0) {
                $sender = User::where('id', $invitation->sender_id)->first();
                $receiver = User::where('id', $invitation->user_id)->first();
            } else {
                $sender = User::where('id', $invitation->user_id)->first();
                $receiver = User::where('id', $invitation->sender_id)->first();
            }
        }

        try {
            if (! empty($receiver) && ! empty($invitation)) {
                $locale = $receiver->lang ?? '';
                $title = '';
                $message = '';
                $from = '';
                $tokens = Device::where('user_id', $receiver->id)
                    ->where('status', 1)
                    ->pluck('fcm_token')
                    ->toArray();

                if ($type == 0) {
                    // invitation created
                    $title = 'AddNewFriendTitle';
                    $message = 'AddNewFriendMessage';
                    $from = $invitation->sender->name;
                    $fromUser = $invitation->sender;
                } elseif ($type == 1) {
                    // accept invitation
                    $title = 'AcceptFriendRequestTitle';
                    $message = 'AcceptFriendRequestMessage';
                    $from = $invitation->user->name;
                    $fromUser = $invitation->user;
                } elseif ($type == 2) {
                    // reject invitation
                    $title = 'RejectFriendRequestTitle';
                    $message = 'RejectFriendRequestMessage';
                    $from = $invitation->user->name;
                    $fromUser = $invitation->user;
                }

                // create notifications in db

                $notification = Notification::create([
                    'en' => [
                        'title' => __('messages.'.$title, [], 'en'),
                        'message' => __('messages.'.$message, ['from' => $from], 'en'),
                    ],
                    'ar' => [
                        'title' => __('messages.'.$title, [], 'ar'),
                        'message' => __('messages.'.$message, ['from' => $from], 'ar'),
                    ],

                    'status' => 0,
                    'type' => 'invitation',
                    'data' => [
                        'id' => $invitation->id,
                        'status' => $invitation->status,
                    ],
                    'user_id' => $receiver->Id,
                    'sender_id' => $sender->id,
                ]);

                // notify notifications
                $message = __(
                    'messages.'.$message,
                    [
                        'from' => $from,
                    ],
                    $locale
                );

                $title = __('messages.'.$title, [], $locale);

                if (! empty($tokens)) {
                    (new FCM)->send($tokens, $title, $message, [
                        'invitation_id' => $invitation->id,
                        'notification_id' => $notification->id,
                        'type' => 'invitation',
                        'status' => $invitation->status,
                        'senderـname' => $fromUser->name ?? '',
                        'senderـmobile' => $fromUser->mobile ?? '',
                        'senderـemail' => $fromUser->email ?? '',
                        'senderـphoto_path' => $fromUser->photo_path ?? '',
                    ]);
                }
            }
        } catch (\Throwable $th) {
            // throw $th;
            Log::error($th);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(InvitationNotification $event, $exception)
    {
        //
        Log::error('Invitation Notification Error', ['exception' => $exception]);
    }
}
