<?php

namespace App\Listeners;

use App\Events\CancelFoodicsOrder;
use App\Services\Foodics\FoodicsService;
use Illuminate\Contracts\Queue\ShouldQueue;

class ListenCancelFoodicsOrder implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(CancelFoodicsOrder $event)
    {
        $service = new FoodicsService();
        $service->cancelOrder($event->order);
    }
}
