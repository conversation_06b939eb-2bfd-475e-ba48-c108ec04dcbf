<?php

namespace App\Listeners;

use App\Events\BulkCouponCreate;
use App\Mail\BulkCouponCreateMail;
use App\Models\Promotion\Coupon\CouponBranch;
use App\Models\Promotion\Coupon\Coupon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ListenBulkCouponCreate implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     */
    public function handle(BulkCouponCreate $event): void
    {
        $no_coupons = $event->no_coupons;
        $prefix = $event->prefix;
        $data = $event->data;
        $bulkCoupon = $event->bulkCoupon;
        $email = $event->email;
        $branches = $event->branches;

        DB::beginTransaction();
        $coupons = [];

        for ($i = 0; $i < $no_coupons; ++$i) {
            // generate code
            $data['code'] = $prefix.$this->unique_str();
            // add bulk coupon to data
            $coupon = Coupon::create($data);
            if ($data['started_at'] <= now() && $data['expired_at'] >= now()) {
                $coupon->update(['active' => 1]);
            }
            if (!empty($branches) && count($branches) > 0) {
                foreach ($branches as $row) {
                    CouponBranch::create([
                        'branch_id' => $row,
                        'coupon_id' => $coupon->id,
                        'brand_id' => $coupon->brand_id,
                    ]);
                }
            }
        }

        DB::commit();
        // send mail to admin after created
        Mail::to($email)->send(new BulkCouponCreateMail($bulkCoupon));
    }

    public function unique_str()
    {
        $uniqueStr = '';
        $uniqueStr = Str::random(5);
        while (Coupon::where('code', $uniqueStr)->exists()) {
            $uniqueStr = Str::random(5);
        }

        return $uniqueStr;
    }
}
