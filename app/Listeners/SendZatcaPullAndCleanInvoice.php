<?php

namespace App\Listeners;

use App\Events\ZatcaPullAndCleanInvoice;
use App\Models\Ordering\Order;
use App\Models\Ordering\ZatcaInvoice;
use App\Services\ZATCA\ZatcaService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class SendZatcaPullAndCleanInvoice implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(ZatcaPullAndCleanInvoice $event)
    {
        $data = (new ZatcaService())->pullInvoice();
        Log::info(json_encode($data));

        $queuelength = isset($data['header']['X-Edi-Queuelength']) ? $data['header']['X-Edi-Queuelength'] : 0;
        if ($queuelength > 0) {
            for ($i = 1; $i <= $queuelength; $i++) {
                //save in db
                $data = (new ZatcaService())->pullInvoice();
                $eventId = isset($data['header']['X-Edi-Eventid']) ? $data['header']['X-Edi-Eventid'] : null; // x-edi-eventid

                $queuelength = isset($data['header']['X-Edi-Queuelength']) ? $data['header']['X-Edi-Queuelength'] : 0; // x-edi-queuelength
                $arr = [];
                if (!empty($data['body']) && isset($data['body']->invoiceNumber)) {
                    $invoiceNumber = $data['body']->invoiceNumber;

                    $eventId = isset($data['header']['X-Edi-Eventid']) ? $data['header']['X-Edi-Eventid'] : null; // x-edi-eventid

                    $order = Order::where('id', $invoiceNumber)->first();

                    if (!empty($order)) {
                        $arr['order_id'] = $order->id;
                    }
                    $arr['validationResults'] = serialize($data['body']->validationResults);
                    $arr['reportingStatus'] = $data['body']->reportingStatus;
                    $arr['invoiceCounter'] = $data['body']->invoiceCounter;
                    $arr['invoiceNumber'] = $data['body']->invoiceNumber;
                    $arr['businessProcessType'] = $data['body']->businessProcessType;
                    $arr['uniqueInvoiceIdentifier'] = $data['body']->uniqueInvoiceIdentifier;
                    $arr['invoiceIssueDate'] = $data['body']->invoiceIssueDate;
                    $arr['invoiceIssueTime'] = $data['body']->invoiceIssueTime;
                    $arr['qrCode'] = $data['body']->qrCode ?? '';
                    $arr['previousInvoiceHash'] = $data['body']->previousInvoiceHash;
                    $arr['cryptographicStamp'] = $data['body']->cryptographicStamp;
                    ZatcaInvoice::updateOrCreate(['order_id' => $invoiceNumber], $arr);
                    if ($eventId) {
                        (new ZatcaService())->cleanupInvoice($eventId);
                    }
                } else {
                    if ($eventId) {
                        (new ZatcaService())->cleanupInvoice($eventId);
                    }
                }
            }
        }
    }
}