<?php

namespace App\Listeners;

use App\Events\ZatcaCreateInvoice;
use App\Models\Ordering\Order;
use App\Models\Ordering\ZatcaInvoice;
use App\Services\ZATCA\ZatcaService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendZatcaCreateInvoice implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(ZatcaCreateInvoice $event)
    {
        $service = new ZatcaService();
        $response = $service->createInvoice($event->order_id);
        $arr['order_id'] = $event->order_id;
        $order = Order::find($event->order_id);
        if (isset($response['payload'])) {
            $arr['request_payload'] = serialize($response['payload']);
        }
        if (isset($response['body'])) {
            $arr['response_body'] = serialize($response['body']);
        }
        $arr['reportingStatus'] = 'In progress';
        $arr['invoiceIssueDate'] = $order ? $order->order_date : null;
        $arr['invoiceIssueTime'] = $order ? $order->order_time : null;
        ZatcaInvoice::updateOrCreate(['order_id' => $event->order_id], $arr);
    }
}
