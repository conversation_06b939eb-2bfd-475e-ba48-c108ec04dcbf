<?php

namespace App\GraphQL\Queries\Wallet;

use App\Models\AppSetting\AppSetting;
use App\Models\Wallet\WalletTransaction;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetExpiredLoyaltyPoints
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = null;
        $setting = AppSetting::first();
        if (! empty($setting)) {
            $checkDateForBrand = strtotime('-'.$setting->points_expiry_month.' months');

            $oneWeekAgoDate = strtotime('+7 day', $checkDateForBrand);
            $user = Auth::user();
            $pointWillExpired = $user
                ->walletTransactions()
                ->where('created_at', '<=', date('Y-m-d', $oneWeekAgoDate))
                ->Loyalty()
                ->Credit()
                ->sum('transaction_amount');

            $pointSpending = $user
                ->walletTransactions()
                ->Loyalty()
                ->Deduct()
                ->sum('transaction_amount');

            if ($pointWillExpired > $pointSpending) {
                //will expired diffrence
                $lastId = WalletTransaction::latest('id')->first();
                $arr = new WalletTransaction();
                $arr->transaction_type = '0';
                $arr->transaction_amount = $pointWillExpired - $pointSpending;
                $arr->transaction_reason = 'Expired points';
                $arr->user_id = $user->id;
                $arr->id = ! empty($lastId) ? $lastId->id + 1 : 1;
                $arr->currency_id = 2;
                $arr->created_at = now();
                $arr->expired_at = date('Y-m-d h:i:s', strtotime('+7 day'));
            }
        }

        return $arr;
    }
}
