<?php

namespace App\GraphQL\Queries\Wallet;

use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetWalletDetails
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $user = Auth::user();
        $walletTotal = $user->walletTotal();
        $loyalityTotal = $user->loyalityTotal();

        $data = [
            'salaCreditTotal' => $walletTotal,
            'loyaltyTotal' => $loyalityTotal,
        ];

        return $data;
    }
}
