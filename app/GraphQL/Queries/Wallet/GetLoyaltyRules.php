<?php

namespace App\GraphQL\Queries\Wallet;

use App\Models\Loyalty\LoyaltyRuleTranslation;
use App\Models\Wallet\LoyaltyRule;

class GetLoyaltyRules
{
    /**
     * @param  null  $_
     * @param  array<string, mixed>  $args
     */
    public function __invoke($_, array $args)
    {
        if ($rules = LoyaltyRuleTranslation::where('locale', app()->getLocale())->first()) {
            return $rules;
        }
        return null;
    }
}