<?php

namespace App\GraphQL\Queries\Wallet;

use App\Models\Wallet\SalaCreditRule;

class GetSalaCreditRules
{
    /**
     * @param  null  $root
     * @return mixed
     */
    public function __invoke($root, array $args)
    {
        $rules = SalaCreditRule::with('translations')->first();

        if (! $rules) {
            $rules = SalaCreditRule::create(['id' => 1]);
        }

        return $rules;
    }
}
