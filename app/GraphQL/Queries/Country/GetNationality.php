<?php

namespace App\GraphQL\Queries\Country;

use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetNationality
{
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $nationalities = [];
        $path = asset('json/nationalities.json');
        $data = json_decode(file_get_contents($path), true);
        if (!empty($data)) {
            foreach ($data as $item) {
                array_push($nationalities, [
                    'title' => app()->getLocale() == 'ar' ? $item['title_ar'] : $item['title_en'],
                    'code' => $item['title_en'],
                ]);
            }
        }

        return ['nationalities' => $nationalities, 'result' => $this->result];
    }
}
