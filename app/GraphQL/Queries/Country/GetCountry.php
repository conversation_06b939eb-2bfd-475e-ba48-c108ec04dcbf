<?php

namespace App\GraphQL\Queries\Country;

use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetCountry
{
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $path = asset('json/countries.json');
        $countries = [];
        $data = json_decode(file_get_contents($path), true);
        if (!empty($data)) {
            foreach ($data as $item) {
                array_push($countries, [
                    'name' => app()->getLocale() == 'ar' ? $item['NameAR'] : $item['Name'],
                    'code' => $item['Code'],
                    'image' => $item['image'] ? 'https://flagcdn.com/48x36/' . $item['image'] : null,
                ]);
            }
        }

        return ['countries' => $countries, 'result' => $this->result];
    }
}
