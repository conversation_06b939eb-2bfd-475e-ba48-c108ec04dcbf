<?php

namespace App\GraphQL\Queries\Ticket;

use App\Models\Booking\Ticket\Ticket;
use App\Models\Booking\Ticket\TicketBranch;
use App\Models\Booking\Ticket\TicketCalendar;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetTicket
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $date = $args['date'];
        $ids = TicketBranch::where('branch_id', $args['branch_id'])
            ->pluck('ticket_id')
            ->toArray();
        $ticketsIds = TicketCalendar::whereIn('ticket_id', $ids)
            ->where(function ($query) use ($date) {
                $query->whereDate('date', '=', date('Y-m-d', strtotime($date)));
                $query->orWhere(function ($q) use ($date) {
                    $q->whereDate('end', '>=', date('Y-m-d', strtotime($date)));
                    $q->whereDate('start', '<=', date('Y-m-d', strtotime($date)));
                });
            })

            ->pluck('ticket_id')
            ->toArray();

        $tickets = Ticket::publish()
            ->ordered()
            ->where('brand_id', $args['brand_id'])
            ->whereIn('id', $ticketsIds);

        if (isset($args['type']) && $args['type'] == 'special') {
            $tickets = $tickets->Special();
        }
        if (isset($args['type']) && $args['type'] == 'birthday') {
            $tickets = $tickets->birthday();
        } else {
            $tickets = $tickets->Normal();
        }

        $tickets = $tickets->get();

        return $tickets;
    }
}