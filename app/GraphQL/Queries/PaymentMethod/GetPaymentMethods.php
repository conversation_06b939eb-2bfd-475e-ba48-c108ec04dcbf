<?php

namespace App\GraphQL\Queries\PaymentMethod;

use App\Models\PaymentMethods\PaymentMethod;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetPaymentMethods
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        if (auth()->user()->is_test_account) {
            $data = [];
            $payments = PaymentMethod::get();
            foreach ($payments as $payment) {
                if ($payment->name == 'Tamara') {
                    $payment->active = true;
                }
                array_push($data, $payment);
            }

            return $data;
        } else {
            return PaymentMethod::all();
        }
    }
}
