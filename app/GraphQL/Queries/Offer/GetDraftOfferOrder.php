<?php

namespace App\GraphQL\Queries\Offer;

use App\Models\Offer\Offer;
use App\Models\Ordering\Order;
use App\Scopes\NonDraftScope;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetDraftOfferOrder
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $error = 0;
        $message = '';
        if ($offer = Offer::active()->activeTime()->where('id', $args['offer_id'])->first()) {
            $order = Order::withoutGlobalScope(NonDraftScope::class)->firstOrCreate(
                [
                    'offer_id' => $offer->id,
                    'brand_id' => $offer->brand_id,
                    'user_id' => Auth::user()->id,
                    'is_draft' => '1',
                ],
                [
                    'order_number' => floor(microtime(true) * 10000),
                    'qr_code' => floor(microtime(true) * 10000),
                ]
            );

            foreach ($offer->allTickets as $ticket) {
                $subOrder = Order::withoutGlobalScope(NonDraftScope::class)->firstOrCreate(
                    [
                        'offer_id' => $offer->id,
                        'brand_id' => $ticket->brand_id,
                        'parent_offer_order_id' => $order->id,
                        'user_id' => Auth::user()->id,
                    ],
                    [
                        'order_number' => floor(microtime(true) * 10000),
                        'qr_code' => floor(microtime(true) * 10000),
                        'is_draft' => '1',
                    ]
                );
                $subOrder->governorate_id = $offer->governorate->id;

                $subOrder->name = Auth::user()->name;
                $subOrder->email = Auth::user()->email;
                $subOrder->mobile = Auth::user()->mobile;

                $subOrder->device_uuid = \Request::header('device_uuid');
                $subOrder->app_version = \Request::header('version');
                $subOrder->order_uuid = Str::orderedUuid();

                $subOrder->save();
            }

            //set the rest of the order data
            $order->governorate_id = $offer->governorate->id;

            $order->name = Auth::user()->name;
            $order->email = Auth::user()->email;
            $order->mobile = Auth::user()->mobile;

            $order->device_uuid = \Request::header('device_uuid');
            $order->app_version = \Request::header('version');
            $order->order_uuid = Str::orderedUuid();

            $order->save();

            //set order offer brands
            $offerBrands = [];
            foreach ($order->offer->allTickets as $ticket) {
                $offerBrandArr['brand'] = $ticket->brand;
                $offerBrandArr['image'] = asset('$ticket->brand->image_path');

                if ($ticket->brand->id == $order->offer->brand_id) {
                    $offerBrandArr['heading'] = __('messages.offerMainBrandHeading', [
                        'amount' => $ticket->quantity,
                        'brand_name' => $ticket->brand->title,
                    ]);
                } else {
                    if ($order->offer->type == 0) {
                        $offerBrandArr['heading'] = __('messages.offerSubBrandHeadingFree', [
                            'amount' => $ticket->quantity,
                            'brand_name' => $ticket->brand->title,
                        ]);
                    } else {
                        $offerBrandArr['heading'] = __('messages.offerSubBrandHeadingDiscount', [
                            'discount' => $ticket->discount,
                            'brand_name' => $ticket->brand->title,
                        ]);
                    }
                }
                $offerBrands[] = $offerBrandArr;
            }
            $order->offer->brands = $offerBrands;

            $arr['order'] = $order;
        } else {
            $error = 1;
            $message = __('messages.offer_not_found');
        }
        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}
