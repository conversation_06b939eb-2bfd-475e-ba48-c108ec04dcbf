<?php

namespace App\GraphQL\Queries\Offer;

use App\Models\Brand\Branch;
use App\Models\Offer\Offer;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetOfferDetails
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $error = 0;
        $message = '';

        if (
            $offer = Offer::active()
                ->activeTime()
                ->where('id', $args['offer_id'])
                ->first()
        ) {
            $offerBrands = [];

            // Group tickets by brand
            $ticketsByBrand = $offer->allTickets->groupBy(function ($ticket) {
                return $ticket->brand->id;
            });

            foreach ($ticketsByBrand as $brandId => $tickets) {
                // Initialize branches for this brand
                $branchIdsIntersection = null;

                foreach ($tickets as $ticket) {
                    // Get branch IDs for this ticket
                    $thisTicketBranchIds = $ticket->ticket->branches->pluck('branch_id')->toArray();

                    if ($branchIdsIntersection === null) {
                        // If it's the first ticket, initialize with all branches
                        $branchIdsIntersection = $thisTicketBranchIds;
                    } else {
                        // For subsequent tickets, find the intersection
                        $branchIdsIntersection = array_intersect($branchIdsIntersection, $thisTicketBranchIds);
                    }
                }

                // Retrieve the actual Branch models based on the intersected branch IDs
                $intersectedBranches = Branch::whereIn('id', $branchIdsIntersection)->get();

                // Prepare brand data for the offer
                $firstTicket = $tickets->first();
                $firstTicket->brand->branches = $intersectedBranches;
                $offerBrandArr['brand'] = $firstTicket->brand;
                $offerBrandArr['image'] = $firstTicket->brand->image_path;

                if ($firstTicket->brand->id == $offer->brand_id) {
                    $offerBrandArr['heading'] = __('messages.offerMainBrandHeading', [
                        'amount' => $firstTicket->quantity,
                        'brand_name' => $firstTicket->brand->title,
                    ]);
                } else {
                    if ($offer->type == 0) {
                        $offerBrandArr['heading'] = __('messages.offerSubBrandHeadingFree', [
                            'amount' => $firstTicket->quantity,
                            'brand_name' => $firstTicket->brand->title,
                        ]);
                    } else {
                        $offerBrandArr['heading'] = __('messages.offerSubBrandHeadingDiscount', [
                            'discount' => $firstTicket->discount,
                            'brand_name' => $firstTicket->brand->title,
                        ]);
                    }
                }

                $offerBrands[] = $offerBrandArr;
            }

            $offer->brands = $offerBrands;

            if ($offer->q_a_description == null) {
                $offer->q_a_description = '';
            }

            $arr['offer'] = $offer;
            // $error = strlen(json_encode($arr['offer']));
        } else {
            $error = 1;
            $message = __('messages.offer_not_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}