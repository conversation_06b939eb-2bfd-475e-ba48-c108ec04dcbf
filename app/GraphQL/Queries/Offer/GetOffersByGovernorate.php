<?php

namespace App\GraphQL\Queries\Offer;

use App\Models\Offer\Offer;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetOffersByGovernorate
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $error = 0;
        $message = '';
        $arr['offers'] = Offer::active()->activeTime()->where('governorate_id', $args['governorate_id'])->get();
        if (empty($arr['offers']) || count($arr['offers']) < 1) {
            $error = 1;
            $message = __('messages.offer_not_found');
        }
        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}
