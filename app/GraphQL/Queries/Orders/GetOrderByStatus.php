<?php

namespace App\GraphQL\Queries\Orders;

use App\Models\Ordering\Order;
use App\Services\Payment\WalletService;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetOrderByStatus
{

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $user = Auth::user();

        // Build the query
        $query = Order::where('user_id', $user->id)
            ->whereIn('status', $args['status']);

        // Filter by reviewed status if provided
        if (isset($args['reviewed'])) {
            $query->where('reviewed', $args['reviewed']);
        }

        // Get the sub-offer orders only
        $query->where(function ($query) {
            $query->whereNull('offer_id')
                ->orWhere(function ($query) {
                    $query->whereNotNull('offer_id')
                        ->whereNotNull('parent_offer_order_id');
                });
        });

        // Apply ordering by 'order_date' in descending direction
        $query->orderBy('order_date', 'DESC');

        // Apply pagination
        $first = $args['first'] ?? 12; // Default to 12 items per page
        $page = $args['page'] ?? 1; // Default to page 1

        // Return LengthAwarePaginator directly (instead of array)
        return $query->paginate($first, ['*'], 'page', $page);
    }
}
