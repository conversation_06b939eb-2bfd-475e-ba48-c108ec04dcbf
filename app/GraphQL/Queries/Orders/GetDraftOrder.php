<?php

namespace App\GraphQL\Queries\Orders;

use App\Helpers\Constant;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Ordering\Order;
use App\Scopes\NonDraftScope;
use App\Services\Order\CouponService;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetDraftOrder
{
    protected CouponService $couponService;

    public function __construct(CouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        //prepare the response vars
        $arr = [];
        $error = 0;
        $message = '';

        //get the brand & branch by IDs to validate them
        $brand = Brand::find($args['brand_id']);
        $branch = Branch::find($args['branch_id']);
        //check if brand doesnt exist or not published
        if (empty($brand) || $brand->active != 1) {
            $arr['result']['error'] = 1;
            $arr['result']['message'] = __('messages.brand_is_not_active_or_not_found');

            return $arr;
            //check if branch doesnt exist or not published
        } elseif (empty($branch) || $branch->active != 1) {
            $arr['result']['error'] = 1;
            $arr['result']['message'] = __('messages.branch_is_not_active_or_not_found');

            return $arr;
            //check if branch doesnt belong to brand
        } elseif ($branch->brand_id != $brand->id) {
            $arr['result']['error'] = 1;
            $arr['result']['message'] = __('messages.branch_doesnt_belong_to_brand');

            return $arr;
            //find or create new draft order
        } else {
            $order = Order::withoutGlobalScope(NonDraftScope::class)->isNotOffer()->firstOrCreate(
                [
                    'brand_id' => $args['brand_id'],
                    'branch_id' => $args['branch_id'],
                    'user_id' => Auth::user()->id,
                    'is_draft' => '1',
                ],
                [
                    'order_number' => floor(microtime(true) * 10000),
                    'qr_code' => floor(microtime(true) * 10000),
                ]
            );

            //check if the current coupon is bankOffer
            //then this means that user left the confirmation page
            //and now the bankOffer coupon is not selected
            //then remove it to let him select it again if he wants
            //and lastly we will set the auto_apply to 1 to let him have the register coupon if exist
            if (!empty($order->coupon) && $order->coupon->coupon_type == Constant::CouponBankOfferType) {
                //this function resets order tickets prices to original price
                //recalculate the order price
                //recalculate the order total_price
                //and lastly remove all coupon related data
                $order = $this->resetOrderPriceToDiscounted($order);
            }
            //reset is_birthday so that if draft order is set as is_birthday
            //and user left the confirmation page
            //then he can select normal order instead
            $order->is_birthday = 0;

            if ($order->auto_apply_coupon != '0') {
                //check if user is eligible for register coupon
                if ($coupon = $this->couponService->checkUserEligibleForRegisterCoupon()) {
                    //validate the coupon itself
                    if ($couponDetails = $this->couponService->validateCoupon($order, $coupon)) {
                        if ($couponDetails['error'] == 0) {
                            $order->coupon_id = $coupon->id;
                            $order->coupon_type = $coupon->coupon_type;
                            $order->coupon_discount = $couponDetails['discount'];
                        } else {
                            $error = 1;
                            $message = $couponDetails['message'];
                        }
                    }
                }
            }

            //set the rest of the order data
            $order->governorate_id = $branch->governorate->id;

            $order->name = Auth::user()->name;
            $order->email = Auth::user()->email;
            $order->mobile = Auth::user()->mobile;

            $order->device_uuid = \Request::header('device_uuid');
            $order->app_version = \Request::header('version');
            $order->order_uuid = Str::orderedUuid();

            $order->save();

            $arr['order'] = $order;
            $arr['result']['error'] = $error;
            $arr['result']['message'] = $message;

            return $arr;
        }
    }

    private function resetOrderPriceToDiscounted($order)
    {
        if (!empty($order->tickets)) {
            foreach ($order->tickets as $ticket) {
                $ticket->total_price = $ticket->price * $ticket->quantity;
                $ticket->save();
            }
        }

        $orderPrice =
            $order->tickets->sum('total_price') +
            $order->bundles->sum('total_price') +
            $order->addons->sum('total_price');
        $order->price = $orderPrice;
        $order->total_price = $orderPrice;
        $order->discount = 0;
        $order->coupon_discount = 0;
        $order->coupon_id = null;
        $order->coupon_type = 0;
        $order->auto_apply_coupon = '1';
        $order->save();

        return $order;
    }
}
