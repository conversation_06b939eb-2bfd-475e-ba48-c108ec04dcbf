<?php

namespace App\GraphQL\Queries\Orders;

use App\Models\Ordering\Order;
use App\Services\Payment\WalletService;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetOrderById
{
    protected WalletService $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        //prepare the response vars
        $arr = [];
        $error = 0;
        $message = '';

        if ($order = Order::where('user_id', Auth::user()->id)->where('id', $args['id'])->first()) {
            $order->partial_payments = $this->walletService->getTheOrderPayments($order);

            return $order;
        }
    }
}
