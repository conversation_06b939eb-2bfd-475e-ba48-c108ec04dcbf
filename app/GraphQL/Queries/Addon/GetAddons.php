<?php

namespace App\GraphQL\Queries\Addon;

use App\Models\Booking\Addon\Addon;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetAddons
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $branch_id = $args['branch_id'];

        return Addon::publish()
            ->ordered()
            ->where('brand_id', $args['brand_id'])
            ->whereHas('branches', function ($query) use ($branch_id) {
                $query->where('branch_id', $branch_id);
            })
            ->get();
    }
}
