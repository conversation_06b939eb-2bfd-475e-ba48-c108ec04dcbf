<?php

namespace App\GraphQL\Queries\Branches;

use App\Models\Brand\Branch;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class branchesByBrand
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $branches = Branch::where('brand_id', $args['brand_id'])
            ->orderBy('position', 'ASC')
            ->publish()
            ->get();

        return ['data' => $branches, 'error' => $context];
    }
}
