<?php

namespace App\GraphQL\Queries\Brand;

use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetBrand
{
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $governorate_id = $args['governorate_id'];
        $branchIds = Branch::where('brand_id', $args['id'])
            ->where('governorate_id', $args['governorate_id'])
            ->pluck('id')
            ->toArray();
        $brand = Brand::where('id', $args['id'])
            ->with([
                'branches' => function ($query) use ($governorate_id) {
                    $query->where('governorate_id', $governorate_id);
                },
                'tickets' => function ($ticketQuery) use ($branchIds) {
                    $ticketQuery->whereHas('branches', function ($q) use ($branchIds) {
                        $q->whereIn('branch_id', $branchIds);
                    });
                },
            ])
            ->first();

        return $brand;
    }
}