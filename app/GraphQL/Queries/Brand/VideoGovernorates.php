<?php

namespace App\GraphQL\Queries\Brand;

use App\Models\Video\Video;
use App\Models\Video\VideoGovernorate;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class VideoGovernorates
{
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $video = null;
        $videoGovernorate = VideoGovernorate::where('governorate_id', $args['governorate_id'])
            ->orderBy('id', 'desc')
            ->first();
        if (!empty($videoGovernorate)) {
            $video = Video::publish()
                ->ordered()
                ->where('brand_id', $args['brand_id'])
                ->where('id', $videoGovernorate->video_id)
                ->first();
        }

        return $video;
    }
}
