<?php

namespace App\GraphQL\Queries\Embed;

use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetEmbedCardById
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        // $data = ['username' => '<EMAIL>', 'password' => 'Aa12341234'];
        $user = auth()->user();

        $service = new EmbedService;
        $response = $service->cardById($args['card_number'], $user->id);

        if ($response['code'] != 200) {
            return null;
        }
        $card = [
            'balance' => $response['result']->balance->cash_balance ?? 0,
            'bonus' => $card->balance->bonus_balance ?? 0,
            'status' => $card->balance->card_state ?? '',

            'bar_code' => $response['result']->bar_code ?? '',
            'card_number' => $response['result']->card_number ?? '',
            'nick_name' => $response['result']->nick_name ?? '',
        ];

        return $card;
    }
}
