<?php

namespace App\GraphQL\Queries\Embed;

use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetEmbedCardActivities
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        // $user = ['username' => '<EMAIL>', 'password' => 'Aa12341234'];
        $user = auth()->user();

        $cards = [];
        $service = new EmbedService();
        $response = $service->getActivityByCardNumber($user->id, $args['card_number']);
        /*
   "card_number": "<string>",
    "bonus_balance": "<double>",
    "cash_balance": "<double>",
    "cumulative_balance": "<double>",
    "running_cumulative_balance": "<double>",
    "p_cash": "<double>",
    "e_cash": "<double>",
    "e_tickets": "<integer>",
    "daily_free_games": "<integer>",
    "free_games": "<integer>",
    "apple_wallet": "<boolean>",
    "google_pay": "<boolean>",
    "last_modified_date": "<dateTime>",
    "card_state": "<string>",
    "date_time": "<dateTime>",
    "reason": "<string>",
    "description": "<string>",
    "reference_id": "<string>",
    "parent_id": "<string>",
    "location_name": "<string>",
    "reissued_to_card_barcode": "<string>",
    "reissued_from_card_barcode": "<string>"*/

        if ($response['code'] != 200) {
            return [];
        }
        $cards = collect($response['result'])
            ->map(function ($card) {
                return [
                    'transaction_amount' => $card->cash_balance ?? 0,
                    'transaction_type' => $card->transaction_type ?? 0,
                    'transaction_reason' => $card->description ?? $card->reason,
                    'created_at' => $card->date_time ?? '',
                    'location_name' => $card->location_name ?? '',
                ];
            })
            ->toArray();

        return $cards;
    }
}
