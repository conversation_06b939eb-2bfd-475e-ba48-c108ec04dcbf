<?php

namespace App\GraphQL\Queries\Embed;

use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetEmbedCards
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        // ['username' => '<EMAIL>', 'password' => 'Aa12341234']
        $user = auth()->user();
        $cards = [];
        $service = new EmbedService;
        $response = $service->cards($user->id);
        if ($response['code'] != 200) {
            return [];
        }
        $cards = collect($response['result']->content)
            ->map(function ($card) {
                return [
                    'balance' => $card->balance->cash_balance ?? 0,
                    'bonus' => $card->balance->bonus_balance ?? 0,
                    'status' => $card->balance->card_state ?? '',
                    'bar_code' => $card->bar_code ?? '',
                    'card_number' => $card->card_number ?? '',
                    'nick_name' => $card->nick_name ?? '',
                ];
            })
            ->toArray();

        return $cards;
    }
}
