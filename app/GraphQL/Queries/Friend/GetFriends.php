<?php

namespace App\GraphQL\Queries\Friend;

use App\Models\Invitation\Friend;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetFriends
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $user = auth()->user();
        if ($args['status'] == 2) {
            //get received invitation
            $invitations = Friend::query()
                ->pending()
                ->where('user_id', $user->id);
        } elseif ($args['status'] == 1) {
            $invitations = Friend::query()
                ->Friend($user->id)
                ->where('status', $args['status']);
        } elseif ($args['status'] == 0) {
            //send request
            $invitations = Friend::query()
                ->where('sender_id', $user->id)
                ->pending();
        } else {
            $invitations = Friend::query()->Friend($user->id);
        }

        return $invitations->paginate(30);
    }
}
