<?php

namespace App\GraphQL\Queries\Group;

use App\Models\Group\Group;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetGroup
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return Group::dynamic()
            ->publish()
            ->orderBy('start_spending_thresholds', 'asc')
            ->get();
    }
}