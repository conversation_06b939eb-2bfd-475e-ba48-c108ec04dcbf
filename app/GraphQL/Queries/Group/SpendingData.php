<?php

namespace App\GraphQL\Queries\Group;

use App\Helpers\Constant;
use App\Models\Group\Group;
use App\Models\Ordering\Order;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SpendingData
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $remainingSpending = 0;
        $user = auth()->user();
        $totalSpending = Order::where('user_id', $user->id)
            ->where('payment_status', Constant::PaidOrder) // Filter by paid orders
            ->where(function ($query) {
                $query->where('status', Constant::ClaimOrder)->orWhere('status', Constant::NoShowOrder);
            })
            ->sum('total_price');

        $next_group = Group::where('start_spending_thresholds', '>', $totalSpending)->first();

        $current_group = $user->groups->where('type', 'dynamic')->first();

        //        $validCurrentGroup = Group::where('type','dynamic')->where("active","1")->where('start_spending_thresholds', '<=', $totalSpending)
        //            ->where('end_spending_thresholds', '>=', $totalSpending)->first();
        //        dd($validCurrentGroup);

        //        $firstDynamicGroup = Group::where('type','dynamic')->where("active","1")->where("end_spending_thresholds",">","0")->orderBy("end_spending_thresholds","asc")->first()->id;
        //        dd($firstDynamicGroup);

        if (!empty($next_group)) {
            $remainingSpending = $next_group->start_spending_thresholds - $totalSpending;
        } else {
            $next_group = $current_group;
        }

        if (!empty($current_group) && $current_group->end_spending_thresholds < $totalSpending) {
            $actual_group = Group::where(function ($query) use ($totalSpending) {
                $query
                    ->where(function ($query) use ($totalSpending) {
                        $query
                            ->where('start_spending_thresholds', '<', $totalSpending)
                            ->where('end_spending_thresholds', '>', $totalSpending);
                    })
                    ->orWhere('start_spending_thresholds', $totalSpending)
                    ->orWhere('end_spending_thresholds', $totalSpending);
            })->first();
            if (!empty($actual_group)) {
                $user->groups()->detach($current_group->id);
                $user->groups()->attach($actual_group->id);
            }
        }

        $spendingData = [
            'currentSpending' => $totalSpending,
            'remainingSpending' => $remainingSpending,
            'nextGroupData' => $next_group,
        ];

        return $spendingData;
    }
}