<?php

namespace App\GraphQL\Queries\Category;

use App\Helpers\FoodicsApi;
use App\Models\Brand\Brand;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use App\Services\Foodics\FoodicsService;

class GetCategory
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $brand = Brand::where('id', $args['brand_id'])->first();
        $service  = new FoodicsService();
        return $service->getCategories($brand);
    }
}
