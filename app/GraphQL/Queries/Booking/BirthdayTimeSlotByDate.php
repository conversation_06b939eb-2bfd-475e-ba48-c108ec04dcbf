<?php

namespace App\GraphQL\Queries\Booking;

use App\Helpers\General;
use App\Models\Booking\Addon\BirthdaySetting;
use App\Models\Brand\Branch;
use App\Models\Brand\WorkHour;
use App\Models\Ordering\Order;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class BirthdayTimeSlotByDate
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        date_default_timezone_set('Asia/Riyadh');
        $slots = [];
        $slotData = [];
        // check if date equal today or tomorrow return time slot is empty
        $result = ['error' => 0, 'message' => ''];
        $branch = Branch::where('id', $args['branch_id'])->first();
        if (strtotime($args['date']) < strtotime(date('Y-m-d'))) {
            $result = ['error' => 1, 'message' => __('messages.Date_Not_Support')];

            return [
                'slots' => $slotData,
                'result' => $result,
            ];
        }
        // if (strtotime($args['date']) <= strtotime(date('Y-m-d', strtotime('tomorrow')))) {
        //     $result = ['error' => 1, 'message' => __('messages.No_Time_Available')];

        //     return [
        //         'slots' => $slotData,
        //         'result' => $result,
        //     ];
        // }

        if (is_null($branch)) {
            $result = ['error' => 1, 'message' => __('messages.Branch_Not_Found')];

            return [
                'slots' => $slotData,
                'result' => $result,
            ];
        } else {
            // slots
            $checkDateExist = WorkHour::where('branch_id', $branch->id)
                ->where('date', $args['date'])
                ->where('type', 0)
                ->where('day_id', 0)
                ->whereNull('from_time')
                ->whereNull('to_time')
                ->first();

            if (!empty($checkDateExist)) {
                $result = ['error' => 1, 'message' => __('messages.Date_Closed')];

                return [
                    'slots' => $slotData,
                    'result' => $result,
                ];
            }
            $day_id = null;
            $day = date('l', strtotime($args['date']));
            $constant = (new General())->getConstantItemByTitle('Days', $day);

            if ($constant) {
                // get slots (sperated by hour) for birthday setting time
                $day_id = $constant['id'];

                $workingDay = BirthdaySetting::where('branch_id', $branch->id)
                    ->where('day_id', $day_id)
                    ->where('type', operator: 1)
                    ->first();

                if ($workingDay) {
                    $start = new \DateTime($workingDay->from_time);
                    $end = new \DateTime($workingDay->to_time);
                    $interval = new \DateInterval('PT1H');
                    $periods = new \DatePeriod($start, $interval, $end);

                    foreach ($periods as $period) {
                        $timeSlot = $period->format('H:i:s');
                        $slotData[] = [
                            'time' => $timeSlot,
                            'break_duration' => $workingDay->break_duration,
                            'available' => true,
                        ];
                    }
                }
            }
        }

        if (count($slotData) < 1) {
            $result = ['error' => 1, 'message' => __('messages.No_Time_Available')];
        }

        if (count($slotData) > 0) {
            $birthdayOrders = Order::where('branch_id', $args['branch_id'])
                ->whereDate('order_date', date('Y-m-d', strtotime($args['date'])))
                ->where('is_birthday', '1')
                ->where('status', '!=', '2')
                ->where('payment_status', '1');
            if (!empty($args['exclude_order_id'])) {
                $birthdayOrders = $birthdayOrders->where('id', '!=', $args['exclude_order_id']);
            }
            $birthdayOrders = $birthdayOrders->pluck('order_time')
                ->toArray();

            // Filter slotData
            $filteredSlots = array_filter($slotData, function ($slot) use ($birthdayOrders, $workingDay, $args) {
                $currentTime = strtotime($slot['time']);
                if ($args['date'] == date('Y-m-d')) {
                    $actualTime = date('H:i:s');
                    if ($actualTime > $slot['time']) {
                        return false; // Remove this slot
                    }
                }

                foreach ($birthdayOrders as $birthdayTime) {
                    $birthdayTimestamp = strtotime($birthdayTime);
                    $breakHoursBefore = strtotime('-'.($workingDay->break_duration + ($workingDay->break_duration - 1)).' hours', $birthdayTimestamp);
                    $breakHoursAfter = strtotime('+'.($workingDay->break_duration + ($workingDay->break_duration - 1)).' hours', $birthdayTimestamp);

                    // If current slot time falls within ±4 hours of any birthday order
                    if ($currentTime >= $breakHoursBefore && $currentTime <= $breakHoursAfter) {
                        return false; // Remove this slot
                    }
                }

                return true; // Keep this slot
            });

            $slotData = array_values($filteredSlots); // Reset array keys
        }

        return [
            'slots' => $slotData,
            'result' => $result,
        ];
    }
}
