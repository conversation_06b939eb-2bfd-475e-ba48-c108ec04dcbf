<?php

namespace App\GraphQL\Queries\Product;

use App\Services\Foodics\FoodicsService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetFoodicsGroupProducts
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $service = new FoodicsService();

        return $service->GetGroupProducts($args['brand_id'], $args['branch_id']);
    }
}