<?php

namespace App\GraphQL\Queries\Product;

use App\Services\Foodics\FoodicsService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetFoodicsProducts
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $service = new FoodicsService();

        return $service->GetProducts($args['brand_id'], $args['category_id']);
    }
}