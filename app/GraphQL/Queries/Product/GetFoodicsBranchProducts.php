<?php

namespace App\GraphQL\Queries\Product;

use App\Services\Foodics\FoodicsService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetFoodicsBranchProducts
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $service = new FoodicsService();
        $products = $service->GetBranchProducts($args['brand_id'], $args['branch_id']);

        return $products;
    }
}
