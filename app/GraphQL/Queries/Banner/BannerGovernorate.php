<?php

namespace App\GraphQL\Queries\Banner;

use App\Models\Banner\Banner;
use GraphQL\Type\Definition\ResolveInfo;
use Log;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class BannerGovernorate
{
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $banners = Banner::home()
            ->publish()
            ->ordered()
            ->Valid()
            ->with('branch', 'brand');
        if (isset($args['governorate_id'])) {
            $governorate_id = $args['governorate_id'];
            $banners = $banners->where(function ($q) use ($governorate_id) {
                $q->whereNull('branch_id')->orWhereHas('branch', function ($query) use ($governorate_id) {
                    $query->where('governorate_id', $governorate_id);
                });
            });
        }
        // $arr['banners'] = $banners->get();
        // $arr['banners'] = $banners->without('brand')->get();

        // $arr['size'] = round(strlen(json_encode($arr['banners'])) / (1024 * 1024), 2) . ' MB';

        $banners = $banners->without('brand')->get();

        return $banners;
    }
}