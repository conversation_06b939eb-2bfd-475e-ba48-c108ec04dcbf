<?php

namespace App\GraphQL\Queries\Banner;

use App\Models\Banner\Banner;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class BannerBrand
{
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $banners = Banner::publish()
            ->ordered()
            ->Valid()
            ->with('branch', 'brand')
            ->where('brand_id', $args['brand_id']);
        if (isset($args['governorate_id'])) {
            $governorate_id = $args['governorate_id'];
            $banners = $banners->where(function ($q) use ($governorate_id) {
                $q->whereNull('branch_id')->orWhereHas('branch', function ($query) use ($governorate_id) {
                    $query->where('governorate_id', $governorate_id);
                });
            });
        }
        $banners = $banners->get();

        return $banners;
    }
}