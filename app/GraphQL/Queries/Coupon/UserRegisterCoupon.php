<?php

namespace App\GraphQL\Queries\Coupon;

use App\Models\Ordering\Order;
use App\Models\Promotion\Coupon\Coupon;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UserRegisterCoupon
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        //coupon register
        $order = 0;
        $device_uuid = \Request::header('device_uuid');
        $coupon = Coupon::Register()
            ->where('user_id', auth()->user()->id)
            ->Publish()
            ->first();
        if (! empty($coupon)) {
            $order = Order::where(function ($q) use ($device_uuid) {
                $q->where('user_id', auth()->user()->id);
                $q->orWhere('device_uuid', $device_uuid);
            })
//                ->where('coupon_id', $coupon->id)
                ->count();

            if ($order > 0) {
                $coupon = null;
            }
        }

        return $coupon;
    }
}
