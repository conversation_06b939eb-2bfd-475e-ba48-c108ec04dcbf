<?php namespace App\GraphQL\Queries\Coupon;

use App\Models\Ordering\Order;
use App\Models\Promotion\Coupon\Coupon;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UserCoupons
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        //coupon register
        $couponsArray = [];
        $device_uuid = \Request::header('device_uuid');

        $groups = auth()
            ->user()
            ->groups->pluck('id')
            ->toArray();
        //get coupon after orders and get coupon after register

        $coupons = Coupon::where(function ($q) use ($groups) {
            $q->where(function ($query) {
                $query->Register()->where('user_id', auth()->user()->id);
            });
            $q->orWhere(function ($query) {
                $query->Order()->where('user_id', auth()->user()->id);
            });
            $q->orWhere(function ($query) use ($groups) {
                $query->whereIn('group_id', $groups)->GroupType();
            });
            $q->orWhere(function ($query) {
                $query->BirthDay()->where('user_id', auth()->user()->id);
            });
        })

            ->Publish()
            ->pluck('maximum_usage', 'id')
            ->toArray();

        if (!empty($coupons)) {
            foreach ($coupons as $coupon => $maximum_usage) {
                $orderCount = Order::where(function ($q) use ($device_uuid) {
                    $q->where('user_id', auth()->user()->id);
                    $q->orWhere('device_uuid', $device_uuid);
                })
                    ->where('coupon_id', $coupon)
                    ->count();

                if ($maximum_usage > $orderCount) {
                    array_push($couponsArray, $coupon);
                }
            }
        }

        $coupons = Coupon::whereIn('id', $couponsArray)
            ->Publish()
            ->Valid()
            ->paginate();

        return $coupons;
    }
}