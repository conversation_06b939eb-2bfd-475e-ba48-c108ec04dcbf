<?php

namespace App\GraphQL\Queries\Feedback;

use App\Models\Feedback\Subject\Subject;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetSubjects
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        if (isset($args['brand_id']) && $args['brand_id'] != '0') {
            $brand = $args['brand_id'];
            $subjects = Subject::publish()
                ->ordered()
                ->whereHas('brands', function ($q) use ($brand) {
                    $q->where('brand_id', $brand);
                })
                ->get();
        } else {
            $subjects = Subject::publish()
                ->ordered()
                ->doesntHave('brands')
                ->get();
        }

        return $subjects;
    }
}
