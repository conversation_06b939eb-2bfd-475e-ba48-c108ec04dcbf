<?php

namespace App\GraphQL\Queries\Survey;

use App\Models\Booking\Category\Category;
use App\Models\Ordering\Order;
use App\Models\Survey\Survey\Survey;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetSurveys
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $order = Order::where('id', $args['order_id'])
            ->where('user_id', auth()->user()->id)
            ->first();
        if (!$order) {
            $message = __('messages.Order_Not_Found');
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }

        $surveys = Survey::publish()
            ->ordered()
            ->with('category')
            ->where('brand_id', $order->brand_id)
            ->whereHas('branches', function ($q) use ($order) {
                $q->where('branch_id', $order->branch_id);
            })
            ->get()
            ->groupBy('category_id');

        $data = [];

        if (!empty($surveys) && count($surveys) > 0) {
            foreach ($surveys as $key => $item) {
                array_push($data, [
                    'category' => Category::find($key),
                    'questions' => $item,
                ]);
            }
        }

        return $data;
    }
}