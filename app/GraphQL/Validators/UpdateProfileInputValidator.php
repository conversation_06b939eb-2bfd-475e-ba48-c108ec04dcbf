<?php

namespace App\GraphQL\Validators;

use App\Rules\ActiveUser;
use App\Rules\CheckSpaceOnStartOfStringRule;
use App\Rules\CountryInputCodeRule;
use Nuwave\Lighthouse\Validation\Validator;

class UpdateProfileInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        $user = auth()->user();

        return [
            // TODO Add your validation rules
            'first_name' => ['required', 'min:2', 'max:50'],
            'last_name' => ['required', 'min:2', 'max:50'],
            'name' => [
                // 'required',
                'min:2',
                'max:50',
                //    new  CheckSpaceOnStartOfStringRule
            ],
            // 'email' => [
            //     new ActiveUser('register'),
            //     'required',
            //     'unique:users,email,' . $user->id . ',id',
            //     'email:rfc,dns',
            //     'bail',
            // ],

            'mobile' => ['nullable', 'min:6', 'unique:users,mobile,' . $user->id . ',id,deleted_at,NULL', 'phone:auto'],
            'nationality' => ['nullable', new CountryInputCodeRule()],

            'country' => ['nullable', new CountryInputCodeRule()],

            //'date_of_birth' => ['nullable', 'before:tomorrow'],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'email.unique' => trans('validation.custom.email.unique'),
            'mobile.unique' => trans('validation.mobile.unique'),
        ];
    }
}