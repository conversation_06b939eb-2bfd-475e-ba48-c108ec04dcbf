<?php

namespace App\GraphQL\Validators;

use Nuwave\Lighthouse\Validation\Validator;

class submitSurveyResultInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            'order_id' => ['required', 'exists:orders,id'],
            'questions' => ['required', 'array'],
            'questions.*.id' => ['required', 'exists:surveys,id'],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'questions.required' => trans('validation.required'),
            'order_id.required' => trans('validation.required'),
        ];
    }
}
