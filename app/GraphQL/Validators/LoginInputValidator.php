<?php

namespace App\GraphQL\Validators;

use App\Rules\ActiveUser;
use Illuminate\Support\Facades\Request;
use Nuwave\Lighthouse\Validation\Validator;

class LoginInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules

            'email' => ['required', 'email:rfc,dns', 'bail', new ActiveUser('login')],
        ];
    }

    public function messages(): array
    {
        $supported_languages = config('translatable.locales');
        if (
            ! Request::header('Accept-Language') ||
            ! in_array(Request::header('Accept-Language'), $supported_languages)
        ) {
            $locale = config('translatable.locale');
        } else {
            $locale = Request::header('Accept-Language');
        }

        app()->setLocale($locale);

        return [
            'email.email' => trans('validation.custom.email.email'),
        ];
    }
}
