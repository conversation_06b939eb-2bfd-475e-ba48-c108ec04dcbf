<?php

namespace App\GraphQL\Validators;

use App\Rules\ActiveUser;
use App\Rules\CheckSpaceOnStartOfStringRule;
use App\Rules\EmailExist;
use Nuwave\Lighthouse\Validation\Validator;

class ResetPasswordInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules
            'email' => ['required_without:mobile', new EmailExist(), 'email:rfc,dns',
            new ActiveUser('login'), 'bail',
            new CheckSpaceOnStartOfStringRule(),
            ],
            'mobile' => ['required_without:email', 'required_if:via,sms', 'min:6'],
            'password' => ['required', 'confirmed'],
            'code' => ['required_if:token,=,null'],
            'token' => ['required_if:code,=,null'],
        ];
    }
}
