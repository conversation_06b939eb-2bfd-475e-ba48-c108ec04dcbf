<?php

namespace App\GraphQL\Validators;

use App\Rules\ActiveUser;
use App\Rules\CheckSpaceOnStartOfStringRule;
use App\Rules\EmailExist;
use Nuwave\Lighthouse\Validation\Validator;

class VerifyCodeInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules
            'email' => [
                'required_without:mobile',
                new EmailExist(),
                'email:rfc,dns',
                new ActiveUser('login'),
                'bail',
                new CheckSpaceOnStartOfStringRule(),
            ],

            'mobile' => ['required_without:email', 'min:6'],
            'code' => ['required'],
        ];
    }
}
