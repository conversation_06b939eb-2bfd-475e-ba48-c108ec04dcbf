<?php

namespace App\GraphQL\Validators;

use Illuminate\Validation\Rule;
use Nuwave\Lighthouse\Validation\Validator;

class OrganizeEmailVerificationInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            'organizational_email' => [
                'required',
                'email',
                Rule::unique('users', 'organizational_email')
                    ->ignore(auth()->user()->id)
                    ->whereNull('deleted_at'),
            ],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'organizational_email.unique' => trans('validation.custom.email.unique'),
            'organizational_email.required' => trans('validation.custom.email.email'),
        ];
    }
}
