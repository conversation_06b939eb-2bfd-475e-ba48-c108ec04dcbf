<?php

namespace App\GraphQL\Validators;

use App\Rules\ReCaptcha;
use App\Rules\ActiveUser;
use Illuminate\Validation\Rule;
use App\Rules\CountryInputCodeRule;
use Nuwave\Lighthouse\Validation\Validator;
use App\Rules\CheckSpaceOnStartOfStringRule;

class RegisterInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules
            'first_name' => ['required', 'min:2', 'max:50'],
            'last_name' => ['required', 'min:2', 'max:50'],
            'name' => [
                // 'required',
                'min:2',
                'max:50',
                //    new  CheckSpaceOnStartOfStringRule
            ],
            'email' => [
                new ActiveUser('register'),
                'required',
                'unique:users,email,NULL,id,deleted_at,NULL',
                'email:rfc,dns',
                'bail',
            ],
            'mobile' => [
                'nullable',
                'min:6',
                'unique:users,mobile,NULL,id,deleted_at,NULL',
                Rule::phone()->international(),
            ],

            'country' => ['nullable', new CountryInputCodeRule()],
            'date_of_birth' => ['nullable', 'before:tomorrow'],
            'recaptcha' => ['required', new ReCaptcha()],
           
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'mobile.required' => trans('validation.required'),
            'mobile.phone' => trans('validation.mobile.phone'),
            'mobile.unique' => trans('validation.mobile.unique'),
            'email.unique' => trans('validation.custom.email.unique'),
            'email.email' => trans('validation.custom.email.email'),
        ];
    }
}