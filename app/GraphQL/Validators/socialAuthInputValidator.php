<?php

namespace App\GraphQL\Validators;

use App\Rules\CountryInputCodeRule;
use Illuminate\Validation\Rule;
use Nuwave\Lighthouse\Validation\Validator;

class socialAuthInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules
            'first_name' => ['required', 'min:2', 'max:50'],
            'last_name' => ['required', 'min:2', 'max:50'],
            'email' => ['required', 'email:rfc,dns', 'bail'],
            'mobile' => ['nullable', 'min:6', Rule::phone()->international()],
            'provider_id' => ['required', 'unique:providers,provider_id,NULL,id,deleted_at,NULL'],
            'country' => ['nullable', new CountryInputCodeRule()],
            'date_of_birth' => ['nullable', 'before:tomorrow'],
            'provider' => ['required', 'in:facebook,twitter,apple,google'],
        ];
    }

    public function messages(): array
    {
        return [
            'mobile.required' => trans('validation.required'),
            'mobile.phone' => trans('validation.mobile.phone'),
            'mobile.unique' => trans('validation.mobile.unique'),
            'email.unique' => trans('validation.custom.email.unique'),
            'email.email' => trans('validation.custom.email.email'),
        ];
    }
}
