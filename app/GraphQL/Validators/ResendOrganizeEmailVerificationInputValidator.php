<?php

namespace App\GraphQL\Validators;

use Nuwave\Lighthouse\Validation\Validator;

class ResendOrganizeEmailVerificationInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            'organizational_email' => ['required', 'email'],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'organizational_email.required' => 'Make Sure You Enter Valid Email',
            'organizational_email.email' => 'Make Sure You Enter Valid Email',
        ];
    }
}