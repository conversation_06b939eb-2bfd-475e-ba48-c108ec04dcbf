<?php

namespace App\GraphQL\Validators;

use Nuwave\Lighthouse\Validation\Validator;

class CheckMobileExistInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            'mobile' => ['required', 'phone:auto', 'unique:users,mobile,NULL,id,deleted_at,NULL'],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'mobile.unique' => trans('validation.unique', ['attribute' => __('attributes.mobile')]),
            'mobile.required' => trans('validation.required', ['attribute' => __('attributes.mobile')]),
        ];
    }
}
