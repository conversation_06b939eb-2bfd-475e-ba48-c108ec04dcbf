<?php

namespace App\GraphQL\Validators;

use Nuwave\Lighthouse\Validation\Validator;

class CheckEmailExistInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules
            'email' => ['required', 'email', 'unique:users,email,NULL,id,deleted_at,NULL', 'email:rfc,dns', 'bail'],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));

        return [
            'email.unique' => trans('validation.custom.email.unique'),
            'mobile.email' => trans('validation.email', ['attribute' => __('attributes.email')]),
            'mobile.required' => trans('validation.email', ['attribute' => __('attributes.email')]),
        ];
    }
}
