<?php

namespace App\GraphQL\Validators;

use App\Rules\CheckSpaceOnStartOfStringRule;
use App\Rules\EmailExist;
use Nuwave\Lighthouse\Validation\Validator;
use Illuminate\Validation\Rule;

class ResendVerificationCodeInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // TODO Add your validation rules
            'email' => [
                'required_without:mobile',
                new EmailExist(),
                'email:rfc,dns',
                'bail',
                new CheckSpaceOnStartOfStringRule(),
            ],

            'mobile' => ['required_without:email', 'required_if:via,sms', 'min:6'],
        ];
    }

    public function messages(): array
    {
        \App::setLocale(\Request::header('Accept-Language'));
        return [
            'email.unique' => trans('validation.custom.email.unique'),
        ];
    }
}
