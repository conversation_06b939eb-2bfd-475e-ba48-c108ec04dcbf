<?php

namespace App\GraphQL\Directives;

use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Request;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class LocalizationDirective extends BaseDirective implements FieldMiddleware
{
    public static function definition(): string
    {
        return /* @lang GraphQL */ <<<'GRAPHQL'
directive @localization on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn (callable $previousResolver) => function (
                $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($previousResolver) {
                $locale = config('translatable.locale');

                $supported_languages = config('translatable.locales');
                if (
                    ! Request::header('Accept-Language') ||
                    ! in_array(Request::header('Accept-Language'), $supported_languages)
                ) {
                    $locale = config('translatable.locale');
                } else {
                    // $locale = Request::header('Accept-Language');
                    $locale = (new \App\Helpers\AcceptLanguage)->parseHeader(Request::header('Accept-Language'));
                }

                app()->setLocale($locale);
                config(['translatable.locale' => $locale]);

                return $previousResolver($root, $args, $context, $resolveInfo);
            }
        );
    }
}