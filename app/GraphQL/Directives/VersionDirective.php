<?php

namespace App\GraphQL\Directives;

use Closure;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class VersionDirective extends BaseDirective implements FieldMiddleware
{
    public $result;

    public static function definition(): string
    {
        return /* @lang GraphQL */ <<<'GRAPHQL'
directive @version on FIELD_DEFINITION
GRAPHQL;
    }
    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(function (callable $previousResolver) {
            return function (
                $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($previousResolver) {
                $supported_version = config('app.version');
                $version = \Request::header('version');

                if (!isset($version) || (isset($version) && version_compare($supported_version, $version, '>'))) {
                    $this->result = ['error' => 1, 'message' => __('messages.VersionNotSupported'), 'update' => true];

                    return [
                        'valid' => 0,
                        'data' => [],
                        'result' => $this->result,
                    ];
                }

                return $previousResolver($root, $args, $context, $resolveInfo);
            };
        });
    }
}
