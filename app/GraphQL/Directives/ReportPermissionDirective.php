<?php

namespace App\GraphQL\Directives;

use Closure;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use App\Exceptions\CustomErrorHandler;
use Nuwave\Lighthouse\Exceptions\DefinitionException;

class ReportPermissionDirective extends BaseDirective implements FieldMiddleware
{
    public static function definition(): string
    {
        return /*
             * @lang GraphQL
             */ <<<'GRAPHQL'
directive @reportPermission on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(function (callable $previousResolver) {
            return function (
                $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($previousResolver) {
                $user = $context->user();

                if (!empty($user) && !$user->hasRole('api-reports')) {
                    throw new CustomErrorHandler('401 you don\'t have permission to do this', true, 'FAIL', false, 401);
                }

                return $previousResolver($root, $args, $context, $resolveInfo);
            };
        });
    }
}
