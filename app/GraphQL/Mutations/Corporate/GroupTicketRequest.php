<?php

namespace App\GraphQL\Mutations\Corporate;

use App\Models\Booking\Ticket\Ticket;
use App\Models\Corporate\Corporate;
use App\Models\Corporate\GroupTicket\CorporateTicket;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\DB;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GroupTicketRequest
{
    public $error = 0;
    public $message = '';
    public $orderId = null;
    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $args['type'] = 1;
        $this->response = [];
        try {
            DB::beginTransaction();
            $corporate = Corporate::create($args);
            //add ticket request
            $this->storeTicketItems($corporate->id, $args['orderItems']);
            $this->message = __('messages.sendGroupTicketSuccess');
            DB::commit();
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
            $this->error = 1;
            $this->message = __('messages.someThingError');
        }

        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }

    public function storeTicketItems($corporate, $ticketItems)
    {
        foreach ($ticketItems as $item) {
            $bookingItem = Ticket::where('id', $item['id'])->first();
            $ticketingItem = [];
            $ticketingItem['price'] = $bookingItem['price'];
            $ticketingItem['ticket_type'] = $bookingItem['ticket_type'];

            $ticketingItem['quantity'] = $item['quantity'];
            $ticketingItem['total_price'] = $bookingItem['price'] * $item['quantity'];
            $ticketingItem['ticket_id'] = (int) $item['id'];
            $ticketingItem['corporate_id'] = $corporate;
            $ticketingItem['ar']['sub_title'] = $bookingItem->sub_title_ar;
            $ticketingItem['en']['sub_title'] = $bookingItem->sub_title_en;
            $ticketingItem['ar']['title'] = $bookingItem->title_ar;
            $ticketingItem['en']['title'] = $bookingItem->title_en;
            CorporateTicket::create($ticketingItem);
        }

        return true;
    }
}