<?php

namespace App\GraphQL\Mutations\Corporate;

use App\Models\Corporate\Corporate;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class CorporateRequest
{
    public $MODEL_Corporate_PREFIEX = 'App\Models\Corporate';

    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $args['type'] = 0;
        $this->response = [];
        $user = auth()->user();
        $args['user_id'] = $user ? $user['id'] : null;
        if ($Corporate = Corporate::create($args)) {
            $this->message = __('messages.sendCorporateSuccess');
        }
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
