<?php

namespace App\GraphQL\Mutations\Card;

use App\Models\UserCard;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SaveCard
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $user = auth()->user();
        $args['user_id'] = $user ? $user['id'] : null;
        $card = UserCard::updateOrCreate(
            [
                'user_id' => $args['user_id'],
                'year' => $args['year'],
                'month' => $args['month'],
                'card_number' => $args['card_number'],
            ],
            $args
        );
        $this->message = __('messages.CardSaved');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
