<?php

namespace App\GraphQL\Mutations\Card;

use App\Models\UserCard;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class DeleteCard
{
    public $error = 0;
    public $message = '';
    public $orderId = null;
    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $user = auth()->user();

        $card = UserCard::where('id', $args['id'])
            ->where('user_id', $user['id'])
            ->first();
        if (empty($card)) {
            //return error
            $this->error = 1;
            $this->message = __('messages.CardNotFound');

            return $this->getResponse();
        }
        $card->delete();
        $this->message = __('messages.CardDeleted');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
