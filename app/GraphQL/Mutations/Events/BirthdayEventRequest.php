<?php

namespace App\GraphQL\Mutations\Events;

use App\Events\BirthdayEventSubmitted;
use App\Models\Event\BirthdayEvent;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class BirthdayEventRequest
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $this->response = [];
        $user = auth()->user();
        $args['reference_number'] = time();
        $args['user_id'] = $user ? $user->id : null;
        if ($args['date'] == '') {
            $args['date'] = null;
        }
        if ($event = BirthdayEvent::create($args)) {
            event(new BirthdayEventSubmitted($event));
            $this->message = __('messages.sendBirthdayEventSuccess');
        }
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
