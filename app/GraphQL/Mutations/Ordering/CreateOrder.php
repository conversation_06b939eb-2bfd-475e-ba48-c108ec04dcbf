<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Events\CreateFoodicsOrder;
use App\Events\OrderNotification;
use App\Events\ZatcaCreateInvoice;
use App\Helpers\FoodicsApi;
use App\Helpers\General;
use App\Helpers\Promotion\CheckCouponExist;
use App\Helpers\Promotion\ReferralValidation;
use App\Helpers\Promotion\ValidateUserCoupon;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Brand\WorkHour;
use App\Models\Ordering\Addon\OrderAddon;
use App\Models\Ordering\Bundle\OrderBundle;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Models\Ordering\Ticket\OrderTicketExtraInfo;
use App\Models\Promotion\Bundle\Bundle;
use App\Models\Promotion\Coupon\Coupon;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Str;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class CreateOrder
{
    public $MODEL_ORDER_PREFIEX = 'App\Models\Ordering';

    public $MODEL_BOOKING_PREFIEX = 'App\Models\Booking';

    public $MODEL_PROMOTION_PREFIEX = 'App\Models\Promotion';

    public $generalHelper;

    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    protected $valid;

    protected $result;

    //
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General();

        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $device_uuid = \Request::header('device_uuid');
        $app_version = \Request::header('version');
        $referralValidation = null;
        $inValidCoupon = false;
        $this->error = 0;
        $this->message = '';
        $this->response = [];
        $card_digits = $args['card_digits'] ?? null;
        $coupon_code = $args['coupon_code'] ?? null;
        $coupon_available = false;
        $isReferral = false;
        //check if order items added
        if ((!empty($args['orderItems']) && count($args['orderItems']) < 1) || empty($args['orderItems'])) {
            $this->error = 1;
            $this->message = __('messages.Order_Items_Required');
            $this->response = $this->getResponse();

            return $this->getResponse();
        }
        $time = isset($args['order_time']) ? $args['order_time'] : null;
        if ($this->isInOffDates($args['branch_id'], $args['order_date'], $time)) {
            $this->error = 1;
            $this->message = __('messages.Order_Date_In_Off_Dates', [
                'order_date' => date('Y-m-d', strtotime($args['order_date'])),
            ]);
            $this->response = $this->getResponse();

            return $this->getResponse();
        } else {
            $brand_id = $args['brand_id'];
            $user = auth()->user();
            if (isset($args['coupon_code'])) {
                $couponCheck = ReferralValidation::validate($coupon_code, $brand_id, $user);
                if (empty($couponCheck)) {
                    $coupon = CheckCouponExist::validate($coupon_code, $user, $brand_id, $card_digits);
                } else {
                    $coupon = !empty($couponCheck['coupon']) ? $couponCheck['coupon'] : null;
                    $isReferral = !empty($couponCheck['coupon']) ? true : false;
                    $args['referral_code'] = $coupon_code;
                    $args['referral_user_id'] = $couponCheck['referralUser'] ? $couponCheck['referralUser']->id : null;
                }

                if (!empty($coupon)) {
                    $validateUserCoupon = ValidateUserCoupon::validate(
                        $coupon,
                        $brand_id,
                        $device_uuid,
                        $args['order_date'],
                        $isReferral
                    );
                    $this->valid = $validateUserCoupon['valid'];
                    if ($validateUserCoupon['valid'] == 0) {
                        $this->result = $validateUserCoupon['result'];
                        $this->message = $validateUserCoupon['result']['message'];
                        $inValidCoupon = true;
                    } else {
                        $args['coupon_id'] = $coupon->id;
                        $args['coupon_type'] = $coupon->coupon_type;
                        $coupon_available = true;
                    }
                } else {
                    $inValidCoupon = true;
                    $this->message = __('messages.CouponNotFound');
                }
            }
            if ($inValidCoupon) {
                $this->error = 1;
                $this->response = $this->getResponse();

                return $this->getResponse();
            }

            $args['user_id'] = $user['id'];
            $args['name'] = $user['name'];
            $args['mobile'] = $user['mobile'];
            $args['email'] = $user['email'];
            $args['order_number'] = floor(microtime(true) * 10000);
            $args['qr_code'] = floor(microtime(true) * 10000);
            $payment_method = $args['payment_method'];
            $args['device_uuid'] = $device_uuid;
            $args['app_version'] = $app_version;

            $constant = $this->generalHelper->getConstantItemByTitle('PaymentMethod', $args['payment_method']);
            if ($constant) {
                $args['payment_method'] = $constant['id'];
            } else {
                $this->error = 1;
                $this->message = __('messages.You_Must_Enter_One_of_Allowed_Payment');
                $this->response = $this->getResponse();

                return $this->getResponse();
            }
            $args['order_uuid'] = Str::orderedUuid();

            $order = Order::create($args);
            // $storeOrderItemsRes = $this->storeOrderItems(
            //     $order->id,
            //     $args['orderItems'],
            //     $args['brand_id'],
            //     $args['branch_id']
            // );
            //saveOrderItems

            $storeOrderItemsRes = $this->saveOrderItems(
                $order->id,
                $args['orderItems'],
                $order->brand->foodics_token,
                $order->branch->foodics_branch_id,
                $coupon_available
            );

            $this->calculateOrderPrice($order, $storeOrderItemsRes['totalItemsPrice']);

            date_default_timezone_set('Asia/Riyadh');
            $today = date('Y-m-d H:i:s');
            $tomorrowAt2Am = date('Y-m-d H:i:s', strtotime('tomorrow 02:00:00'));
            $date = $args['order_date'];
            $time = date('H:i:s');
            $orderDateTime = date('Y-m-d', strtotime($date)) . ' ' . $time;

            if (isset($args['order_time']) && $args['order_time'] != '') {
                $orderDateTime = date('Y-m-d', strtotime($date)) . ' ' . $args['order_time'];
            }
            //$orderDateTime >= $today &&

            if ($storeOrderItemsRes['products'] > 0 && $orderDateTime <= $tomorrowAt2Am) {
                event(new CreateFoodicsOrder($order->id));
            }

            if ($order->total_price <= 0) {
                //payment status : complete

                $order->update([
                    'payment_status' => 1,
                ]);

                event(new ZatcaCreateInvoice($order->id));

                if ($constant['id'] != '1') {
                    //booking order notification
                    try {
                        event(new OrderNotification($order, 4));
                    } catch (\Exception $e) {
                        // return $e->getMessage();
                        $this->error = 1;
                    }
                }
            }
            if (!empty($coupon) && $coupon->coupon_type == \App\Helpers\Constant::ReferralCouponType) {
                //removedregister couponor invalid
                Coupon::where('user_id', $user->id)
                    ->Register()
                    ->update([
                        'active' => 0,
                    ]);
            }

            if ($order && $constant['id'] == '1') {
                //booking order notification
                try {
                    event(new OrderNotification($order, 4));
                } catch (\Exception $e) {
                    // return $e->getMessage();
                    $this->error = 1;
                }
            }

            $this->orderId = $order->id;
            // $this->message = $orderDateTime <= $tomorrowAt2Am;
            $this->response = $this->getResponse();
            $this->response['order'] = $order;

            return $this->response;
        }
    }

    public function saveOrderItems($orderId, $orderItems, $foodics_token, $foodics_branch_id, $coupon_available)
    {
        $addons = 0;
        $totalItemsPrice = 0;
        $itemBundles = [];
        $itemTickets = [];
        $itemAddons = [];
        $itemBundleIds = [];
        $itemTicketIds = [];
        $itemAddonIds = [];
        $itemAddonFoodics = [];
        foreach ($orderItems as $item) {
            if ($item['type'] === 'ticket') {
                array_push($itemTickets, $item);
                array_push($itemTicketIds, $item['id']);
            } elseif ($item['type'] === 'addon') {
                array_push($itemAddons, $item);
                array_push($itemAddonIds, $item['id']);
            } elseif ($item['type'] === 'bundle') {
                array_push($itemBundles, $item);
                array_push($itemBundleIds, $item['id']);
            }
        }
        if (count($itemAddonIds) > 0) {
            if ($foodics_token) {
                $itemAddons = FoodicsApi::sendRequest(
                    'products?filter[id]=' . implode(',', $itemAddonIds),
                    [],
                    'GET',
                    $foodics_token
                );
                $itemAddonFoodics = collect($itemAddons);
            }
        }
        if (count($itemBundleIds) > 0) {
            $itemBundles = Bundle::whereIn('id', $itemBundleIds)->get();
        }
        if (count($itemTicketIds) > 0) {
            $itemTickets = Ticket::whereIn('id', $itemTicketIds)->get();
        }

        foreach ($orderItems as $item) {
            $orderingItem = [];
            $i = null;
            $bookingItem = null;
            if ($item['type'] === 'bundle' && count($itemBundles) > 0) {
                $bookingItem = $itemBundles->first(function ($search) use ($item) {
                    return $search->id == $item['id'];
                });
            } elseif ($item['type'] === 'addon' && count($itemAddonFoodics) > 0) {
                $bookingItem = $itemAddonFoodics->first(function ($search) use ($item) {
                    return $search->id == $item['id'];
                });
                $bookingItem = (array) $bookingItem;
            } elseif ($item['type'] === 'ticket' && count($itemTickets) > 0) {
                $bookingItem = $itemTickets->first(function ($search) use ($item) {
                    return $search->id == $item['id'];
                });
            }

            if (!empty($bookingItem) && isset($bookingItem['price'])) {
                $orderingItem['quantity'] = $item['quantity'];
                $bookingItem['price'] = $bookingItem['price'] ?? 0;
                $orderingItem['order_id'] = $orderId;
                $orderingItem[$item['type'] . '_id'] = $item['id'];

                if ($item['type'] == 'addon') {
                    //
                    if (!empty($bookingItem['branches'])) {
                        foreach ($bookingItem['branches'] as $branch) {
                            if ($branch['id'] == $foodics_branch_id && !empty($branch['pivot'])) {
                                $bookingItem['price'] = $branch['pivot']['price'];
                            }
                        }
                    }
                    $orderingItem['price'] = $bookingItem['price'];
                    $orderingItem['total_price'] = $bookingItem['price'] * $item['quantity'];
                    $totalItemsPrice += $orderingItem['total_price'];

                    $orderingItem['ar']['title'] = $bookingItem['name_localized'];
                    $orderingItem['en']['title'] = $bookingItem['name'];
                    OrderAddon::create($orderingItem);
                    $addons++;
                } elseif ($item['type'] == 'ticket' || $item['type'] == 'bundle') {
                    $orderingItem['price'] = $bookingItem['price'];
                    if ($item['type'] == 'ticket') {
                        $orderingItem['price_before'] = $bookingItem['price_before'];
                        $orderingItem['have_discount'] = $bookingItem['have_discount'];
                        if ($coupon_available == true && $bookingItem['have_discount'] == 1) {
                            $orderingItem['price_before'] = $bookingItem['price'];
                            $orderingItem['price'] = $bookingItem['price_before'];
                        }
                    }

                    $orderingItem['total_price'] = $orderingItem['price'] * $item['quantity'];
                    $totalItemsPrice += $orderingItem['total_price'];
                    $orderingItem['ar']['title'] = $bookingItem['title_ar'];
                    $orderingItem['en']['title'] = $bookingItem['title_en'];

                    $orderingItemCreate = OrderTicket::create($orderingItem);

                    if ($item['type'] === 'ticket') {
                        if (isset($item['ticketExtraInfo']) && count($item['ticketExtraInfo']) > 0) {
                            foreach ($item['ticketExtraInfo'] as $extraInfoItem) {
                                $orderTicketExtraInfo = [];
                                $orderTicketExtraInfo['order_ticket_id'] = $orderingItemCreate->id;
                                $orderTicketExtraInfo['ticket_extra_info_id'] = $extraInfoItem['id'];
                                $orderTicketExtraInfo['value'] = $extraInfoItem['value'];
                                OrderTicketExtraInfo::create($orderTicketExtraInfo);
                            }
                        }
                    }

                    if ($item['type'] === 'bundle') {
                        OrderBundle::create($orderingItem);
                    }
                }
            } else {
                $this->error = 1;
                $this->message = __('messages.Order_Item_Not_Found', [
                    'type' => ucfirst($item['type']),
                    'bookingItem' => $item['id'],
                ]);
                break;
            }
        }

        return [
            'totalItemsPrice' => $totalItemsPrice,
            'products' => $addons,
        ];
    }

    public function calculateOrderPrice($order, $totalItemsPrice)
    {
        $couponDiscount = 0;
        if ($order->coupon_id) {
            //get coupon
            $coupon = Coupon::where('id', $order->coupon_id)->first();
            if (!empty($coupon)) {
                $couponDiscount = $coupon->discount; //value
                if ($coupon->type != 1) {
                    // 0 percent
                    $couponDiscount = round($totalItemsPrice * $coupon->discount) / 100;
                }
                //check conditions
                if ($coupon->condition != 0) {
                    // 1 max discount value
                    if ($coupon->condition == 1) {
                        $couponDiscount =
                            $couponDiscount > $coupon->max_value_discount
                                ? $coupon->max_value_discount
                                : $couponDiscount;
                    }
                    if ($coupon->condition == 2) {
                        //min total order amount total_order_amount
                        $couponDiscount = $totalItemsPrice >= $coupon->total_order_amount ? $couponDiscount : 0;
                    }
                    if ($coupon->condition == 3) {
                        // both (min total order amount and  max discount value)
                        $couponDiscount =
                            $totalItemsPrice >= $coupon->total_order_amount
                                ? ($couponDiscount >= $coupon->max_value_discount
                                    ? $coupon->max_value_discount
                                    : $couponDiscount)
                                : 0;
                    }
                }
            }
        }
        $order['price'] = $totalItemsPrice;
        $order['coupon_discount'] = $couponDiscount;
        $order['total_price'] = $couponDiscount > $totalItemsPrice ? 0 : $totalItemsPrice - $couponDiscount;
        $order['status'] = $this->generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
        $order['payment_status'] = $this->generalHelper->getConstantItemByTitle('PaymentStatus', 'Waiting')['id'];
        if ($order['total_price'] == 0) {
            $order['payment_method'] = $this->generalHelper->getConstantItemByTitle('PaymentMethod', 'Coupon')['id'];
            $order['payment_status'] = $this->generalHelper->getConstantItemByTitle('PaymentStatus', 'Paid')['id'];
        }
        $order->save();
    }

    public function updateExtraInfo($orderingItem, $item, $other)
    {
        $info = [];
        if ($item['type'] === 'bundle') {
            $modelName = $this->MODEL_PROMOTION_PREFIEX . '\\' . ucfirst($item['type']) . '\\' . ucfirst($item['type']);
        } else {
            $modelName = $this->MODEL_BOOKING_PREFIEX . '\\' . ucfirst($item['type']) . '\\' . ucfirst($item['type']);
        }

        $bookingItem = $modelName::where('id', $orderingItem[$item['type'] . '_id'])->first();

        if ($item['type'] === 'ticket') {
            $info['ar']['sub_title'] = $bookingItem->getSubTitleArAttribute();
            $info['en']['sub_title'] = $bookingItem->getSubTitleEnAttribute();
            $info['ar']['title'] = $bookingItem->getTitleArAttribute();
            $info['en']['title'] = $bookingItem->getTitleEnAttribute();
            if (isset($item['ticketExtraInfo']) && count($item['ticketExtraInfo']) > 0) {
                foreach ($item['ticketExtraInfo'] as $extraInfoItem) {
                    $orderTicketExtraInfo = new OrderTicketExtraInfo();
                    $orderTicketExtraInfo['order_ticket_id'] = $orderingItem['id'];
                    $orderTicketExtraInfo['ticket_extra_info_id'] = $extraInfoItem['id'];
                    $orderTicketExtraInfo['value'] = $extraInfoItem['value'];
                    $orderTicketExtraInfo->save();
                }
            }
        }

        if ($item['type'] === 'bundle') {
            $info['ar']['title'] = $bookingItem->getTitleArAttribute();
            $info['en']['title'] = $bookingItem->getTitleEnAttribute();
        }

        if ($item['type'] === 'addon') {
            $info['ar']['title'] = $other['title_ar'];
            $info['en']['title'] = $other['title_en'];
        }

        $orderingItem->update($info);
    }

    public function getResponse()
    {
        if (!$this->error) {
            $this->message = __('messages.Order_Success_Created');
        }
        $res = [
            'orderId' => $this->orderId,
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }

    public function isInOffDates($branch, $orderDate, $time = null)
    {
        $offDates = WorkHour::OffDates()
            ->where('branch_id', $branch)
            ->whereNull('to_time')
            ->whereNull('from_time');
        // if ($time != null) {
        //     $offDates = $offDates
        //         ->where(function ($q) use ($time) {
        //             $q->where('to_time', '>', $time)
        //                 ->where('from_time', '<=', $time)
        //                 ->whereNotNull('from_time')
        //                 ->whereNotNull('to_time');
        //         })
        //         ->orWhere(function ($q) {
        //             $q->whereNull('to_time')->whereNull('from_time');
        //         });
        // }
        $offDates = $offDates->pluck('date')->toArray();

        return in_array(date('Y-m-d', strtotime($orderDate)), $offDates);
    }
}