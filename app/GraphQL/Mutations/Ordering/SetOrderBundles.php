<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Models\Ordering\Bundle\OrderBundle;
use App\Models\Ordering\Order;
use App\Models\Promotion\Bundle\Bundle;
use App\Scopes\NonDraftScope;
use Auth;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOrderBundles
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';

        //first check if order already exists
        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            //then check if orderItems (ticktes) are not empty
            if (!empty($args['orderItems']) && count($args['orderItems']) > 0) {
                //extract the bundles ids from the orderItems input array as key and val is [quantity]
                foreach ($args['orderItems'] as $orderItem) {
                    $bundlesArr[$orderItem['id']] = [
                        'quantity' => $orderItem['quantity'],
                    ];
                }
                $orderItemIds = array_keys($bundlesArr);
                //get the bundles info by ids and by brand_id to make sure it belongs to this brand
                //also check on bundle date availability
                $bundles = Bundle::whereIn('id', $orderItemIds)
                    ->where('brand_id', $order->brand_id)
                    ->where('started_at', '<=', $order->order_date)
                    ->where('expired_at', '>=', $order->order_date)
                    ->get();
                //check if all bundles belong to same brand
                if (count($bundles) == count($orderItemIds)) {
                    //get the ids of existing order bundles before deleting them to deduct their price
                    $oldBundlesPriceToBeDeducted = 0;
                    foreach ($order->bundles as $oldBundle) {
                        $oldBundlesPriceToBeDeducted =
                            $oldBundlesPriceToBeDeducted + $oldBundle->quantity * $oldBundle->price;
                    }
                    $order->bundles()->delete();
                    $order->price = $order->price - $oldBundlesPriceToBeDeducted;
                    $order->save();
                    //set the initial orderPrice to order price after deducting the old bundles price
                    $orderPrice = $order->price;
                    foreach ($bundles as $bundle) {
                        //preparing to add orderBundle
                        $orderBundleData['bundle_id'] = $bundle->id;
                        $orderBundleData['order_id'] = $order->id;
                        $orderBundleData['quantity'] = $bundlesArr[$bundle->id]['quantity'];
                        $orderBundleData['price'] = $bundle->price;
                        $orderBundleData['total_price'] = $orderBundleData['quantity'] * $bundle->price;
                        $orderBundleData['ar']['title'] = $bundle->title_ar;
                        $orderBundleData['en']['title'] = $bundle->title_en;
                        OrderBundle::create($orderBundleData);

                        //add the bundles price to the order price
                        $orderPrice = $orderPrice + $orderBundleData['total_price'];
                    }
                    $order->price = $orderPrice;
                    $order->save();
                    $arr['order'] = $order;
                } else {
                    $error = 1;
                    $message = __('messages.some_bundles_doesnt_belong_to_order_brand');
                }
            } else {
                $error = 1;
                $message = __('messages.bundles_required');
            }
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}