<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Models\Ordering\Order;
use App\Models\Ordering\OrderStatusLog;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UpdatePaymentStatus
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $error = 0;
        $message = '';
        $user = auth()->user();

        $order = Order::where('id', $args['order_id'])
            ->draft()
            ->where('user_id', $user->id)
            ->first();
        if (! $order) {
            $message = __('messages.Order_Not_Found');
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }
        //log tries
        OrderStatusLog::create(['order_id' => $args['order_id'], 'status' => 6, 'user_id' => $user->id]);

        if ($order->payment_status == 0) {
            //update status on payfort only

            $order
                ->where('user_id', $user->id)
                ->where('payment_status', 0)
                ->where('payment_method', 3)
                ->update([
                    'payment_status' => 6,
                ]);

            $message = __('messages.Order_Has_Been_Updated');
        }

        return [
            'result' => [
                'error' => $error,
                'message' => $message,
            ],
        ];
    }
}
