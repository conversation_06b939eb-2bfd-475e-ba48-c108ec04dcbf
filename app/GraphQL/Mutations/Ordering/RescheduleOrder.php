<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Events\CancelFoodicsOrder;
use App\Events\CreateFoodicsOrder;
use App\Events\OrderNotification;
use App\Helpers\General;
use App\Models\Booking\Addon\BirthdaySetting;
use App\Models\Ordering\Order;
use DateTime;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Log;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class RescheduleOrder
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General;

        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $error = 0;
        $message = '';
        $user = auth()->user();
        $order = Order::where('id', $args['order_id'])
            ->where('user_id', $user->id)
            ->first();
        if (! $order) {
            $message = __('messages.Order_Not_Found');
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }
        if ($order->is_birthday == 1) {
            if ($this->validateTimeSlot($order, $args) == 'error') {
                $message = __('messages.time_slot_is_not_available');
                $error = 1;

                return [
                    'result' => [
                        'error' => $error,
                        'message' => $message,
                    ],
                ];
            }
        }

        $date1 = new DateTime(date('Y-m-d'));
        $date2 = new DateTime(date('Y-m-d', strtotime($order['order_date'])));
        $daysDifferent = $date1->diff($date2);

        if ($daysDifferent->days < $order->branch->no_day_to_reschedule) {
            $message = __('messages.Order_Reschedule_Possibility', [
                'no_day' => $order->branch->no_day_to_reschedule,
            ]);
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }
        // cancelOrder
        $order->update([
            'order_date' => date('Y-m-d', strtotime($args['order_date'])),
            'order_time' => $args['order_time'] ? $args['order_time'] : null,
        ]);

        if ($order->sending_status == 'sent' && $order->is_parent_order_transfer) {
            $children = Order::where('parent_order_transfer', $order->id);
            $children->update([
                'order_date' => date('Y-m-d', strtotime($args['order_date'])),
                'order_time' => $args['order_time'] ? $args['order_time'] : null,
            ]);
            foreach ($children->get() as $child) {
                event(new OrderNotification($child, 1));
            }
        }
        // check if have foodics order
        if ($order->foodics_id) {
            event(new CancelFoodicsOrder($order));

            event(new CreateFoodicsOrder($order->id));
        }

        $message = __('messages.Order_Has_Been_Rescheduled');
        try {
            event(new OrderNotification($order, 1));
        } catch (\Exception $exception) {
            Log::error('Order Notification Error', ['exception' => $exception]);
        }

        return [
            'order' => $order,
            'result' => [
                'error' => $error,
                'message' => $message,
            ],
        ];
    }

    public function validateTimeSlot($order, $args)
    {
        $timeSlot = $args['order_time'];
        $orderDate = $args['order_date'];
        $branchId = $order->branch_id;

        $day = date('l', strtotime($args['order_date']));
        $constant = $this->generalHelper->getConstantItemByTitle('Days', $day);

        if ($constant) {
            $day_id = $constant['id'];
        }
        if (! empty($day_id)) {
            if ($slotId = BirthdaySetting::where('day_id', $day_id)
                ->where('branch_id', $branchId)
                ->where('from_time', '<', $timeSlot)
                ->where('to_time', '>', $timeSlot)
                ->first()) {

                if (Order::where('order_date', $orderDate)
                    ->where('branch_id', $branchId)
                    ->where('order_time', '>=', date('H:i:s', strtotime($timeSlot.' - '.($slotId->break_duration + ($slotId->break_duration - 1)).' hours')))
                    ->where('order_time', '<=', date('H:i:s', strtotime($timeSlot.' + '.($slotId->break_duration + ($slotId->break_duration - 1)).' hours')))
                    ->where('is_birthday', '1')

                    ->where('status', '!=', '2')
                    ->where('payment_status', '1')

                    ->where('id', '!=', $order->id)

                    ->count() > 0) {
                    return 'error';
                }
            } else {
                return 'error';
            }

        } else {
            return 'error';
        }
    }

    public function calculateDays($date)
    {
        $targetDate = $date;
        if (strpos($targetDate, '@')) {
            $targetDate = explode('@', $targetDate)[0];
        }
        $targetDate = date('Y-m-d', strtotime($targetDate));

        $now = time();
        $targetDate = strtotime($targetDate);
        $datDiff = $targetDate - $now;

        return round($datDiff / (60 * 60 * 24));
    }
}
