<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Events\OrderNotification;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Models\User;
use Auth;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Str;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SendOrderTickets
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';
        $device_uuid = \Request::header('device_uuid');
        $app_version = \Request::header('version');

        // first check if order already exists
        if (
            $order = Order::where('id', $args['order_id'])
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            // then check if orderItems (ticktes) are not empty
            if (!empty($args['orderTransferItems']) && count($args['orderTransferItems']) > 0) {
                // extract the tickets ids from the orderTransferItems input array as key and val is [quantity,extraInfo]
                foreach ($args['orderTransferItems'] as $orderItem) {
                    $ticketsArr[$orderItem['id']] = [
                        'quantity' => $orderItem['quantity'],
                    ];
                }
                $orderItemIds = array_keys($ticketsArr);
                // Step 1: Retrieve OrderTicket data for the current order
                $orderTickets = OrderTicket::where('order_id', $order->id)
                    ->whereIn('ticket_id', $orderItemIds)
                    ->get();

                // Step 2: Compare quantities
                foreach ($args['orderTransferItems'] as $orderItem) {
                    $ticketId = $orderItem['id'];
                    $transferQuantity = $orderItem['quantity'];

                    // Find the corresponding OrderTicket entry for this ticket ID
                    $orderTicket = $orderTickets->where('ticket_id', $ticketId)->first();

                    if ($orderTicket) {
                        // Compare quantities
                        $orderTicketQuantity = $orderTicket->quantity;

                        if ($transferQuantity > $orderTicketQuantity) {
                            $error = 1;
                            $message = __('messages.transfer_quantity_is_greater_than_order_ticket_quantity');
                            break;
                        }
                        $orderTicket->quantity = $orderTicket->quantity - $transferQuantity;
                        $orderTicket->save();
                    } else {
                        $error = 1;
                        $message = __('messages.tickets_not_exist');
                        break;
                    }
                }
                if (!$error) {
                    // get the tickets info by ids and by brand_id to make sure it belongs to this brand
                    $tickets = Ticket::whereIn('id', $orderItemIds)
                        ->where('brand_id', $order->brand_id)
                        ->get();

                    if (count($tickets) == count($orderItemIds)) {
                        $receiver = User::where('id', $args['receiver_id'])->first();
                        if (!empty($receiver)) {
                            $order->sending_status = 'sent';
                            $order->is_parent_order_transfer = 1;
                            $order->save();
                            // create a new sub order
                            $args_sub['user_id'] = $receiver->id;
                            $args_sub['name'] = $receiver->name;
                            $args_sub['mobile'] = $receiver->mobile;
                            $args_sub['email'] = $receiver->email;
                            $args_sub['order_number'] = floor(microtime(true) * 10000);
                            $args_sub['qr_code'] = floor(microtime(true) * 10000);
                            $args_sub['payment_method'] = 5;
                            $args_sub['device_uuid'] = $device_uuid;
                            $args_sub['app_version'] = $app_version;
                            $args_sub['order_uuid'] = Str::orderedUuid();
                            $args_sub['branch_id'] = $order->branch_id;
                            $args_sub['brand_id'] = $order->brand_id;
                            $args_sub['order_date'] = $order->order_date;
                            $args_sub['order_time'] = $order->order_time;
                            $args_sub['parent_order_transfer'] = $order->id;
                            $args_sub['status'] = 0;
                            $args_sub['sending_status'] = 'receive';

                            $sub_order = Order::create($args_sub);

                            // set the initial orderPrice to new order->price after deducting the old tickets price
                            $orderPrice = 0;
                            foreach ($tickets as $ticket) {
                                // preparing to add orderTicket
                                $orderTicketData['ticket_id'] = $ticket->id;
                                $orderTicketData['order_id'] = $sub_order->id;
                                $orderTicketData['quantity'] = $ticketsArr[$ticket->id]['quantity'];
                                $orderTicketData['price'] = $ticket->price;
                                $orderTicketData['total_price'] = $ticketsArr[$ticket->id]['quantity'] * $ticket->price;
                                $orderTicketData['sender'] = $order->user_id;
                                $orderTicketData['receiver'] = $receiver->id;
                                $orderTicketData['ar']['title'] = $ticket->title_ar;
                                $orderTicketData['en']['title'] = $ticket->title_en;
                                $orderTicketData['ar']['sub_title'] = $ticket->sub_title_ar;
                                $orderTicketData['en']['sub_title'] = $ticket->sub_title_en;
                                $orderTicket = OrderTicket::create($orderTicketData);

                                // add the tickets price to the order price
                                $orderPrice = $orderPrice + $orderTicketData['total_price'];
                            }
                            $sub_order->price = $orderPrice;
                            $sub_order->total_price = $orderPrice;
                            $sub_order->save();

                            try {
                                event(new OrderNotification($sub_order, 10, '', $receiver->id));
                            } catch (\Exception $e) {
                            }

                            $arr['order'] = $sub_order;
                        } else {
                            $error = 1;
                            $message = __('messages.sender_not_found');
                        }
                    } else {
                        $error = 1;
                        $message = __('messages.some_tickets_doesnt_belong_to_order_brand');
                    }
                }
            } else {
                $error = 1;
                $message = __('messages.tickets_required');
            }
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}
