<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Models\Booking\Ticket\Ticket;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Models\Ordering\Ticket\OrderTicketExtraInfo;
use App\Models\Ordering\Ticket\OrderTicketTranslation;
use App\Scopes\NonDraftScope;
use App\Services\Payment\WalletService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOrderTickets
{
    protected WalletService $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';

        // first check if order already exists
        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->where('user_id', \Auth::user()->id)
                ->first()
        ) {
            // then check if orderItems (ticktes) are not empty
            if (! empty($args['orderItems']) && count($args['orderItems']) > 0) {
                // extract the tickets ids from the orderItems input array as key and val is [quantity,extraInfo]
                foreach ($args['orderItems'] as $orderItem) {
                    // set extra info to empty to avoid Undefined array key ticketExtraInfo
                    $extraInfo = '';
                    // check if $orderItem has key 'ticketExtraInfo' then assign its value to $extraInfo
                    if (! empty($orderItem['ticketExtraInfo'])) {
                        $extraInfo = $orderItem['ticketExtraInfo'];
                    }

                    $ticketsArr[$orderItem['id']] = [
                        'quantity' => $orderItem['quantity'],
                        'ticketExtraInfo' => $extraInfo,
                    ];
                }
                $orderItemIds = array_keys($ticketsArr);

                // get the tickets info by ids and by brand_id to make sure it belongs to this brand
                $tickets = Ticket::whereIn('id', $orderItemIds)
                    ->where('brand_id', $order->brand_id)
                    ->whereHas('branches', function ($query) use ($order) {
                        $query->where('branch_id', $order->branch_id);
                    })
                    ->whereHas('calendars', function ($query) use ($order) {
                        $query->where('date', $order->order_date)->orWhere(function ($query) use ($order) {
                            $query->where('start', '<=', $order->order_date)->where('end', '>=', $order->order_date);
                        });
                    })
                    ->where('active', '1')
                    ->get();

                if (count($tickets) == count($orderItemIds)) {
                    // get the ids of tickets before deleting them to delete their extra info and translations
                    // and deduct their price too
                    $orderTicketsIds = $order->tickets->pluck('id');
                    $oldTicketsPriceToBeDeducted = 0;
                    foreach ($order->tickets as $oldTicket) {
                        $oldTicketsPriceToBeDeducted =
                            $oldTicketsPriceToBeDeducted + $oldTicket->quantity * $oldTicket->price;
                    }

                    OrderTicketTranslation::whereIn('order_ticket_id', $orderTicketsIds)->delete();
                    $order->tickets()->delete();
                    OrderTicketExtraInfo::whereIn('order_ticket_id', $orderTicketsIds)->delete();
                    $order->price = $order->price - $oldTicketsPriceToBeDeducted;
                    $order->save();

                    // handle the refund of the partially paid deposites
                    $this->walletService->handlePartiallyPaidOrdersRefunds();

                    // set the initial orderPrice to new order->price after deducting the old tickets price
                    $orderPrice = $order->price;
                    foreach ($tickets as $ticket) {
                        // preparing to add orderTicket
                        $orderTicketData['ticket_id'] = $ticket->id;
                        $orderTicketData['order_id'] = $order->id;
                        $orderTicketData['quantity'] = $ticketsArr[$ticket->id]['quantity'];
                        $orderTicketData['price'] = $ticket->price;
                        $orderTicketData['total_price'] = $orderTicketData['quantity'] * $ticket->price;
                        $orderTicketData['have_discount'] = $ticket->have_discount;
                        $orderTicketData['price_before'] = $ticket->price_before;
                        $orderTicketData['ar']['title'] = $ticket->title_ar;
                        $orderTicketData['en']['title'] = $ticket->title_en;
                        $orderTicket = OrderTicket::create($orderTicketData);
                        // preparing to add orderTicketExtraInfo
                        if (
                            ! empty($ticketsArr[$ticket->id]['ticketExtraInfo'])
                            && count($ticketsArr[$ticket->id]['ticketExtraInfo']) > 0
                        ) {
                            foreach ($ticketsArr[$ticket->id]['ticketExtraInfo'] as $ticketExtraInfo) {
                                $orderTicketExtraInfo['order_ticket_id'] = $orderTicket->id;
                                $orderTicketExtraInfo['ticket_extra_info_id'] = $ticketExtraInfo['id'];
                                $orderTicketExtraInfo['value'] = $ticketExtraInfo['value'] ?? '';
                                OrderTicketExtraInfo::create($orderTicketExtraInfo);
                            }
                        }
                        // add the tickets price to the order price
                        $orderPrice = $orderPrice + $orderTicketData['total_price'];
                        if ($ticket->ticket_type == '3') {
                            $order->is_birthday = 1;
                            $order->save();
                        }
                    }
                    $order->price = $orderPrice;
                    $order->save();
                    $arr['order'] = $order;
                } else {
                    $error = 1;
                    $message = __('messages.some_tickets_doesnt_belong_to_order_brand');
                }
            } else {
                $error = 1;
                $message = __('messages.tickets_required');
            }
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}
