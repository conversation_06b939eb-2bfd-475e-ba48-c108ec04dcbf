<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Events\CancelFoodicsOrder;
use App\Events\OrderNotification;
use App\Helpers\Constant;
use App\Helpers\General;
use App\Models\Ordering\Order;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class CancelOrder
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $error = 0;
        $message = '';

        if (! empty($args['order_id']) && $order = Order::where('id', $args['order_id'])
            ->where('user_id', Auth::user()->id)
            ->first()) {
            $daysDifferent = $this->calculateDays($order['order_date']);

            $forceCancel = $args['forceCancel'] ?? false;
            if ($daysDifferent < 2 && $forceCancel == false) {
                $message = __('messages.Order_Cancel_Possibility');
                $error = 1;

                $arr['order'] = $order;
                $arr['result']['error'] = $error;
                $arr['result']['message'] = $message;

                return $arr;
            }
            //case offer sub order
            if ($order->offer()->exists() && $order->offer_sub_orders()->count() < 1) {
                if ($this->hasPartiallyClaimedOrders($order->offer_parent_order)) {
                    $message = __('messages.this_offer_order_has_partially_claimed_sub_orders');
                } elseif ($this->hasLessThan2DaysOrder($order->offer_parent_order, $forceCancel)) {
                    $message = __('messages.Order_Cancel_Possibility');
                } elseif ($order->brand_id != $order->offer_parent_order->brand_id) {
                    $message = __('messages.can_only_cancel_order_with_same_brand_as_main_order_brand');
                } else {
                    //cancel main order
                    $this->cancelOrder($order->offer_parent_order);
                    //cancel all its sub orders
                    foreach ($order->offer_parent_order->offer_sub_orders as $offer_sub_order) {
                        $this->cancelOrder($offer_sub_order);
                    }
                }
                $arr['order'] = $order;
                //case offer main order
            } elseif ($order->offer()->exists() && $order->offer_sub_orders()->count() > 0) {
                if ($this->hasPartiallyClaimedOrders($order)) {
                    $message = __('messages.this_offer_order_has_partially_claimed_sub_orders');
                } elseif ($this->hasLessThan2DaysOrder($order, $forceCancel)) {
                    $message = __('messages.Order_Cancel_Possibility');
                } else {
                    //cancel main order
                    $this->cancelOrder($order);
                    //cancel all its sub orders
                    foreach ($order->offer_sub_orders as $offer_sub_order) {
                        $this->cancelOrder($offer_sub_order);
                    }
                }
                $arr['order'] = $order;
            } elseif ($order->offer()->doesntExist()) {
                $arr['order'] = $this->cancelOrder($order);
            }

            $message = __('messages.Order_Has_Been_Cancelled');
        } else {
            $error = 1;
            $message = __('messages.Order_Not_Found');
        }
        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    public function calculateDays($date)
    {
        $targetDate = $date;
        if (strpos($targetDate, '@')) {
            $targetDate = explode('@', $targetDate)[0];
        }

        $targetDate = date('Y-m-d', strtotime($targetDate));

        $now = time();
        $targetDate = strtotime($targetDate);
        $datDiff = $targetDate - $now;

        return round($datDiff / (60 * 60 * 24));
    }

    private function cancelOrder($order)
    {
        $generalHelper = new General();
        $order['status'] = $generalHelper->getConstantItemByTitle('OrderStatus', 'cancelled')['id'];
        if ($order->payment_status == $generalHelper->getConstantItemByTitle('PaymentStatus', 'Paid')['id'] &&
            $order->payment_method == $generalHelper->getConstantItemByTitle('PaymentMethod', 'PayFort')['id']
        ) {
            $order['payment_status'] = $generalHelper->getConstantItemByTitle('PaymentStatus', 'Request Refund')['id'];
        }
        $order->save();

        $message = __('messages.Order_Has_Been_Cancelled');
        if ($order->sending_status == 'sent' && $order->is_parent_order_transfer) {
            $children = Order::where('parent_order_transfer', $order->id);
            $children->update(['status' => $order['status']]);
            foreach ($children->get() as $child) {
                event(new OrderNotification($child, 2));
            }
        }

        if ($order->foodics_id != null) {
            event(new CancelFoodicsOrder($order)); //cancel order
        }
        event(new OrderNotification($order, 2));
        if ($order->coupon_id != null) {
            $order->where('user_id', auth()->user()->id)->update([
                'old_coupon_id' => $order->coupon_id,
                'coupon_id' => null,
            ]);
        }

        return $order->fresh();
    }

    private function hasPartiallyClaimedOrders($order)
    {
        foreach ($order->offer_sub_orders as $offer_sub_order) {
            if ($offer_sub_order->status == Constant::ClaimOrder || $offer_sub_order->status == Constant::NoShowOrder) {
                return true;
            }
        }
    }

    private function hasLessThan2DaysOrder($order, $forceCancel = false)
    {
        foreach ($order->offer_sub_orders as $offer_sub_order) {
            if ($offer_sub_order->status == Constant::ClaimOrder) {
                $daysDifferent = $this->calculateDays($order->order_date);
                if ($daysDifferent < 2 && $forceCancel == false) {
                    return true;
                }
            }
        }
    }
}
