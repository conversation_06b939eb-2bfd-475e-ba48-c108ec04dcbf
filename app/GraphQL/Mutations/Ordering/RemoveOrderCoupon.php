<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Scopes\NonDraftScope;
use Auth;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class RemoveOrderCoupon
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';

        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            //this function resets order tickets prices to original price
            //recalculate the order price
            //recalculate the order total_price
            //and lastly remove all coupon related data
            $order = $this->resetOrderPriceToDiscounted($order);
            $arr['order'] = $order;
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    private function resetOrderPriceToDiscounted($order)
    {
        if (! empty($order->tickets)) {
            foreach ($order->tickets as $ticket) {
                $ticket->total_price = $ticket->price * $ticket->quantity;
                $ticket->save();
            }
        }

        $orderPrice =
            $order->tickets->sum('total_price') +
            $order->bundles->sum('total_price') +
            $order->addons->sum('total_price');

        $totalOrderPayments = Payment::where('order_id', $order->id)->sum('amount');

        $order->price = $orderPrice;
        $order->total_price = $orderPrice;
        $order->to_be_paid = $orderPrice - $totalOrderPayments;
        $order->discount = 0;
        $order->coupon_discount = 0;
        $order->coupon_id = null;
        $order->coupon_type = 0;
        $order->auto_apply_coupon = '0';
        $order->referral_user_id = null;
        $order->referral_code = '';
        $order->save();

        return $order;
    }
}
