<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Events\OrderNotification;
use App\Events\ZatcaCreateInvoice;
use App\Helpers\General;
use App\Models\Brand\WorkHour;
use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Scopes\NonDraftScope;
use App\Services\Order\CouponService;
use App\Services\Payment\WalletService;
use Carbon\Carbon;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class confirmOrder
{
    protected CouponService $couponService;

    protected WalletService $walletService;

    public $generalHelper;

    public function __construct(CouponService $couponService, WalletService $walletService)
    {
        $this->couponService = $couponService;
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General;
        $arr = [];
        $error = 0;
        $message = '';

        //get instance of the order model and
        if ($orderInstance = Order::where('id', $args['order_id'])
            ->withoutGlobalScope(NonDraftScope::class)
            ->where('user_id', Auth::user()->id)
            ->first()) {
            //check if order_metadata is sent then save it
            if (isset($args['order_metadata'])) {
                $orderInstance->metadata = $args['order_metadata'];
            } else {
                //then set to empty
                $orderInstance->metadata = null;
            }
            //save the model instance
            $orderInstance->save();
        }

        //then check if order already exists and is draft and belongs to this user
        if ($order = Order::where('id', $args['order_id'])
            ->withoutGlobalScope(NonDraftScope::class)
            ->draft()
            ->where('user_id', Auth::user()->id)
            ->first()
        ) {
            //validate orderDate
            $arr['result'] = $this->validateDate($order);
            if (! empty($arr['result']['error'])) {
                return $arr;
            }
            //validate tickets
            $arr['result'] = $this->validateTickets($order);
            if (! empty($arr['result']['error'])) {
                return $arr;
            }
            //validate bundles
            $arr['result'] = $this->validateBundles($order);
            if (! empty($arr['result']['error'])) {
                return $arr;
            }

            //validate if order has tickets with "no_coupon" condition
            //then return 0 or 1 as $noCoupon to be set on the fly for
            //the order->no_coupon key
            //also removes the coupon and resets the prices
            $noCoupon = $this->validateNoCouponTickets($order);

            //validate price and coupon
            $arr['result'] = $this->validatePrice($order);
            if (! empty($arr['result']['error'])) {
                return $arr;
            }

            if ($order->to_be_paid == 0 && ! empty($args['complete'])) {
                $this->fullyPayOrder($order);
                $arr['result']['error'] = 1;
                $arr['result']['message'] = __('messages.no_order_found');

                return $arr;
            }

            $order->wallet = $this->walletService->getTheWallet();
            $order->partial_payments = $this->walletService->getTheOrderPayments($order);

            $order->no_coupon = $noCoupon;

            $arr['order'] = $order;
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    //Vaildate Date Functions
    public function validateDate($order)
    {
        //check if there is no order_time then set to 23:59:59 which means the end if the day
        if (empty($order->order_time) || $order->order_time == '00:00:00') {
            $orderMaxTime = $order->order_date.' '.'23:59:59';
        } else {
            $orderMaxTime = $order->order_date.' '.$order->order_time;
        }

        //combining the orderDateTime
        $orderDateTimeString = $order->order_date.' '.$order->order_time;
        $orderDateTime = Carbon::parse($orderDateTimeString);

        $arr = [];
        if ($this->isInOffDates($order->branch_id, $order->order_date, $order->order_time)) {
            $arr['error'] = 1;
            $arr['message'] = __('messages.Order_Date_In_Off_Dates', [
                'order_date' => date('Y-m-d', strtotime($order->order_date)),
            ]);
        } elseif ($orderMaxTime < Carbon::now()) {
            $arr['error'] = 1;
            $arr['message'] = __('messages.wrong_order_date');
        }

        return $arr;
    }

    public function isInOffDates($branch, $orderDate, $time = null)
    {
        $offDates = WorkHour::OffDates()
            ->where('branch_id', $branch)
            ->whereNull('to_time')
            ->whereNull('from_time');

        $offDates = $offDates->pluck('date')->toArray();

        return in_array(date('Y-m-d', strtotime($orderDate)), $offDates);
    }

    //Vaildate Tickets Function
    public function validateTickets($order)
    {
        if (! empty($order->tickets) && count($order->tickets) > 0) {
            $arr = [];
            foreach ($order->tickets as $ticket) {
                if ($ticket->ticket->brand_id != $order->brand_id) {
                    $arr['error'] = 1;
                    $arr['message'] = __('messages.some_tickets_doesnt_belong_to_order_brand');
                }
            }

            return $arr;
        }
    }

    //Vaildate Bundles Function
    public function validateBundles($order)
    {
        if (! empty($order->bundles) && count($order->bundles) > 0) {
            $arr = [];
            foreach ($order->bundles as $bundle) {
                if ($bundle->bundle->brand_id != $order->brand_id) {
                    $arr['error'] = 1;
                    $arr['message'] = __('messages.some_bundles_doesnt_belong_to_order_brand');
                }
            }

            return $arr;
        }
    }

    //Validate Price Function
    public function validatePrice($order)
    {
        $arr = [];
        //calculate whole price (tickets+bundles+addons)
        if (! empty($order->coupon)) {
            if (! empty($order->tickets)) {
                foreach ($order->tickets as $ticket) {
                    if ($ticket->have_discount == '1' && $ticket->price_before > 0) {
                        $ticket->total_price = $ticket->price_before * $ticket->quantity;
                        $ticket->save();
                    }
                }
            }
        }
        $orderPrice =
            $order->tickets->sum('total_price') +
            $order->bundles->sum('total_price') +
            $order->addons->sum('total_price');
        if ($order->price !== $orderPrice) {
            $order->price = $orderPrice;
            $order->save();
        }

        if (! empty($order->coupon)) {
            //calculate total_price (after applying coupon)
            $orderDiscount = $this->couponService->validateCoupon($order, $order->coupon);
            if ($orderDiscount['error'] == 0) {
                $order->total_price =
                    $order->price - $orderDiscount['discount'] > 0 ? $order->price - $orderDiscount['discount'] : 0;
                $order->coupon_discount = $orderDiscount['discount'];
                $order->save();
            } else {
                $arr['error'] = 1;
                $arr['message'] = $orderDiscount['message'];

                return $arr;
            }
        } else {
            $order->total_price = $order->price;
            $order->save();
        }

        $totalPayments = Payment::NotRefunded()->where('order_id', $order->id)->sum('amount');
        $order->to_be_paid = $order->total_price - $totalPayments;
        $order->save();

        return $arr;
    }

    public function validateNoCouponTickets($order)
    {
        $noCoupon = '0';
        foreach ($order->tickets as $ticket) {
            if ($ticket->ticket->no_coupon == 1) {
                $noCoupon = 1;
            }
        }

        if ($noCoupon == 1) {
            $this->resetOrderPriceToDiscounted($order);
        }

        return $noCoupon;
    }

    //reset prices to discounted

    private function resetOrderPriceToDiscounted($order)
    {
        if (! empty($order->tickets)) {
            foreach ($order->tickets as $ticket) {
                $ticket->total_price = $ticket->price * $ticket->quantity;
                $ticket->save();
            }
        }

        $orderPrice =
            $order->tickets->sum('total_price') +
            $order->bundles->sum('total_price') +
            $order->addons->sum('total_price');
        $order->price = $orderPrice;
        $order->total_price = $orderPrice;
        $order->discount = 0;
        $order->coupon_discount = 0;
        $order->coupon_id = null;
        $order->coupon_type = 0;
        $order->auto_apply_coupon = '1';
        $order->save();

        return $order;
    }

    public function fullyPayOrder($order): mixed
    {
        $paymentStatus = $this->generalHelper->getConstantItemByTitle('PaymentStatus', 'Paid')['id'];
        //update the order

        $order->payment_status = $paymentStatus;
        $order->to_be_paid = 0;
        $order->is_draft = '0';
        $order->status = 0;
        $order->save();

        //fire zatca create invoice event
        event(new ZatcaCreateInvoice($order->id));
        //fire booking order notification event
        try {
            event(new OrderNotification($order, 4));
        } catch (\Exception $e) {
            $error = 1;
        }

        return $order;
    }
}
