<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Helpers\FoodicsApi;
use App\Models\Booking\Addon\Addon;
use App\Models\Ordering\Addon\OrderAddon;
use App\Models\Ordering\Addon\OrderAddonTranslation;
use App\Models\Ordering\Order;
use App\Scopes\NonDraftScope;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOrderAddons
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';

        // first check if order already exists
        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            if (! empty($args['type'] && $args['type'] == 'birthday')) {
                $arr = $this->handleBirthdayAddons($args, $order);
            } else {
                $arr = $this->handleFoodicsAddons($args, $order);
            }
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;

    }

    public function validateAddonsBrandAndBranch($order)
    {
        if ($order->brand->accept_food == 0 || $order->brand->foodics_token === '') {
            $arr['error'] = 1;
            $arr['message'] = __('messages.brand_doesnt_accept_addons');

            return $arr;
        } elseif (
            ($order->brand->accept_food == 1 || $order->brand->foodics_token > '') &&
            ! isset($order->branch->foodics_branch_id)
        ) {
            $arr['error'] = 1;
            $arr['message'] = __('messages.branch_doesnt_accept_addons');

            return $arr;
        }
    }

    public function handleFoodicsAddons($args, $order)
    {
        $validationResult = $this->validateAddonsBrandAndBranch($order);
        if (! empty($validationResult['error']) && $validationResult['error'] == 1) {
            $arr['result'] = $validationResult;

            return $arr;
        } else {
            // then check if orderItems (addons) are empty
            if (empty($args['orderItems']) || count($args['orderItems']) < 1) {
                $arr['result']['error'] = 1;
                $arr['result']['message'] = __('messages.addons_required');

                return $arr;
            } else {
                // extract the addons ids from the orderItems input array as key and val is [quantity]
                foreach ($args['orderItems'] as $orderItem) {
                    $addonsArr[$orderItem['id']] = [
                        'quantity' => $orderItem['quantity'],
                    ];
                }
                $orderItemIds = array_keys($addonsArr);
                // get the addons info by ids and by brand_id to make sure it belongs to this brand
                $itemAddons = FoodicsApi::sendRequest(
                    'products?filter[id]='.implode(',', $orderItemIds).'&include=branches',
                    [],
                    'GET',
                    $order->brand->foodics_token
                );
                $itemAddonFoodics = collect($itemAddons);

                if (! empty($itemAddonFoodics)) {
                    $oldAddonsPriceToBeDeducted = 0;
                    foreach ($order->addons as $oldAddon) {
                        $oldAddonsPriceToBeDeducted =
                            $oldAddonsPriceToBeDeducted + $oldAddon->quantity * $oldAddon->price;
                    }
                    // get OrderAddons Ids to delete translations then delete order Addons
                    $oldOrderAddonsIds = $order->addons()->pluck('id');
                    OrderAddonTranslation::whereIn('order_addon_id', $oldOrderAddonsIds)->delete();
                    $order->addons()->delete();
                    $order->price = $order->price - $oldAddonsPriceToBeDeducted;
                    $order->save();

                    // set the initial orderPrice to order price after deducting the old addons price
                    $orderPrice = $order->price;
                    foreach ($itemAddonFoodics as $addon) {
                        $addonPrice = $addon->price ?? 0;
                        if (! empty($addon->branches)) {
                            foreach ($addon->branches as $branch) {
                                if (
                                    $branch->id == $order->branch->foodics_branch_id &&
                                    ! empty($branch->pivot) &&
                                    $branch->pivot->price > 0
                                ) {
                                    $addonPrice = $branch->pivot->price;
                                }
                            }
                        }
                        $orderAddonData['price'] = $addonPrice;
                        $orderAddonData['quantity'] = $addonsArr[$addon->id]['quantity'];
                        $orderAddonData['total_price'] = $orderAddonData['price'] * $orderAddonData['quantity'];
                        $orderAddonData['addon_id'] = $addon->id;
                        $orderAddonData['order_id'] = $order->id;
                        $orderAddonData['ar']['title'] = $addon->name_localized;
                        $orderAddonData['en']['title'] = $addon->name;

                        OrderAddon::create($orderAddonData);
                        // add the bundles price to the order price
                        $orderPrice = $orderPrice + $orderAddonData['total_price'];
                    }
                }

                $order->price = $orderPrice;
                if (! empty($args['food_arrival'])) {
                    $order->food_arrival = $args['food_arrival'];
                }
                $order->save();
                $arr['order'] = $order;
            }
        }

        return $arr;
    }

    public function handleBirthdayAddons($args, $order)
    {
        $addonsArr = [];
        foreach ($args['orderItems'] as $orderItem) {
            $validationResult = $this->checkBirthdayAddon($orderItem, $order);
            if (! empty($validationResult['error']) && $validationResult['error'] == 1) {
                $arr['result'] = $validationResult;

                return $arr;
            }
            $addonsArr[$orderItem['id']] = [
                'quantity' => $orderItem['quantity'],
                'customize_text' => $orderItem['customize_text'] ?? '',
            ];
        }
        if (! empty($addonsArr)) {
            $oldAddonsPriceToBeDeducted = 0;
            foreach ($order->addons as $oldAddon) {
                $oldAddonsPriceToBeDeducted =
                    $oldAddonsPriceToBeDeducted + ($oldAddon->quantity * $oldAddon->price);
            }
            // get OrderAddons Ids to delete translations then delete order Addons
            $order->addons()->delete();
            $order->price = $order->price - $oldAddonsPriceToBeDeducted;
            $order->save();

            // set the initial orderPrice to order price after deducting the old addons price
            $orderPrice = $order->price;
            foreach ($addonsArr as $key => $addon) {
                $addon = Addon::find($key);
                $addonPrice = $addon->price ?? 0;

                $orderAddonData['price'] = $addonPrice;
                $orderAddonData['quantity'] = $addonsArr[$addon->id]['quantity'];
                $orderAddonData['customize_text'] = $addonsArr[$addon->id]['customize_text'];
                $orderAddonData['total_price'] = $orderAddonData['price'] * $orderAddonData['quantity'];
                $orderAddonData['addon_id'] = $addon->id;
                $orderAddonData['order_id'] = $order->id;
                $orderAddonData['ar']['title'] = $addon->name_localized;
                $orderAddonData['en']['title'] = $addon->name;

                OrderAddon::create($orderAddonData);
                // add the bundles price to the order price
                $orderPrice = $orderPrice + $orderAddonData['total_price'];
            }
            $order->price = $orderPrice;
            $order->save();
        }

        $arr['order'] = $order;

        return $arr;
    }

    public function checkBirthdayAddon($orderItem, $order)
    {
        $addon = Addon::publish()
            ->ordered()
            ->where('brand_id', $order->brand_id)
            ->whereHas('branches', function ($query) use ($order) {
                $query->where('branch_id', $order->branch_id);
            })
            ->where('id', $orderItem['id'])
            ->first();
        if (empty($addon)) {
            $arr['error'] = 1;
            $arr['message'] = __('messages.addon_not_found');

            return $arr;
        }
    }
}