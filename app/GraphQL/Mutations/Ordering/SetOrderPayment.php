<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Events\CreateFoodicsOrder;
use App\Helpers\General;
use App\Models\Ordering\Order;
use App\Scopes\NonDraftScope;
use App\Services\Payment\WalletService;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOrderPayment
{
    public $generalHelper;

    protected WalletService $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General();
        $arr = [];
        $error = 0;
        $message = '';
        $createFoodicsOrder = false;
        // then check if order already exists and is draft and belongs to this user
        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            if ($order->order_date < date('Y-m-d')) {
                $error = 1;
                $message = __('messages.wrong_order_date');
            } else {
                if (!empty($args['payment_method'])) {
                    $constant = $this->generalHelper->getConstantItemByTitle('PaymentMethod', $args['payment_method']);
                    if ($constant) {
                        if (
                            ($args['payment_method'] == 'salaCredit' || $args['payment_method'] == 'loyaltyPoints')
                            && $order->is_draft == 1
                        ) {
                            $theWallet = $this->walletService->getTheWallet();
                            if ($theWallet[$args['payment_method']] > 0) {
                                // pay with one of the wallet options (salaCredit/loyaltyPoints) fully or partially
                                $arr['order'] = $this->payWithWallet($order, $theWallet, $args['payment_method']);
                            } else {
                                $arr['order'] = $order;
                                $error = 1;
                                $message = __('messages.insufficient_balance');
                            }
                        } elseif (
                            ($args['payment_method'] == 'salaCredit' || $args['payment_method'] == 'loyaltyPoints')
                            && $order->is_draft == 0
                        ) {
                            $error = 1;
                            $message = __('messages.order_not_found');
                        } elseif ($args['payment_method'] == 'payfort' || $args['payment_method'] == 'tamara' || $args['payment_method'] == 'checkout') {
                            // check if is_draft is already == 0 then this means
                            // its already partially paid order with failed external payment
                            // then this is a repay and a success message will be returned
                            if ($order->is_draft == '0') {
                                // this is for the repay action
                                $message = __('success');
                            }
                            // as using payfort or tamara we just set order to non draft
                            // the web hook will now make the rest of process
                            $order->is_draft = '0';

                            // check if the order is offer order
                            // then make is draft 0 for all its sub orders too

                            if ($order->offer_id != null) {
                                $subOrders = $order->offer_sub_orders;
                                if (empty($subOrders) || count($subOrders) == 0) {
                                    $subOrders = $order->draft_offer_sub_orders;
                                }
                                foreach ($subOrders as $subOrder) {
                                    $subOrder->update(['is_draft' => '0']);
                                }
                            }

                            $order->status = 0;
                            $order->save();

                            $arr['order'] = $order;
                        }
                    } else {
                        $error = 1;
                        $message = __('messages.You_Must_Enter_One_of_Allowed_Payment');
                    }
                } else {
                    $error = 1;
                    $message = __('messages.You_Must_Enter_One_of_Allowed_Payment');
                }
            }
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        if (!empty($arr['order']) && $arr['order']->to_be_paid == 0) {
            $tomorrowAt2Am = date('Y-m-d H:i:s', strtotime('tomorrow 02:00:00'));
            $orderDateTime = $order->order_date.' '.$order->order_time;
            if (!empty($order->addons) && count($order->addons) > 0 && $orderDateTime <= $tomorrowAt2Am) {
                event(new CreateFoodicsOrder($order->id));
            }
        }

        return $arr;
    }

    public function payWithWallet($order, $theWallet, $method)
    {
        // set currency id default to 1 = salacredit
        // then update it to 2 = loyalty points if method is set to loyalty
        $currency_id = 1;
        if ($method == 'loyaltyPoints') {
            $currency_id = 2;
        }

        // set the methodMoney dynamically
        $methodMoney = $method.'Money';
        // check if this is a fully pay order (money in wallet is = or > order->to_be_paid

        if ($order->to_be_paid > 0) {
            if ($theWallet[$methodMoney] >= $order->to_be_paid) {
                $to_be_deducted_coins = $this->walletService->convertToCoins($order->to_be_paid, $method);
                // set the payment record and wallet transaction record
                $this->walletService->setPayment($order, $currency_id, $to_be_deducted_coins, $order->to_be_paid);
                $order->partially_paid = '1';
                $order->to_be_paid = 0;
                $order->save();
            } else {
                $to_be_deducted_coins = $theWallet[$method];
                $to_be_deducted_money = $this->walletService->convertToMoney($to_be_deducted_coins, $method);
                // set the payment record and wallet transaction record
                $this->walletService->setPayment($order, $currency_id, $to_be_deducted_coins, $to_be_deducted_money);

                $order->partially_paid = '1';
                $order->to_be_paid = $order->to_be_paid - $to_be_deducted_money;
                $order->save();
            }
        }

        return $order;
    }
}
