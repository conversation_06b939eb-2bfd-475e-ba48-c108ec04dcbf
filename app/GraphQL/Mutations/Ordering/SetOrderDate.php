<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Helpers\General;
use App\Models\Booking\Addon\BirthdaySetting;
use App\Models\Brand\WorkHour;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicketExtraInfo;
use App\Scopes\NonDraftScope;
use App\Services\Payment\WalletService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOrderDate
{
    protected WalletService $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General();
        $arr = [];
        $error = 0;
        $message = '';
        // first check if order_date is valid (greater than or equal today)
        if ($args['order_date'] >= today()) {
            // then check if order already exists and is draft and belongs to this user
            if (
                $order = Order::where('id', $args['order_id'])
                    ->withoutGlobalScope(NonDraftScope::class)
                    ->draft()
                    ->where('user_id', \Auth::user()->id)
                    ->first()
            ) {
                // handle the refund of the partially paid deposites
                $this->walletService->handlePartiallyPaidOrdersRefunds();

                // get the ids of tickets before deleting them to delete their extra info too
                $orderTicketsIds = $order->tickets->pluck('id');
                // also remove the price as tickets/bundles/addons have been deleted (deattached)
                $order->tickets()->delete();
                OrderTicketExtraInfo::whereIn('order_ticket_id', $orderTicketsIds)->delete();
                // delete the bundles/addons
                $order->bundles()->delete();
                $order->addons()->delete();
                // check this date/time is allowed and in working days not off
                $time = isset($args['birthday_time_slot']) ? $args['birthday_time_slot'] : (isset($args['order_time']) ? $args['order_time'] : null);
                if (!$this->isInOffDates($order->branch_id, $args['order_date'], $time)) {
                    // check if order_time is through "birthday_time_slot" then validate its available
                    if (!isset($args['birthday_time_slot']) || (isset($args['birthday_time_slot']) && !$this->validateTimeSlot($order, $args))) {
                        // check if order_date is really going to change
                        // then delete all existing tickets with their extra info
                        if (
                            date('Y-m-d', strtotime($args['order_date'])) != $order->order_date
                            || $order->order_time != $time
                        ) {
                            // change the order_date
                            $order->order_date = $args['order_date'];
                            // change the order_time
                            $order->order_time = $time;

                            $order->price = 0;
                            $order->total_price = 0;
                            $order->to_be_paid = 0;
                            $order->save();
                        }

                        $arr['order'] = $order;
                    } else {
                        $error = 1;
                        $message = __('messages.time_slot_is_not_available');
                    }
                } else {
                    $error = 1;
                    $message = __('messages.Order_Date_In_Off_Dates', [
                        'order_date' => date('Y-m-d', strtotime($args['order_date'])),
                    ]);
                }
            } else {
                $error = 1;
                $message = __('messages.no_order_found');
            }
        } else {
            $error = 1;
            $message = __('messages.wrong_order_date');
        }
        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    public function validateTimeSlot($order, $args)
    {
        $timeSlot = $args['birthday_time_slot'];
        $orderDate = $args['order_date'];
        $branchId = $order->branch_id;

        $day = date('l', strtotime($args['order_date']));
        $constant = $this->generalHelper->getConstantItemByTitle('Days', $day);

        if ($constant) {
            $day_id = $constant['id'];
        }
        if (!empty($day_id)) {
            if ($slotId = BirthdaySetting::where('day_id', $day_id)
                ->where('branch_id', $branchId)
                ->where('from_time', '<=', $timeSlot)
                ->where('to_time', '>=', $timeSlot)
                ->first()) {
                if (Order::where('order_date', $orderDate)
                    ->where('branch_id', $branchId)
                    ->where('order_time', '>=', date('H:i:s', strtotime($timeSlot.' - '.($slotId->break_duration + ($slotId->break_duration - 1)).' hours')))
                    ->where('order_time', '<=', date('H:i:s', strtotime($timeSlot.' + '.($slotId->break_duration + ($slotId->break_duration - 1)).' hours')))
                    ->where('is_birthday', '1')
                    ->where('payment_status', '1')
                    ->where('status', '!=', '2')
                    ->count() > 0) {
                    return 'error';
                }
            } else {
                return 'error';
            }
        } else {
            return 'error';
        }
    }

    public function isInOffDates($branch, $orderDate, $time = null)
    {
        $offDates = WorkHour::OffDates()
            ->where('branch_id', $branch)
            ->whereNull('to_time')
            ->whereNull('from_time');

        $offDates = $offDates->pluck('date')->toArray();

        return in_array(date('Y-m-d', strtotime($orderDate)), $offDates);
    }
}
