<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Helpers\General;
use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Models\Wallet\WalletTransaction;
use App\Scopes\NonDraftScope;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class RemoveOrderPayment
{
    public $generalHelper;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General();
        $arr = [];
        $error = 0;
        $message = '';
        $createFoodicsOrder = false;

        //then check if order already exists and is draft and belongs to this user
        if ($order = Order::where('id', $args['order_id'])
            ->withoutGlobalScope(NonDraftScope::class)
            ->draft()
            ->where('user_id', Auth::user()->id)
            ->first()
        ) {
            if (empty($args['payment_method'])) {
                $args['payment_method'] = null;
            }
            $arr['order'] = $this->refundOrder($order, $args['payment_method']);
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    public function refundOrder($order, $method = '')
    {
        if (! empty($method)) {
            $methodId = ($method == 'salaCredit')
                ? 1
                : (($method == 'loyaltyPoints')
                    ? 2
                    : 0);
            if ($methodId > 0) {
                $orderPaymentsWithThisMethod = Payment::where('order_id', $order->id)->where('user_id', Auth::user()->id)->where('method_type', '0')->where('method_id', $methodId);
                $orderPaymentsWithThisMethodSum = $orderPaymentsWithThisMethod->sum('amount');

                $orderPaymentsWithThisMethod->delete();
                WalletTransaction::where('user_id', Auth::user()->id)
                    ->where('currency_id', $methodId)
                    ->where('order_id', $order->id)
                    ->delete();

                $order->to_be_paid = $order->to_be_paid + $orderPaymentsWithThisMethodSum;
                if ($order->to_be_paid == $order->total_price) {
                    $order->partially_paid = '0';
                }
                $order->save();

                return $order;
            }
        }
        if (empty($method) || $method == 0) {
            $refunded = $this->generalHelper->getConstantItemByTitle('PaymentStatus', 'Refunded')['id'];
            //fully refund the order

            //first turn this order payments status into refunded
            Payment::where('order_id', $order->id)->where('user_id', Auth::user()->id)->update([
                'status' => $refunded,
            ]);

            //get the wallet transactions payments for this order
            //to re add the credit to the user
            $paidTransactions = WalletTransaction::where('user_id', Auth::user()->id)
                ->where('order_id', $order->id)
                ->get();
            foreach ($paidTransactions as $transaction) {
                WalletTransaction::create([
                    'order_id' => $order->id,
                    'user_id' => Auth::user()->id,
                    'transaction_amount' => $transaction->transaction_amount,
                    'transaction_reason' => 'refund from order number :'.$order->id,
                    'transaction_type' => '1',
                    'currency_id' => $transaction->currency_id,
                    'payment_id' => $transaction->payment_id,
                ]);
            }
            $order->payment_status = $refunded;
            $order->to_be_paid = $order->total_price;
            $order->referral_user_id = null;
            $order->referral_code = null;
            $order->partially_paid = '0';
            $order->is_draft = '0';
            $order->save();

            return $order;
        }
    }
}
