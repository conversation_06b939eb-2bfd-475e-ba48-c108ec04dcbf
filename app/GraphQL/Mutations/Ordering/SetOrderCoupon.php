<?php

namespace App\GraphQL\Mutations\Ordering;

use App\Helpers\Constant;
use App\Models\BankOffer\BankOffer;
use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Wallet\WalletTransaction;
use App\Scopes\NonDraftScope;
use App\Services\Order\CouponService;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOrderCoupon
{
    protected CouponService $couponService;

    public function __construct(CouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $referralUser = 0;
        $error = 0;
        $message = '';

        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            if (isset($args['coupon_code'])) {
                if (!empty($order->offer_id)) {
                    $error = 1;
                    $message = __('messages.offer_cant_have_coupon');
                } else {
                    if (isset($args['coupon_type']) && $args['coupon_type'] == 'bankOffer') {
                        if (
                            empty($order->coupon)
                            || (!empty($order->coupon) && $order->coupon_type == Constant::RegisterCouponType)
                            || (!empty($order->coupon) && $order->coupon_type == Constant::CouponBankOfferType)
                        ) {
                            if ($bankOffer = BankOffer::whereJsonContains('card_inits', $args['coupon_code'])->first()) {
                                $coupon = Coupon::where('coupon_type', Constant::CouponBankOfferType)
                                    ->where('bank_offer_id', $bankOffer->id)
                                    ->first();
                                // call validate Coupon first to check if coupon is valid
                                $couponDetails = $this->couponService->validateCoupon($order, $coupon);
                                if ($couponDetails['error'] == 0) {
                                    $discount = $couponDetails['discount'];
                                    $arr = $this->getOrderAfterApplyCouponDiscount($discount, $order, $coupon, $arr);
                                } else {
                                    $error = 1;
                                    $message = $couponDetails['message'];
                                }
                            } else {
                                $error = 1;
                                $message = __('messages.coupon_not_found');
                            }
                        } else {
                            $error = 1;
                            $message = __('messages.coupon_not_found');
                        }
                    } else {
                        $couponWithReferralData = $this->couponService->checkIfUserIsEligibleForReferralCoupon(
                            $args['coupon_code'],
                            $order->brand_id
                        );
                        if (!empty($couponWithReferralData)) {
                            $coupon = $couponWithReferralData['coupon'];
                            // set the referral user data as this is a referral coupon
                            $referralUser = $couponWithReferralData['referralUser'];
                        } else {
                            $coupon = Coupon::where('code', $args['coupon_code'])
                                ->where(function ($q) use ($order) {
                                    $q->where('brand_id', $order->brand_id);
                                    $q->orWhereNull('brand_id');
                                })
                                ->where('active', '1')
                                ->first();
                        }
                        // check if coupon assigned to branches

                        $couponDetails = $this->couponService->validateCoupon($order, $coupon);

                        if ($couponDetails['error'] == 0) {
                            // get the discount on the new prices
                            $discount = $couponDetails['discount'];
                            $arr = $this->getOrderAfterApplyCouponDiscount($discount, $order, $coupon, $arr, $referralUser);
                        } else {
                            $error = 1;
                            $message = $couponDetails['message'];
                        }
                    }
                }
            }
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        //        $order = $order->fresh();
        $arr['order'] = $order;
        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    public function getOrderAfterApplyCouponDiscount(
        mixed $discount,
        $order,
        $coupon,
        array $arr,
        $referralUser = null
    ): array {
        Payment::where('order_id', $order->id)->delete();
        WalletTransaction::where('transaction_type', '0')
            ->where('order_id', $order->id)
            ->delete();

        // set the referral info if referralUser exists
        // and this means the coupon is referral coupon
        if (!empty($referralUser)) {
            $order->referral_user_id = $referralUser->id;
            $order->referral_code = $referralUser->referral_code;
        }

        $order->discount = $discount;
        $order->total_price = $order->price >= $discount ? $order->price - $discount : 0;
        $order->to_be_paid = $order->total_price;
        $order->coupon_id = $coupon->id;
        $order->coupon_type = $coupon->coupon_type;
        $order->coupon_discount = $discount;
        $order->auto_apply_coupon = '0';
        $order->save();
        $arr['order'] = $order;

        return $arr;
    }
}
