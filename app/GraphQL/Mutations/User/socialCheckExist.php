<?php

namespace App\GraphQL\Mutations\User;

use App\Models\AppleSocial;
use App\Models\Provider;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class socialCheckExist
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->requestToken($args);
    }

    protected function requestToken($args)
    {
        // only for twitter
        $userFromDb = null;
        $token = null;
        $apple = null;
        $provider = Provider::where([
            'provider' => $args['provider'],
            'provider_id' => $args['provider_id'],
        ])->first();

        if ($provider) {
            $userFromDb = $provider->user;
            if ($userFromDb) {
                $token = $userFromDb->createToken('token-name')->plainTextToken;
            }
        }
        if (!$provider && $args['provider'] == 'apple' && isset($args['email'])) {
            $apple = AppleSocial::firstOrCreate(
                [
                    'provider' => $args['provider'],
                    'provider_id' => $args['provider_id'],
                ],
                [
                    'email' => $args['email'] ?? '',
                    'first_name' => $args['first_name'] ?? '',
                    'last_name' => $args['last_name'] ?? '',
                ]
            );
        }
        if (!$provider && $args['provider'] == 'apple') {
            $apple = AppleSocial::where([
                'provider' => $args['provider'],
                'provider_id' => $args['provider_id'],
            ])->first();
        }

        return ['token' => $token, 'user' => $userFromDb, 'apple' => $apple];
    }
}
