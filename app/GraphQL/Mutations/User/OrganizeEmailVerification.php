<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Events\OrganizeEmailVerified;
use App\Models\User;
use Illuminate\Contracts\Translation\Translator;

class OrganizeEmailVerification
{
    protected Translator $translator;

    public function __construct(Translator $translator)
    {
        $this->translator = $translator;
    }

    public function __invoke($_, array $args): array
    {
        $user = '';
        $user = User::where('id', auth()->user()->id)
            ->where(function ($q) {
                $q->whereDate('expire_email_date', '<', date('Y-m-d'));
                $q->orWhereNull('expire_email_date');
            })
            ->first();

        if (!empty($user)) {
            $user->update(['organizational_email' => $args['organizational_email']]);

            event(new OrganizeEmailVerified($user));

            return [
                'status' => 'VERICATION_URL_SENT',
                'message' => __('messages.EMAIL_SENT'),
            ];
        } else {
            return [
                'status' => 'EMAIL_ALREADY_EXIST',
                'message' => __('messages.EMAIL_ALREADY_EXIST'),
            ];
        }
    }
}