<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Helpers\DynamicLink;
use App\Helpers\SMS;
use App\Mail\ForgotPasswordMail;
use App\Models\User;
use App\Services\Auth\ResetPasswordService;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Contracts\Auth\PasswordBroker;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class ForgotPassword
{
    protected PasswordBroker $passwordBroker;

    protected ResetPasswordService $resetPasswordService;

    protected Translator $translator;

    public CanResetPassword $notifiable;

    public function __construct(
        PasswordBroker $passwordBroker,
        ResetPasswordService $resetPasswordService,
        Translator $translator
    ) {
        $this->passwordBroker = $passwordBroker;
        $this->resetPasswordService = $resetPasswordService;
        $this->translator = $translator;
    }

    public function __invoke($_, array $args): array
    {
        $code = rand(1000, 4000);
        if ($args['via'] == 'sms') {
            $user = User::where(['mobile' => $args['mobile']])->first();
        } else {
            $user = User::where(['email' => $args['email']])->first();
        }

        if ($user) {
            $token = $this->passwordBroker->createToken($user);
            $reset_password_link = '';
            // if (isset($args['reset_password_url'])) {
            //     $reset_password_link = $this->transformUrl(
            //         $user,
            //         $code,
            //         $args['reset_password_url']['url']
            //     );
            // }
            $reset_password_link = 'https://salahub.ninja/reset_password/email='.$user->email.'/code='.$code;
            $link = DynamicLink::generate($reset_password_link);
            DB::table('password_resets')
                ->where('email', $user['email'])
                ->update(['code' => $code]);

            if (isset($args['via']) && $args['via'] === 'sms') {
                $message = __('messages.ForgotPasswordSMSMessage', [
                    'link' => $link,
                    'code' => $code,
                    'userName' => $user->first_name.' ',
                    $user->last_name,
                ]);
                $countryCode = isset($args['countryCode']) ? $args['countryCode'] : '+966';
                SMS::send($args['mobile'], $message, $countryCode);

                return [
                    'status' => 'SMS_SENT',
                    'message' => $message,
                ];
            } else {
                $mailDataView = new ForgotPasswordMail($code, $link, $user);
                Mail::to($args['email'])->send($mailDataView);

                return [
                    'status' => 'EMAIL_SENT',
                    'message' => __('password.sent'),
                ];
            }
        } else {
            $message = __('messages.EmailAddressNotExist');
            if ($args['via'] == 'sms') {
                $message = __('messages.MobileNotExist');
            }

            return [
                'status' => 'USER_NOT_FOUND',
                'message' => $message,
            ];
        }
    }

    public function transformUrl(CanResetPassword $notifiable, $code, string $url): string
    {
        return str_replace(['__EMAIL__', '__CODE__'], [$notifiable->getEmailForPasswordReset(), $code], $url);
    }
}
