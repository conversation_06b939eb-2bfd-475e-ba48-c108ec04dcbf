<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Models\User;
use DanielDeWit\LighthouseSanctum\Contracts\Services\ResetPasswordServiceInterface;
use DanielDeWit\LighthouseSanctum\Exceptions\GraphQLValidationException;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\PasswordBroker;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class ResetPassword
{
    protected PasswordBroker $passwordBroker;
    protected Translator $translator;
    protected ResetPasswordServiceInterface $resetPasswordService;

    public function __construct(
        PasswordBroker $passwordBroker,
        Translator $translator,
        ResetPasswordServiceInterface $resetPasswordService
    ) {
        $this->passwordBroker = $passwordBroker;
        $this->translator = $translator;
        $this->resetPasswordService = $resetPasswordService;
    }

    public function __invoke($_, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        $credentials = Arr::except($args, ['directive', 'password_confirmation']);

        if ($args['code']) {
            $codeData = DB::table('password_resets')
                ->where('code', $args['code'])
                ->first();

            if (!$codeData) {
                return [
                    'status' => 'PASSWORD_RESET',
                    'message' => __('auth.You_have_entered_a_wrong_code_for_reset_password'),
                ];
            }
            if (isset($args['email']) && $args['email'] != '') {
                $user = User::where('email', $args['email'])->first();
            }

            if (isset($args['mobile']) && $args['mobile'] != '') {
                $user = User::where('mobile', $args['mobile'])->first();
            }

            if (!$user) {
                return [
                    'status' => 'PASSWORD_RESET',
                    'message' => __('auth.You_Have_Entered_A_Wrong_Email_For_Reset_Password'),
                ];
            }

            $user->password = \Hash::make($args['password']);
            $user->update(); //or $user->save();
            DB::table('password_resets')
                ->where('email', $user->email)
                ->delete();

            return [
                'status' => 'PASSWORD_RESET',
                'message' => __('password.sent'),
            ];
        } else {
            $response = $this->passwordBroker->reset($credentials, function (Authenticatable $user, string $password) {
                $this->resetPasswordService->resetPassword($user, $password);
            });

            if ($response === PasswordBroker::PASSWORD_RESET) {
                return [
                    'status' => 'PASSWORD_RESET',
                    'message' => $this->translator->get($response),
                ];
            }

            throw new GraphQLValidationException(
                $this->translator->get($response),
                $this->getInvalidField($response),
                $resolveInfo
            );
        }
    }

    protected function getInvalidField(string $response): string
    {
        switch ($response) {
            case PasswordBroker::INVALID_USER:
                return 'email';

            case PasswordBroker::INVALID_TOKEN:
                return 'token';

            default:
                return '';
        }
    }
}
