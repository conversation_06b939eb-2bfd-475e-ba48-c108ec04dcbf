<?php

namespace App\GraphQL\Mutations\User;

use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UpdateBillingAddress
{
    public $error = 0;
    public $message = '';
    public $response;
    public $data = [];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $user = auth()->user();
        $address = $user
            ->billingAddress()
            ->where('id', $args['id'])
            ->first();
        if (empty($address)) {
            $this->message = __('messages.NotFound');
        } else {
            $this->data = $user
                ->billingAddress()
                ->where('id', $args['id'])
                ->update([
                    'name' => $args['name'],
                    'customer_address1' => $args['customer_address1'] ?? '',
                    'customer_address2' => isset($args['customer_address2']) ? $args['customer_address2'] : '',
                    'customer_apartment_no' => $args['customer_apartment_no'],
                    'customer_city' => $args['customer_city'],
                    'customer_state' => $args['customer_state'],
                    'customer_zip_code' => $args['customer_zip_code'],
                    'customer_country_code' => $args['customer_country_code'],
                ]);
            $this->message = __('messages.UpdateBillingAddressSuccess');
        }
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
            'data' => $this->data,
        ];

        return $res;
    }
}
