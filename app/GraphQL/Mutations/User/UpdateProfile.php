<?php

namespace App\GraphQL\Mutations\User;

use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Storage;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UpdateProfile
{
    public $error = 0;
    public $message = '';
    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $user = auth()->user();
        $args['name'] = $args['first_name'] . ' ' . $args['last_name'];
        User::where('id', $user->id)->update([
            'name' => $args['first_name'] . ' ' . $args['last_name'],
            'date_of_birth' => isset($args['date_of_birth']) ? $args['date_of_birth'] : $user->date_of_birth,
            'country' => $args['country'],
            'nationality' => $args['nationality'],
            'city' => $args['city'] ?? '',
            'gender' => isset($args['gender']) ? $args['gender'] : $user->gender,
            'mobile' => $args['mobile'],
            'first_name' => $args['first_name'],
            'last_name' => $args['last_name'],
        ]);
        if (isset($args['image']) && $args['image']['key']) {
            //upload image with update profile
            $path = 'users/' . $user->id . '/profile/';
            $name = $args['image']['content_type']
                ? $args['image']['uuid'] . '.' . explode('/', $args['image']['content_type'])[1]
                : $args['image']['uuid'] . '.png';
            $image = $path . $name;

            User::where('id', $user->id)->update([
                'profile_photo' => $image,
            ]);
            Storage::disk('s3')->copy($args['image']['key'], $image, 'public');
        }
        $this->message = __('messages.UpdateProfileSuccess');
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
