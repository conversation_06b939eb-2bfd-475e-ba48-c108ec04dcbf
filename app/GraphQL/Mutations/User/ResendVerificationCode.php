<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Helpers\DynamicLink;
use App\Helpers\SMS;
use App\Mail\ForgotPasswordMail;
use App\Models\User;
use App\Services\Auth\ResetPasswordService;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Contracts\Auth\PasswordBroker;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class ResendVerificationCode
{
    protected PasswordBroker $passwordBroker;

    protected Translator $translator;

    public CanResetPassword $notifiable;

    protected ResetPasswordService $resetPasswordService;

    public function __construct(
        PasswordBroker $passwordBroker,
        Translator $translator,
        ResetPasswordService $resetPasswordService
    ) {
        $this->passwordBroker = $passwordBroker;
        $this->translator = $translator;
        $this->resetPasswordService = $resetPasswordService;
    }

    public function __invoke($_, array $args): array
    {
        $code = rand(1000, 4000);
        $via = 'email';
        $user = '';
        if (isset($args['email'])) {
            $user = User::where(['email' => $args['email']])->first();
        }

        if (isset($args['mobile'])) {
            $user = User::where(['mobile' => $args['mobile']])->first();
            $via = 'sms';
        }

        $token = '';
        $reset_password_link = '';
        $link = '';

        if (! empty($user)) {
            $token = $this->passwordBroker->createToken($user);
            // if (isset($args['reset_password_url'])) {
            //     $reset_password_link = $this->transformUrl(
            //         $user,
            //         $code,
            //         $args['reset_password_url']['url']
            //     );
            // }
            $reset_password_link = 'https://salahub.ninja/reset_password/email='.$user->email.'/code='.$code;
            $link = DynamicLink::generate($reset_password_link);
            DB::table('password_resets')
                ->where('email', $user['email'])
                ->update(['code' => $code]);

            if ($via === 'sms') {
                $message = __('messages.ForgotPasswordSMSMessage', [
                    'link' => $link,
                    'code' => $code,
                    'userName' => $user->first_name.' ',
                    $user->last_name,
                ]);
                // $countryCode = isset($args['countryCode']) ? $args['countryCode'] : '+966';

                SMS::send($args['mobile'], $message);

                return [
                    'status' => 'VERICATION_CODE_SENT',
                    'message' => $message,
                ];
            } else {
                $mailDataView = new ForgotPasswordMail($code, $link, $user);
                Mail::to($args['email'])->send($mailDataView);

                return [
                    'status' => 'VERICATION_CODE_SENT',
                    'message' => __('messages.EMAIL_SENT'),
                ];
            }
        } else {
            return [
                'status' => 'USER_NOT_FOUND',
                'message' => '',
            ];
        }
    }

    public function transformUrl(CanResetPassword $notifiable, string $token, string $url): string
    {
        return str_replace(['__EMAIL__', '__TOKEN__'], [$notifiable->getEmailForPasswordReset(), $token], $url);
    }
}
