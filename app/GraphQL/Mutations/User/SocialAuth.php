<?php

namespace App\GraphQL\Mutations\User;

use App\Events\RegisterCouponNotification;
use App\Mail\WelcomeMail;
use App\Models\Invitation\Friend;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\Provider;
use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use App\Models\Group\Group;

class SocialAuth
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->requestToken($args);
    }

    protected function requestToken($args)
    {
        // only for twitter
        $userFromDb = null;
        $token = null;
        $error = 0;
        $message = '';
        // Getting or creating user from db
        $provider = Provider::where([
            'provider' => $args['provider'],
            'provider_id' => $args['provider_id'],
        ])->first();

        if (!empty($provider)) {
            $error = 1;
            $message = __('messages.AlreadyRegister');

            return [
                'token' => $token,
                'user' => $userFromDb,
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }
        $checkFirstRegister = User::where('email', $args['email'])->count();
        $userFromDb = User::updateOrCreate(
            ['email' => $args['email']],
            [
                'lang' => app()->getLocale() ?? 'en',
                'email_verified_at' => now(),
                'first_name' => $args['first_name'],
                'name' => $args['first_name'] . ' ' . $args['last_name'],
                'last_name' => $args['last_name'],
                'email' => $args['email'],
                'country' => $args['country'] ?? null,
                'city' => $args['city'] ?? null,
                'nationality' => $args['nationality'] ?? null,
                'mobile' => $args['mobile'],
                'status' => true,
                'referral_code' => uniqueStringCode('App\Models\User', 'referral_code', 3),
                'date_of_birth' =>
                    $args['date_of_birth'] != '' ? date('Y-m-d', strtotime($args['date_of_birth'])) : null,
            ]
        );

        $userFromDb = User::where('email', $args['email'])->first();
        $userFromDb->providers()->updateOrCreate([
            'provider' => $args['provider'],
            'provider_id' => $args['provider_id'],
        ]);

        if (isset($args['image']) && $args['image']['key']) {
            //upload image with update profile
            $path = 'users/' . $userFromDb->id . '/profile/';
            $name = $args['image']['content_type']
                ? $args['image']['uuid'] . '.' . explode('/', $args['image']['content_type'])[1]
                : $args['image']['uuid'] . '.png';
            $image = $path . $name;
            User::where('id', $userFromDb->id)->update([
                'profile_photo' => $image,
            ]);
            Storage::disk('s3')->copy($args['image']['key'], $image, 'public');
        }
        $token = $userFromDb->createToken('token-name')->plainTextToken;

        // Returning response
        $message = __('messages.RegisterSuccess');

        $coupon = Coupon::Register()
            ->Publish()
            ->NotUser()
            ->where('notify_days', 0)
            ->first();

        if (!empty($coupon) && $checkFirstRegister == 0) {
            event(new RegisterCouponNotification($userFromDb, $coupon));
        } else {
            Mail::to($userFromDb->email)->send(new WelcomeMail($userFromDb));
        }

        Friend::where(function ($q) use ($userFromDb) {
            $q->where('email', $userFromDb->email);
            $q->orWhere('mobile', $userFromDb->mobile);
        })
            ->whereNull('user_id')
            ->where('status', 0)
            ->update(['user_id' => $userFromDb->id]);

        // assign to default group
        $group = Group::whereTranslation('title', 'Silver', 'en')->first();
        if (!empty($group)) {
            $userFromDb->groups()->save($group);
        }

        return [
            'token' => $token,
            'user' => $userFromDb,
            'result' => [
                'error' => $error,
                'message' => $message,
            ],
        ];
    }
}