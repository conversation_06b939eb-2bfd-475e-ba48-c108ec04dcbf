<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Models\User;
use DanielDeWit\LighthouseSanctum\Exceptions\HasApiTokensException;
use DanielDeWit\LighthouseSanctum\Traits\HasAuthenticatedUser;
use Illuminate\Contracts\Auth\Factory as AuthFactory;
use Illuminate\Contracts\Translation\Translator;
use Laravel\Sanctum\Contracts\HasApiTokens;

class DeleteUser
{
    use HasAuthenticatedUser;

    protected AuthFactory $authFactory;

    protected Translator $translator;

    public function __construct(AuthFactory $authFactory, Translator $translator)
    {
        $this->authFactory = $authFactory;
        $this->translator = $translator;
    }

    public function __invoke($_, array $args): array
    {
        $user = $this->getAuthenticatedUser();

        if (! $user instanceof HasApiTokens) {
            throw new HasApiTokensException($user);
        }
        $user = User::where('id', $user->id)->first();

        // $checkExist = Family::where('user_id', $user->id)->first();

        // if (!empty($checkExist) && $checkExist->no_members > 1) {
        //     $message = __('messages.CanNotDeleteYourAccount');
        //     $error = 1;

        //     return [
        //         'result' => [
        //             'error' => $error,
        //             'message' => $message,
        //         ],
        //     ];
        // }

        $user->providers()->delete();
        $user->delete();

        return [
            'status' => 'USER_DELETED',
            'message' => $this->translator->get('this user is deleted successfully'),
        ];
    }

    protected function getAuthFactory(): AuthFactory
    {
        return $this->authFactory;
    }
}
