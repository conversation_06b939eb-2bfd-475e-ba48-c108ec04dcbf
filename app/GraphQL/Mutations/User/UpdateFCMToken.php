<?php

namespace App\GraphQL\Mutations\User;

use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UpdateFCMToken
{
    public $error = 0;
    public $message = '';
    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $user = auth()->user();
        $user
            ->devices()
            ->where(['device_id' => $args['device_id']])
            ->delete();

        $user->devices()->updateOrCreate(
            ['device_id' => $args['device_id']],
            [
                'device_id' => $args['device_id'],
                'status' => 1,
                'lang' => app()->getLocale(),
                'fcm_token' => $args['fcm_token'] ?? '',
                'version' => $args['version'] ?? '',
                'platform' => $args['platform'] ?? '',
            ]
        );
        $this->message = __('messages.Success');
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}