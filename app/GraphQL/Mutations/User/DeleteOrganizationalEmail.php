<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class DeleteOrganizationalEmail
{
    public $error = 0;

    public $message = '';

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $user = auth()->user();
        if ($user) {
            if ($user->organizational_email) {
                $search = trim(explode('@', $user->organizational_email)[1]);
                $fixed_group = $user->groups->where('type', 'fixed')->first();
                if (!empty($fixed_group)) {
                    $user->groups()->detach($fixed_group->id);
                }

                User::where('id', $user->id)->update([
                    'organizational_email' => null,
                    'expire_email_date' => null,
                ]);
                $this->message = __('messages.OrganizationalEmailDeleted');
            } else {
                $this->error = 1;
                $this->message = __('messages.OrganizationalEmailNotFound');
            }
        } else {
            $this->error = 1;
            $this->message = __('messages.UserNotFound');
        }

        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}