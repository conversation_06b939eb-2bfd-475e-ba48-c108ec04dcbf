<?php

namespace App\GraphQL\Mutations\User;

use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\DB;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use App\Models\User;

class VerifyCode
{
    public function __construct()
    {
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->verifyCode($args);
    }

    protected function verifyCode($args)
    {
        $error = 0;
        $message = '';
        $email = '';

        if (isset($args['mobile'])) {
            $user = User::where('mobile', $args['mobile'])->first();
            $email = $user->email;
        } else {
            $email = $args['email'];
        }

        $userResetPasswordRow = DB::table('password_resets')
            ->where([['email', '=', $email]])
            ->first();

        if (!is_null($userResetPasswordRow)) {
            if ($userResetPasswordRow->code === $args['code']) {
                $message = 'Success';
            } else {
                $error = 1;
                $message = __('auth.You_Have_Entered_A_Wrong_Code_For_Reset_Password');
            }
        } else {
            $error = 1;
            $message = __('auth.You_Have_Entered_A_Wrong_Email_For_Reset_Password');
        }

        // Returning response
        return [
            'result' => [
                'error' => $error,
                'message' => $message,
            ],
        ];
    }
}
