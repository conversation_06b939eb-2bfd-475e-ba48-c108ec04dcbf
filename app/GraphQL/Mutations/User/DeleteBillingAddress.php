<?php

namespace App\GraphQL\Mutations\User;

use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class DeleteBillingAddress
{
    public $error = 0;
    public $message = '';
    public $response;
    public $data = [];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $user = auth()->user();
        $addresses = $user->billingAddress()->count();
        if ($addresses <= 1) {
            $this->message = __('messages.DeleteBillingAddressFailed');
        } else {
            $address = $user
                ->billingAddress()
                ->where('id', $args['id'])
                ->first();
            if (empty($address)) {
                $this->message = __('messages.NotFound');
            } else {
                $this->data = $user
                    ->billingAddress()
                    ->where([
                        'id' => $args['id'],
                    ])
                    ->delete();
                $this->message = __('messages.DeleteBillingAddressSuccess');
            }
        }
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
            'data' => $this->data,
        ];

        return $res;
    }
}
