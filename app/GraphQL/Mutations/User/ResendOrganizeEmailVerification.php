<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Events\OrganizeEmailVerified;
use App\Models\User;
use Illuminate\Contracts\Translation\Translator;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

class ResendOrganizeEmailVerification
{
    protected Translator $translator;

    public function __construct(Translator $translator)
    {
        $this->translator = $translator;
    }

    public function __invoke($_, array $args): array
    {
        $user = '';
        $user = User::where(['organizational_email' => $args['organizational_email']])->first();

        if ($user) {
            event(new OrganizeEmailVerified($user));

            return [
                'status' => 'VERICATION_URL_SENT',
                'message' => __('messages.EMAIL_SENT'),
            ];
        } else {
            throw new AuthenticationException(__('auth.The_provided_credentials_are_incorrect'));
        }
    }
}
