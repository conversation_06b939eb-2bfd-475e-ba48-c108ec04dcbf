<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Events\RegisterCouponNotification;
use App\Mail\WelcomeMail;
use App\Models\Group\Group;
use App\Models\Invitation\Friend;
use App\Models\Promotion\Coupon\Coupon;
use App\Models\User;
use DanielDeWit\LighthouseSanctum\Contracts\Services\EmailVerificationServiceInterface;
use DanielDeWit\LighthouseSanctum\Exceptions\HasApiTokensException;
use DanielDeWit\LighthouseSanctum\Traits\CreatesUserProvider;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Config\Repository as Config;
use Illuminate\Contracts\Hashing\Hasher;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Contracts\HasApiTokens;

class Register
{
    use CreatesUserProvider;

    protected AuthManager $authManager;

    protected Config $config;

    protected Hasher $hash;

    protected EmailVerificationServiceInterface $emailVerificationService;

    public function __construct(
        AuthManager $authManager,
        Config $config,
        Hasher $hash,
        EmailVerificationServiceInterface $emailVerificationService
    ) {
        $this->authManager = $authManager;
        $this->config = $config;
        $this->hash = $hash;
        $this->emailVerificationService = $emailVerificationService;
    }

    public function __invoke($_, array $args): array
    {
        $userProvider = $this->createUserProvider();

        $user = $this->saveUser($userProvider->createModel(), $this->getPropertiesFromArgs($args));

        if ($user instanceof MustVerifyEmail) {
            if (isset($args['verification_url'])) {
                $this->emailVerificationService->setVerificationUrl($args['verification_url']['url']);
            }

            $user->sendEmailVerificationNotification();

            return [
                'token' => null,
                'status' => 'MUST_VERIFY_EMAIL',
            ];
        }

        if (! $user instanceof HasApiTokens) {
            throw new HasApiTokensException($user);
        }

        $coupon = Coupon::Register()
            ->Publish()
            ->NotUser()
            ->where('notify_days', 0)
            ->first();

        if (! empty($coupon)) {
            $user = User::where('id', $user->id)->first();
            event(new RegisterCouponNotification($user, $coupon));
        } else {
            Mail::to($user->email)->send(new WelcomeMail($user));
        }

        //friend assign
        Friend::where(function ($q) use ($user) {
            $q->where('email', $user->email);
            $q->orWhere('mobile', $user->mobile);
        })
            ->whereNull('user_id')
            ->where('status', 0)
            ->update(['user_id' => $user->id]);

        // assign to default group
        $group = Group::whereTranslation('title', 'Silver', 'en')->first();
        if (! empty($group)) {
            $user = User::where('id', $user->id)->first();
            $user->groups()->save($group);
        }

        return [
            'token' => $user->createToken('default')->plainTextToken,
            'status' => 'SUCCESS',
        ];
    }

    protected function saveUser(Model $user, array $args): Model
    {
        $args['name'] = $args['first_name'].' '.$args['last_name'];
        $args['lang'] = app()->getLocale() ?? 'en';
        $args['referral_code'] = uniqueStringCode('App\Models\User', 'referral_code', 3);

        $user->fill($args)->save();

        if (isset($args['image']) && $args['image']['key']) {
            //upload image with update profile
            $path = 'users/'.$user->id.'/profile/';
            $name = $args['image']['content_type']
                ? $args['image']['uuid'].'.'.explode('/', $args['image']['content_type'])[1]
                : $args['image']['uuid'].'.png';
            $image = $path.$name;
            User::where('id', $user->id)->update([
                'profile_photo' => $image,
            ]);
            Storage::disk('s3')->copy($args['image']['key'], $image, 'public');
        }

        return $user;
    }

    protected function getPropertiesFromArgs(array $args): array
    {
        $properties = Arr::except($args, ['directive', 'password_confirmation', 'verification_url']);

        $properties['password'] = $this->hash->make($properties['password']);

        return $properties;
    }

    protected function getAuthManager(): AuthManager
    {
        return $this->authManager;
    }

    protected function getConfig(): Config
    {
        return $this->config;
    }
}
