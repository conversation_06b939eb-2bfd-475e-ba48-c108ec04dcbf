<?php

namespace App\GraphQL\Mutations\Offers;

use App\Events\OrderNotification;
use App\Events\ZatcaCreateInvoice;
use App\Helpers\General;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Scopes\NonDraftScope;
use App\Services\Payment\WalletService;
use Carbon\Carbon;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class ConfirmOfferOrder
{
    protected WalletService $walletService;

    public $generalHelper;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General;
        $arr = [];
        $error = 0;
        $message = '';

        // get instance of the order model and
        if (
            $orderInstance = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->isOffer()
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            // check if order_metadata is sent then save it
            if (isset($args['order_metadata'])) {
                $orderInstance->metadata = $args['order_metadata'];
            } else {
                // then set to empty
                $orderInstance->metadata = null;
            }
            // save the model instance
            $orderInstance->save();
        }
        // then check if order already exists and is draft and belongs to this user
        if (
            $order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->isOffer()
                ->where('user_id', Auth::user()->id)
                ->first()
        ) {
            // check date has not passed yet
            if ($this->isInvalidDate($order)) {
                return $this->creatErrorResponse(__('messages.wrong_order_date'));
            }

            // check on price and quantities
            if ($this->validatePriceAndQuantity($order)) {
                return $this->validatePriceAndQuantity($order);
            }

            if ($order->to_be_paid == 0 && ! empty($args['complete'])) {
                $this->fullyPayOrder($order);
                $arr['result']['error'] = 1;
                $arr['result']['message'] = __('messages.no_order_found');

                return $arr;
            }

            $order->wallet = $this->walletService->getTheWallet();
            $order->payments = $this->walletService->getOrderPayments($order);
            $order->partial_payments = $this->walletService->getTheOrderPayments($order);

            $arr['order'] = $order;
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    public function isInvalidDate($order)
    {
        $orderDate = Carbon::parse($order->order_date); // Convert to Carbon instance

        return $orderDate->toDateString() < today()->toDateString();
    }

    public function validatePriceAndQuantity($order)
    {
        if (! empty($order->quantity) && $order->quantity > 0) {
            if (! empty($order->offer->allTickets) && $order->offer->allTickets->count() > 0) {
                // reformat the tickets arr
                foreach ($order->offer->allTickets as $ticket) {
                    $ticketsArr[$ticket->brand_id][$ticket->ticket_id] = $ticket;
                }

                if (! empty($order->draft_offer_sub_orders) && count($order->draft_offer_sub_orders) > 0) {
                    foreach ($order->draft_offer_sub_orders as $subOrder) {
                        if (! empty($ticketsArr) && ! empty($ticketsArr[$subOrder->brand_id])) {
                            $orderTickets = OrderTicket::where('order_id', $subOrder->id)->get();
                            foreach ($orderTickets as $orderTicket) {
                                if (
                                    $orderTicket->quantity !=
                                    $ticketsArr[$subOrder->brand_id][$orderTicket->ticket->id]->quantity *
                                        $order->quantity
                                ) {
                                    return $this->creatErrorResponse(__('messages.invalid_quantity'));
                                }
                            }
                            if (
                                $order->total_price != $order->quantity * $order->offer->price ||
                                $order->price != $order->quantity * $order->offer->price
                            ) {
                                return $this->creatErrorResponse(__('messages.invalid_price'));
                            }
                        } else {
                            return $this->creatErrorResponse(__('messages.this_brand_doesnt_have_offer_tickets'));
                        }
                    }
                } else {
                    return $this->creatErrorResponse(__('messages.main_offer_order_doesnt_have_sub_orders'));
                }
            } else {
                return $this->creatErrorResponse(__('messages.offer_has_no_tickets'));
            }
        } else {
            return $this->creatErrorResponse(__('messages.you_must_set_quantity'));
        }
    }

    public function creatErrorResponse($msg)
    {
        $arr['result']['error'] = 1;
        $arr['result']['message'] = $msg;

        return $arr;
    }

    public function fullyPayOrder($order): mixed
    {
        $payementStatus = $this->generalHelper->getConstantItemByTitle('PaymentStatus', 'Paid')['id'];
        // update the order

        $order->payment_status = $payementStatus;
        $order->to_be_paid = 0;
        $order->is_draft = '0';

        // check if the order is offer order
        // then make is draft 0 for all its sub orders too
        if ($order->offer_id != null) {
            $subOrders = $order->offer_sub_orders();
            foreach ($subOrders as $subOrder) {
                $subOrder->is_draft = '0';
                $subOrder->save();
            }
        }

        $order->save();

        if (! empty($order->offer_id) && $order->offer_id > 0) {
            foreach ($order->draft_offer_sub_orders as $subOrder) {
                $subOrder->is_draft = '0';
                $subOrder->save();
            }
        }

        // fire zatca create invoice event
        event(new ZatcaCreateInvoice($order->id));
        // fire booking order notification event
        try {
            event(new OrderNotification($order, 4));
        } catch (\Exception $e) {
            $error = 1;
        }

        return $order;
    }
}
