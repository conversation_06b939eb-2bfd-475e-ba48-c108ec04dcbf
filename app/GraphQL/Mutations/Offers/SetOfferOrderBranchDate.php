<?php

namespace App\GraphQL\Mutations\Offers;

use App\Models\Brand\Branch;
use App\Models\Brand\WorkHour;
use App\Models\Ordering\Order;
use App\Models\Ordering\Payment;
use App\Models\Wallet\WalletTransaction;
use App\Scopes\NonDraftScope;
use App\Services\Payment\WalletService;
use Auth;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOfferOrderBranchDate
{

    protected WalletService $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';
        //first check if order_date is valid (greater than or equal today)
        if ($args['order_date'] >= today()) {
            //then check if order already exists and is draft and belongs to this user
            if ($order = Order::where('id', $args['order_id'])
                ->withoutGlobalScope(NonDraftScope::class)
                ->draft()
                ->isOffer()
                ->where('user_id', Auth::user()->id)
                ->first()
            ) {
                //get the branch
                $branch = Branch::find($args['branch_id']);
                //check brand is active
                if ($order->brand->active != 1) {
                    $arr['result']['error'] = 1;
                    $arr['result']['message'] = __('messages.brand_is_not_active_or_not_found');

                    return $arr;
                    //check if branch doesnt exist or not published
                } elseif (empty($branch) || $branch->active != 1) {
                    $arr['result']['error'] = 1;
                    $arr['result']['message'] = __('messages.branch_is_not_active_or_not_found');

                    return $arr;
                    //check if branch doesnt belong to brand
                } elseif ($branch->brand_id != $order->brand_id) {
                    $arr['result']['error'] = 1;
                    $arr['result']['message'] = __('messages.branch_doesnt_belong_to_brand');

                    return $arr;
                    //find or create new draft order
                } else {
                    //handle the refund of the partially paid deposites
                    $this->walletService->handlePartiallyPaidOrdersRefunds();
                    //check this date/time is allowed and in working days not off
                    $time = isset($args['order_time']) ? $args['order_time'] : null;
                    if (! $this->isInOffDates($branch, $args['order_date'], $time)) {
                        //check if order_date is really going to change
                        //then delete all existing tickets with their extra info
                        if (date('Y-m-d', strtotime($args['order_date'])) != $order->order_date || $order->order_time != $time) {
                            //change the order_date
                            $order->order_date = $args['order_date'];
                            //change the order_time
                            $order->order_time = $time;

                            $order->branch_id = $args['branch_id'];
                            //set the main order "order_date" to the maximum sub order "order_date"
                            //to use it in "no show" job later
                            if ($order->parentOfferOrder->order_date < $args['order_date']) {
                                $order->parentOfferOrder->order_date = $args['order_date'];
                                $order->parentOfferOrder->save();
                            }
                            $order->save();
                        }

                        $arr['order'] = $order;
                    } else {
                        $error = 1;
                        $message = __('messages.Order_Date_In_Off_Dates', [
                            'order_date' => date('Y-m-d', strtotime($args['order_date'])),
                        ]);
                    }
                }
            } else {
                $error = 1;
                $message = __('messages.no_order_found');
            }
        } else {
            $error = 1;
            $message = __('messages.wrong_order_date');
        }
        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }

    public function isInOffDates($branch, $orderDate, $time = null)
    {
        $offDates = WorkHour::OffDates()
            ->where('branch_id', $branch)
            ->whereNull('to_time')
            ->whereNull('from_time');

        $offDates = $offDates->pluck('date')->toArray();

        return in_array(date('Y-m-d', strtotime($orderDate)), $offDates);
    }
}
