<?php

namespace App\GraphQL\Mutations\Offers;

use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Scopes\NonDraftScope;
use App\Services\Payment\WalletService;
use Auth;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SetOfferOrderQuantity
{
    protected WalletService $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $arr = [];
        $error = 0;
        $message = '';

        // then check if order already exists and is draft and belongs to this user
        if ($order = Order::where('id', $args['order_id'])
            ->withoutGlobalScope(NonDraftScope::class)
            ->draft()
            ->isOffer()
            ->where('user_id', Auth::user()->id)
            ->first()
        ) {
            if (! empty($args['quantity']) && $args['quantity'] > 0) {
                if (! empty($order->offer->allTickets) && $order->offer->allTickets->count() > 0) {
                    // reformat the tickets arr
                    foreach ($order->offer->allTickets as $ticket) {
                        $ticketsArr[$ticket->brand_id][] = $ticket;
                    }
                    if (! empty($order->draft_offer_sub_orders) && count($order->draft_offer_sub_orders) > 0) {
                        foreach ($order->draft_offer_sub_orders as $subOrder) {
                            if (! empty($ticketsArr) && ! empty($ticketsArr[$subOrder->brand_id])) {
                                OrderTicket::where('order_id', $subOrder->id)->delete();

                                foreach ($ticketsArr[$subOrder->brand_id] as $subOrderTicket) {
                                    $orderTicketData['ticket_id'] = $subOrderTicket->ticket_id;
                                    $orderTicketData['order_id'] = $subOrder->id;
                                    $orderTicketData['quantity'] = $subOrderTicket->quantity * $args['quantity'];

                                    $orderTicketData['price'] = $subOrderTicket->ticket->price;
                                    $orderTicketData['total_price'] = $orderTicketData['quantity'] * $subOrderTicket->ticket->price;
                                    $orderTicketData['have_discount'] = $subOrderTicket->ticket->have_discount;
                                    $orderTicketData['price_before'] = $subOrderTicket->ticket->price_before;

                                    $orderTicketData['ar']['title'] = $subOrderTicket->ticket->title_ar;
                                    $orderTicketData['en']['title'] = $subOrderTicket->ticket->title_en;
                                    OrderTicket::create($orderTicketData);
                                }
                            } else {
                                $error = 1;
                                __('messages.this_brand_doesnt_have_offer_tickets');
                            }
                        }
                        if ($error == 0) {
                            // handle the refund of the partially paid deposites
                            $this->walletService->handlePartiallyPaidOrdersRefunds();

                            $order->quantity = $args['quantity'];
                            $order->price = $args['quantity'] * $order->offer->price;
                            $order->total_price = $args['quantity'] * $order->offer->price;
                            $order->to_be_paid = $args['quantity'] * $order->offer->price;
                            $order->save();
                        }
                    } else {
                        $error = 1;
                        $message = __('messages.main_offer_order_doesnt_have_sub_orders');
                    }
                } else {
                    $error = 1;
                    $message = __('messages.offer_has_no_tickets');
                }
            } else {
                $error = 1;
                $message = __('messages.you_must_set_quantity');
            }
            $arr['order'] = $order;
        } else {
            $error = 1;
            $message = __('messages.no_order_found');
        }

        $arr['result']['error'] = $error;
        $arr['result']['message'] = $message;

        return $arr;
    }
}
