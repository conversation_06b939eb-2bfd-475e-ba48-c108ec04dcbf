<?php

namespace App\GraphQL\Mutations\PayFort;

use App\Models\Ordering\Order;
use App\Services\Payment\PayFortService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GenerateSDKToken
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $error = 0;
        $message = '';

        $user = auth()->user();
        $order = Order::where('id', $args['order_id'])
            ->where('user_id', $user->id)
            ->first();
        if (!$order) {
            $message = __('messages.Order_Not_Found');
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }
        $payFortService = new PayFortService();
        $payFort = $payFortService->generateSDKToken($args['device_id'], $args['order_id']);

        return [
            'payFort' => $payFort,
            'result' => [
                'error' => $error,
                'message' => $message,
            ],
        ];
    }
}
