<?php

namespace App\GraphQL\Mutations\Survey;

use App\Models\Ordering\Order;
use App\Models\Survey\UserSurvey;
use App\Models\Survey\UserSurveyAnswer;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SubmitSurveyResult
{
    public $error = 0;
    public $message = '';
    public $orderId = null;
    public $response = [];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $user = auth()->user();
        $order = Order::where('id', $args['order_id'])
            ->where('user_id', $user->id)
            ->first();

        if (!$order) {
            return [
                'result' => [
                    'error' => 1,
                    'message' => __('messages.Order_Not_Found'),
                ],
            ];
        }
        if ($order && $order->reviewed == 1) {
            return [
                'result' => [
                    'error' => 1,
                    'message' => __('messages.Order_Reviewed_Before'),
                ],
            ];
        }

        $survey = UserSurvey::create([
            'is_finished' => $args['is_finished'],
            'user_id' => $user->id,
            'order_id' => $order->id,
            'order_date' => $order->order_date,
        ]);

        foreach ($args['questions'] as $item) {
            UserSurveyAnswer::create([
                'survey_id' => $item['id'],
                'user_survey_id' => $survey->id,
                'answer' => $item['answer'],
            ]);
        }
        $order->where('user_id', $user->id)->update(['reviewed' => 1, 'reviewed_at' => now()]);
        $this->message = __('messages.SubmitSurveySuccess');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
