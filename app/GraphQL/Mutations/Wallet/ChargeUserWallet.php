<?php

namespace App\GraphQL\Mutations\Wallet;

use App\Models\Ordering\Payment;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class ChargeUserWallet
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $points = $args['amount'];
        $user = auth()->user();
        $args['reference_number'] = time();
        $args['user_id'] = $user->id;

        $payment = Payment::create($args);
        $salaCreditRules = \App\Models\Wallet\SalaCreditRule::find(1);
        if (! empty($salaCreditRules)) {
            $points = ($payment->amount * $salaCreditRules->charging_coins) / $salaCreditRules->charging_money;
        }

        return [
            'payment' => $payment,
            'points' => $points,
            'result' => [
                'error' => 0,
                'message' => __('messages.PaymentCreated'),
            ],
        ];
    }
}
