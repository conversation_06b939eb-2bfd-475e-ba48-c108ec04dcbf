<?php

namespace App\GraphQL\Mutations\Notification;

use App\Models\Notification\Notification;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class ReadNotification
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $error = 0;
        $message = '';
        $user = auth()->user();

        Notification::whereIn('id', $args['notifications_ids'])
            ->where('user_id', $user->id)
            ->update([
                'status' => 1,
                'read_at' => now(),
            ]);

        $message = __('messages.Read_Notification_Successfully');

        return [
            'result' => [
                'error' => $error,
                'message' => $message,
            ],
        ];
    }
}
