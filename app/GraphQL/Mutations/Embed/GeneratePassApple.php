<?php

namespace App\GraphQL\Mutations\Embed;

use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GeneratePassApple
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response = '';

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $user = auth()->user();

        // check if card is exist
        $result = (new EmbedService)->generatePassApple($user->id, $args['card_number']);
        if ($result['code'] == 200) {
            $this->message = __('messages.GeneratePassAppleSuccess');
            $this->response = $result['result'];

            return $this->getResponse();
        }

        // there's an error
        $this->error = 1;
        $this->response = $result['result'];
        $this->message = ! empty($result['result']->message)
            ? $result['result']->message
            : __('messages.GeneratePassAppleFailed');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
            'pass' => $this->response,
        ];

        return $res;
    }
}
