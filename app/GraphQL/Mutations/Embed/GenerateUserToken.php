<?php

namespace App\GraphQL\Mutations\Embed;

use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GenerateUserToken
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response = '';

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $user = auth()->user();

        // check if card is exist
        $this->response = (new EmbedService)->login($user->id);

        // there's an error
        $this->error = 1;
        $this->message = __('messages.TokenGenerateSuccess');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
            'token' => $this->response,
        ];

        return $res;
    }
}
