<?php

namespace App\GraphQL\Mutations\Embed;

use App\Models\EmbedCard;
use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class RegisterNewCardAccount
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response = [];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $user = auth()->user();
        $embedCheckExistAccount = EmbedCard::where('user_id', $user->id)
            ->where('user_id', $user->id)
            ->first();

        if (! empty($embedCheckExistAccount)) {
            return [
                'result' => [
                    'error' => 1,
                    'message' => __('messages.This_User_Already_Register'),
                ],
            ];
        }
        $args['user_id'] = $user->id;
        $args['email'] = $user->email;
        $args['phone_number'] = $user->mobile;
        $args['birth_date'] = date('Y-m-d', strtotime($user->date_of_birth ?? '1991-01-01'));
        $args['family_name'] = $user->last_name;
        $args['given_name'] = $user->first_name;
        $args['marketing_checkbox'] = true;
        // random gerated password include text and numbers
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '**********';
        $password = substr(str_shuffle($chars), 0, 7).substr(str_shuffle($numbers), 0, 1);
        $args['password'] = str_shuffle($password);
        $args['title'] = $user->gender == 'male' ? 'Mr' : 'Mrs';

        // check if card is exist
        $result = (new EmbedService)->signUp($args);

        if ($result['code'] == 401) {
            $this->error = 1;
            $this->message = __('messages.RegisterNewAccountError');

            return $this->getResponse();
        }

        if ($result['code'] == 201) {
            // create new
            EmbedCard::createOrFirst(
                ['user_id' => $args['user_id']],
                [
                    'email' => $args['email'],
                    'password' => $args['password'],
                    'phone_number' => $args['phone_number'],
                    'user_id' => $args['user_id'],
                ]
            );
            $this->message = __('messages.RegisterNewAccountSuccess');

            return $this->getResponse();
        }

        // there's an error
        $this->error = 1;
        $this->message = ! empty($result['result']->message)
            ? $result['result']->message
            : __('messages.RegisterNewAccountError');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
