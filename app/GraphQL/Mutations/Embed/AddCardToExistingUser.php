<?php

namespace App\GraphQL\Mutations\Embed;

use App\Services\EMBED\EmbedService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class AddCardToExistingUser
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response = [];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $user = auth()->user();
        // check if card is exist
        $args['cvv'] = isset($args['card_cvv']) ? $args['card_cvv'] : null;
        $result = (new EmbedService)->addCard($user->id, $args);
        if ($result['code'] == 201) {
            $this->message = __('messages.AddrNewCardSuccess');

            return $this->getResponse();
        }

        // there's an error
        $this->error = 1;
        $this->message = ! empty($result['result']->message)
            ? $result['result']->message
            : __('messages.AddrNewCardError');

        return $this->getResponse();
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
