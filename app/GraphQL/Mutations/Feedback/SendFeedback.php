<?php

namespace App\GraphQL\Mutations\Feedback;

use App\Mail\FeedbackMail;
use App\Mail\SubmitFeedbackMail;
use App\Models\AppSetting\AppSetting;
use App\Models\Feedback\Feedback;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SendFeedback
{
    public $MODEL_Feedback_PREFIEX = 'App\Models\Feedback';

    public $error = 0;
    public $message = '';
    public $orderId = null;
    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $reference_number = '';
        $setting = AppSetting::first();
        $user = auth()->user();
        $args['user_id'] = $user ? $user['id'] : null;
        $args['name'] = $args['name'] ?? $user['name'];
        $args['email'] = $args['email'] ?? $user['email'];
        $args['mobile'] = $args['mobile'] ?? $user['mobile'];
        $args['reference_number'] = time();
        $this->response = [];

        if (isset($args['images'])) {
            $args['attachments'] = [];
            foreach ($args['images'] as $file) {
                if ($file['key']) {
                    //upload image with update profile
                    $path = 'feedback/';
                    $name = $file['content_type']
                        ? $file['uuid'] . '.' . explode('/', $file['content_type'])[1]
                        : $file['uuid'] . '.png';
                    $image = $path . $name;
                    Storage::disk('s3')->copy($file['key'], $image, 'public');
                    array_push($args['attachments'], \Storage::url($image));
                }
            }
        }

        if ($feedback = Feedback::create($args)) {
            if ($setting && $setting->support_email != '') {
                Mail::to($setting->support_email)->send(new FeedbackMail($feedback, $args['attachments'] ?? []));
                $title = __('messages.submitFeedbackTitle');
                $message = __('messages.submitFeedbackMessage', [
                    'name' => $args['name'],
                    'reference_number' => $feedback->reference_number,
                ]);
                Mail::to($args['email'])->send(new SubmitFeedbackMail($title, $message));
            }
            $this->message = __('messages.sendFeedbackSuccess', [
                'name' => $args['name'],
                'reference_number' => $feedback->reference_number,
            ]);
        }
        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
