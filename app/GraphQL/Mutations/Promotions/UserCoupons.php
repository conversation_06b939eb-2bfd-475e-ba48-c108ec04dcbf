<?php

namespace App\GraphQL\Mutations\Promotions;

use App\Models\Promotion\Coupon\Coupon;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UserCoupons
{
    private $valid = 0;

    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        //get bank offercoupon
        $card_digits = $args['card_digits'] ?? null;
        $user = auth()->user();

        $coupon = Coupon::Available($user)
            ->BankOffer()
            ->whereNull('brand_id')
            ->whereHas('bank_offer', function ($q2) {
                $q2->publish();
            })
            ->where(function ($q) use ($card_digits) {
                $q->whereHas('bank_offer', function ($q2) use ($card_digits) {
                    $q2->whereJsonContains('card_inits', $card_digits)->publish();
                });
            })
            ->Valid()
            ->publish()
            ->first();

        return [
            'valid' => $this->valid,
            'coupon' => $coupon,
            'result' => $this->result,
        ];
    }
}
