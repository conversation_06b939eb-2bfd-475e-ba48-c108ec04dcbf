<?php

namespace App\GraphQL\Mutations\Promotions;

use App\Helpers\OrderAmount;
use App\Helpers\Promotion\CheckCouponExist;
use App\Helpers\Promotion\ReferralValidation;
use App\Helpers\Promotion\ValidateUserCoupon;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class ValidateUserOnCoupon
{
    private $valid = 0;

    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $data = null;
        $coupon_code = $args['coupon_code'] ?? null;
        $brand_id = $args['brand_id'];
        $user = auth()->user();
        $device_uuid = \Request::header('device_uuid');
        $card_digits = $args['card_digits'] ?? null;
        $isReferral = false;
        $couponCheck = ReferralValidation::validate($coupon_code, $brand_id, $user);
        if (empty($couponCheck)) {
            $coupon = CheckCouponExist::validate($coupon_code, $user, $brand_id, $card_digits);
        } else {
            $coupon = $couponCheck['coupon'];
            $isReferral = true;
        }

        if (!empty($coupon)) {
            $validateUserCoupon = ValidateUserCoupon::validate(
                $coupon,
                $brand_id,
                $device_uuid,
                $args['order_date'] ?? date('Y-m-d'),
                $isReferral
            );

            $this->valid = $validateUserCoupon['valid'];
            if ($validateUserCoupon['valid'] == 0) {
                $this->result = $validateUserCoupon['result'];
            } else {
                $this->valid = 1;
                $this->result = ['error' => 0, 'message' => __('messages.CouponIsAvailable')];
                $brand = Brand::where('id', $args['brand_id'])->first();

                if (empty($brand)) {
                    $this->result = ['error' => 1, 'message' => __('messages.Brand_Not_Found')];
                }
                $branch = Branch::where('brand_id', $args['brand_id'])
                    ->where('id', isset($args['branch_id']) ? $args['branch_id'] : 0)
                    ->first();

                if (empty($branch)) {
                    $this->result = ['error' => 1, 'message' => __('messages.Branch_Not_Found')];
                }
                if (!empty($branch) && !empty($brand)) {
                    $data = OrderAmount::calculate($brand, $branch, $coupon, $args['orderItems']);

                    if (isset($data['couponDiscount']) && $data['couponDiscount'] == 0) {
                        $this->valid = 0;
                        $this->result = [
                            'error' => 1,
                            'message' => __('messages.OrderAmountValue', [
                                'value' => $coupon->total_order_amount,
                            ]),
                        ];
                    }
                }
            }
        } else {
            $this->result = ['error' => 1, 'message' => __('messages.CouponNotFound')];
        }
        // if ($this->result['error'] == 1) {
        //     $coupon = null;
        // }

        return [
            'valid' => $this->valid,
            'data' => $data,
            'coupon' => $coupon,
            'result' => $this->result,
        ];
    }
}