<?php

namespace App\GraphQL\Mutations\Governorate;

use App\Models\Brand\Brand;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetGovernorateByBrand
{
    private $data = [];
    private $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $brand = Brand::where('id', $args['brand_id'])
            ->with('branches')
            ->publish()
            ->first();

        if (is_null($brand)) {
            $this->result = ['error' => 1, 'message' => __('messages.Brand_Not_Found')];
        } else {
            if (count($brand['branches']) == 0) {
                $this->result = ['error' => 1, 'message' => __('messages.Branch_Not_Found_On_This_Brand')];
            }
        }

        if (!$this->result['error']) {
            $this->data = $brand->governorates();
        }

        return ['governorates' => $this->data, 'result' => $this->result];
    }
}
