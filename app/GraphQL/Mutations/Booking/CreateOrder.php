<?php

namespace App\GraphQL\Mutations\Booking;

use App\Events\OrderNotification;
use App\Events\OrderStatusUpdate;
use App\Helpers\General;
use App\Models\Ordering\Order;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class CreateOrder
{
    public $MODEL_ORDER_PREFIEX = 'App\Models\Ordering';
    public $MODEL_BOOKING_PREFIEX = 'App\Models\Booking';

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $error = 0;
        $message = '';

        $order = new Order();
        $user = Auth::user();
        $order['user_id'] = $user['id'];
        $order['name'] = $user['name'];
        $order['mobile'] = $user['mobile'];
        $order['email'] = $user['email'];
        $order['branch_id'] = $args['branch_id'];
        $order['brand_id'] = $args['brand_id'];
        $order['order_number'] = time();
        $order['order_date'] = $args['order_date'];
        $order->save();

        $storeItemsRes = $this->storeOrderItems($order, $args['orderItems']);
        $this->calculateOrderPrice($order, $storeItemsRes['totalItemsPrice']);

        // event(new OrderStatusUpdate($order));

        event(new OrderNotification($order, 4));

        return [
            'order' => $order,
            'error' => [
                'found' => $error,
                'message' => $message,
            ],
        ];
    }

    /*here we store order tickets or addons based on $args : orderItem type */
    public function storeOrderItems($order, $orderItems)
    {
        $totalItemsPrice = 0;

        foreach ($orderItems as $item) {
            $this->modelName =
                $this->MODEL_BOOKING_PREFIEX . '\\' . ucfirst($item['type']) . '\\' . ucfirst($item['type']);
            $this->modelOrderName =
                $this->MODEL_ORDER_PREFIEX . '\\' . ucfirst($item['type']) . '\\' . 'Order' . ucfirst($item['type']);
            $bookingItem = $this->modelName::where('id', $item['id'])->first();
            $orderingItem = new $this->modelOrderName();
            $orderingItem['price'] = $bookingItem['price'];
            $orderingItem['quantity'] = $bookingItem['quantity'];
            $orderingItem['total_price'] = $bookingItem['price'] * $item['quantity'];
            $totalItemsPrice += $orderingItem['total_price'];
            $orderingItem[$item['type'] . '_id'] = $item['id'];
            $orderingItem['order_id'] = $order->id;
            $orderingItem->save();
            $this->updateExtraInfo($orderingItem, $bookingItem, $item['type']);
        }

        return [
            'totalItemsPrice' => $totalItemsPrice,
        ];
    }

    public function calculateOrderPrice($order, $totalItemsPrice)
    {
        $order['price'] = $totalItemsPrice;
        $order['total_price'] = $totalItemsPrice;
        $generalHelper = new General();
        $order['status'] = $generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
        $order['payment_status'] = $generalHelper->getConstantItemByTitle('PaymentStatus', 'Waiting')['id'];
        $order->save();
    }

    public function updateExtraInfo($orderingItem, $bookingItem, $type)
    {
        $info = [];
        $info['ar']['title'] = $bookingItem->getTitleArAttribute();
        $info['en']['title'] = $bookingItem->getTitleEnAttribute();

        if ($type === 'ticket') {
            $info['ar']['sub_title'] = $bookingItem->getSubTitleArAttribute();
            $info['en']['sub_title'] = $bookingItem->getSubTitleEnAttribute();
        }

        $orderingItem->update($info);
    }
}
