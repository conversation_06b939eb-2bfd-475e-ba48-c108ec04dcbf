<?php

namespace App\GraphQL\Mutations\Booking;

use App\Helpers\General;
use App\Models\Brand\Branch;
use App\Models\Brand\WorkHour;
use App\Models\Brand\WorkingDay;
use App\Models\Ordering\Bundle\OrderBundle;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use DateInterval;
use DatePeriod;
use DateTime;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class TimeSlotByDate
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $this->generalHelper = new General;

        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        date_default_timezone_set('Asia/Riyadh');
        $slots = [];
        $slotData = [];
        $branchWorkHours = null;
        $offDates = [];
        $result = ['error' => 0, 'message' => ''];
        $branch = Branch::where('id', $args['branch_id'])->first();
        if (strtotime($args['date']) < strtotime(date('Y-m-d'))) {
            $result = ['error' => 1, 'message' => __('messages.Date_Not_Support')];

            return [
                'slots' => $slotData,
                'result' => $result,
            ];
        }

        if (is_null($branch)) {
            $result = ['error' => 1, 'message' => __('messages.Branch_Not_Found')];

            return [
                'slots' => $slotData,
                'result' => $result,
            ];
        } else {
            // slots
            $checkDateExist = WorkHour::where('branch_id', $branch->id)
                ->where('date', $args['date'])
                ->where('type', 0)
                ->where('day_id', 0)
                ->whereNull('from_time')
                ->whereNull('to_time')
                ->first();

            if (! empty($checkDateExist)) {
                $result = ['error' => 1, 'message' => __('messages.Date_Closed')];

                return [
                    'slots' => $slotData,
                    'result' => $result,
                ];
            }
            $day_id = null;
            $day = date('l', strtotime($args['date']));
            $constant = $this->generalHelper->getConstantItemByTitle('Days', $day);

            if ($constant) {
                $day_id = $constant['id'];
                $brandWork = WorkingDay::where('brand_id', $branch->brand_id)
                    ->where('day_id', $day_id)
                    ->pluck('day_id')
                    ->toArray();
                $branchWorkHours = $branch
                    ->branchWorkHours()
                    ->whereIn('day_id', $brandWork)
                    ->where('day_id', $day_id)
                    ->orderBy('from_time', 'asc')
                    ->get();

                if ($branchWorkHours) {
                    foreach ($branchWorkHours as $workHourBranch) {
                        $startTime = $workHourBranch->from_time;
                        $toTime = $workHourBranch->to_time;
                        // $toTime = date('h:i A', strtotime($workHourBranch->to_time . '+ 1 hours'));
                        $period = new DatePeriod(
                            new DateTime($startTime),
                            new DateInterval('PT1H'),
                            new DateTime($toTime)
                        );
                        foreach ($period as $date) {
                            array_push($slots, $date->format('H:i'));
                        }

                        if (strtotime($args['date']) == strtotime(date('Y-m-d'))) {
                            // remove time
                            $startTime = $workHourBranch->from_time;

                            $toTime = date('h:i A', strtotime('- 0 hours'));
                            $period = new DatePeriod(
                                new DateTime($startTime),
                                new DateInterval('PT1H'),
                                new DateTime($toTime)
                            );
                            foreach ($period as $date) {
                                array_push($offDates, $date->format('H:i'));
                            }
                        }
                        // get off time
                        $branchOffHour = $branch
                            ->offDates()
                            ->where('date', date('Y-m-d', strtotime($args['date'])))
                            ->get();
                        if ($branchOffHour) {
                            foreach ($branchOffHour as $offDate) {
                                $startTime = $offDate->from_time;
                                $toTime = $offDate->to_time;
                                $period = new DatePeriod(
                                    new DateTime($startTime),
                                    new DateInterval('PT1H'),
                                    new DateTime($toTime)
                                );
                                foreach ($period as $date) {
                                    array_push($offDates, $date->format('H:i'));
                                }
                            }
                        }
                        $slots = array_diff($slots, $offDates);
                    }
                }
            }
        }

        if (count($slots)) {
            foreach ($slots as $slot) {
                // $time = date('h:i:s', strtotime(date('Y-m-d') . ' ' . $slot));
                // get no of orders that have ticket by time and date
                $orders = Order::whereHas('tickets')
                    ->where('branch_id', $args['branch_id'])
                    ->where('order_time', $slot.':00')
                    ->whereDate('order_date', date('Y-m-d', strtotime($args['date'])))
                    ->has('bundles', '<', 1)
                    ->has('tickets', '>', 0)
                    ->pluck('id')
                    ->toArray();
                $ids = OrderTicket::whereIn('order_id', $orders)->count();

                $bundles_orders = Order::whereHas('bundles')
                    ->where('branch_id', $args['branch_id'])
                    ->where('order_time', $slot.':00')
                    ->whereDate('order_date', date('Y-m-d', strtotime($args['date'])))
                    ->has('bundles', '>', 0)
                    ->pluck('id')
                    ->toArray();
                $bundles_ids = OrderBundle::whereIn('order_id', $bundles_orders)->count();
                if (
                    $branchWorkHours[0]->capacity - $ids > 0 ||
                    $branchWorkHours[0]->bundle_capacity - $bundles_ids > 0
                ) {
                    $item = [
                        'time' => (string) $slot,
                        'bundle_capacity' => $branchWorkHours[0]->bundle_capacity - $bundles_ids,
                        'capacity' => $branchWorkHours[0]->capacity - $ids,
                    ];
                    array_push($slotData, $item);
                }
            }
        }

        if (count($slotData) < 1) {
            $result = ['error' => 1, 'message' => __('messages.No_Time_Available')];
        }

        return [
            'slots' => $slotData,
            'result' => $result,
        ];
    }
}
