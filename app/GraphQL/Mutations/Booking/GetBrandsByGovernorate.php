<?php

namespace App\GraphQL\Mutations\Booking;

use App\Models\Governorate\Governorate;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetBrandsByGovernorate
{
    protected $data = [];

    protected $result = ['error' => 0, 'message' => ''];

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $governorate = Governorate::where('id', $args['governorate_id'])
            ->with('branches')
            ->whereHas('branches')
            ->publish()
            ->first();

        if (is_null($governorate)) {
            $this->result = ['error' => 1, 'message' => __('messages.Governorate_Not_Found')];
        }

        if (! $this->result['error']) {
            $this->data = $governorate->brands;
        }

        return ['brands' => $this->data, 'error' => $this->result];
    }
}
