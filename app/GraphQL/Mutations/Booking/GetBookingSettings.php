<?php

namespace App\GraphQL\Mutations\Booking;

use App\Models\Brand\Brand;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class GetBookingSettings
{
    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $returnBookingSteps = [];
        $workingDaysReturn = [];
        $result = ['error' => 0, 'message' => ''];
        $brand = Brand::where('id', $args['brand_id'])->first();

        if (is_null($brand)) {
            $result = ['error' => 1, 'message' => __('messages.Brand_Not_Found')];
        } else {
            $workingDaysReturn = $brand->workingDays()->get();
        }

        return [
            'brand' => $brand,
            'addons' => $brand
                ->addons()
                ->orderBy('position', 'asc')
                ->get(),
            'tickets' => $brand
                ->tickets()
                ->orderBy('position', 'asc')
                ->get(),
            'workingDays' => $workingDaysReturn,
            'result' => $result,
        ];
    }
}