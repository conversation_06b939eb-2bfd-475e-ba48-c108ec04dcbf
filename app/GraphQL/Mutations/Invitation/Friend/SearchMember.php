<?php

namespace App\GraphQL\Mutations\Invitation\Friend;

use App\Models\Invitation\Friend;
use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class SearchMember
{
    public $error = 0;

    public $user = null;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $this->response = [];

        $authUser = auth()->user();

        $userCheck = User::where(function ($q) use ($args) {
            if ($args['searchBy'] == 'email') {
                $q->where('email', $args['search']);
            } elseif ($args['searchBy'] == 'mobile') {
                $q->where('mobile', 'like', '%'.$args['search'].'%');
            }
        })->first();

        if (empty($userCheck)) {
            $message = __('messages.UserNotFound');
            $error = 1;

            return [
                'user' => $this->user,
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }

        $friendCheck = Friend::CheckFriend($authUser->id, $userCheck->id)->where('status', '!=', '2')->first();

        $this->user = [
            'exist' => $userCheck ? true : false,
            'name' => ! $friendCheck && $userCheck
                    ? getStarred($userCheck->first_name).' '.getStarred($userCheck->last_name)
                    : '',
            'joinBefore' => $friendCheck ? true : false,
        ];

        if ($friendCheck) {
            $message = __('messages.MemberAlreadyExist');
            $error = 1;

            return [
                'user' => $this->user,
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }

        $this->message = __('messages.AvailableToAdd');

        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'user' => $this->user,
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
