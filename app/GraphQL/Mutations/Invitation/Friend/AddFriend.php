<?php

namespace App\GraphQL\Mutations\Invitation\Friend;

use App\Events\InvitationNotification;
use App\Models\Invitation\Friend;
use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class AddFriend
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $this->response = [];
        $sender = auth()->user();

        // check if invited user (receiver) exists
        $receiver = User::where(function ($q) use ($args) {
            if ($args['inviteBy'] == 'email') {
                $q->where('email', $args['member']);
            } elseif ($args['inviteBy'] == 'mobile') {
                $q->where('mobile', $args['member']);
            }
        })->first();

        // case invited user (receiver) exists
        if (! empty($receiver)) {
            // check if user trying to add himself
            if ($sender->id == $receiver->id) {
                $message = __('messages.YouCanNotAddYourSelf');
                $error = 1;

                return [
                    'result' => [
                        'error' => $error,
                        'message' => $message,
                    ],
                ];
            }
            // check if there is an invitation already
            if ($existingFriendRequest = Friend::CheckFriend($sender->id, $receiver->id)->first()) {
                if ($existingFriendRequest->status != 2) {
                    $message = __('messages.MemberAlreadyExist');
                    $error = 1;

                    return [
                        'result' => [
                            'error' => $error,
                            'message' => $message,
                        ],
                    ];
                } else {
                    // it means $existingFriendRequest->status = 2 "rejected"
                    // so it will be updated to 0 "pending" and resend notification
                    // or create new record if the receiver (who rejected) is now the sender
                    $existingFriendRequest = Friend::updateOrCreate(
                        [
                            'sender_id' => $sender->id,
                            'user_id' => $receiver->id,
                        ],
                        [
                            'email' => $args['inviteBy'] == 'email' ? $args['member'] : null,
                            'mobile' => $args['inviteBy'] == 'mobile' ? $args['member'] : null,
                            'invite_by' => $args['inviteBy'],
                            'status' => 0,
                            'family_relation_id' => $args['relation_id'],
                        ]
                    );
                    event(new InvitationNotification($receiver->id, $existingFriendRequest->id, 0));
                }
            } else {
                $existingUserInvitation = Friend::create([
                    'user_id' => $receiver->id,
                    'sender_id' => $sender->id,
                    'email' => $args['inviteBy'] == 'email' ? $args['member'] : null,
                    'mobile' => $args['inviteBy'] == 'mobile' ? $args['member'] : null,
                    'invite_by' => $args['inviteBy'],
                    'status' => 0,
                    'family_relation_id' => $args['relation_id'],
                ]);
                event(new InvitationNotification($receiver->id, $existingUserInvitation->id, 0));
            }
        } else {
            // create friend invitation by email/mobile if invited user doesn't exit
            // to be updated later when invited user register
            $nonExistingUserInvitation = Friend::create([
                'user_id' => null,
                'sender_id' => $sender->id,
                'email' => $receiver->email ?? ($args['inviteBy'] == 'email' ? $args['member'] : null),
                'mobile' => $receiver->mobile ?? ($args['inviteBy'] == 'mobile' ? $args['member'] : null),
                'invite_by' => $args['inviteBy'],
                'status' => 0,
                'family_relation_id' => $args['relation_id'],
            ]);
        }

        $this->message = __('messages.MemberAddedToFriend');

        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
