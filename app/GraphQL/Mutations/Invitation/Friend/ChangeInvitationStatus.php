<?php

namespace App\GraphQL\Mutations\Invitation\Friend;

use App\Events\InvitationNotification;
use App\Models\Invitation\Friend;
use App\Models\Notification\Notification;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class ChangeInvitationStatus
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $this->response = [];
        $user = auth()->user();

        $checkExist = Friend::where('user_id', $user->id)
            ->where('id', $args['inviatation_id'])
            ->where('status', 0)
            ->first();

        if (empty($checkExist)) {
            $message = __('messages.MemberDoenotExist');
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }

        if ($args['status'] == 0) {
            $this->message = __('messages.MemberRejectFriendInvitation');
            $checkExist->update(['status' => 2, 'rejected_at' => now()]);
        } elseif ($args['status'] == 1) {
            $this->message = __('messages.MemberAcceptFriendInvitation');
            $checkExist->update([
                'status' => 1,
                'accepted_at' => now(),
            ]);
        }

        Notification::Invitation()
            ->where('user_id', $checkExist->user_id)
            ->whereJsonContains('data', ['id' => $checkExist->id])
            ->delete();

        event(new InvitationNotification($checkExist->sender_id, $checkExist->id, $checkExist->status));

        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
