<?php

namespace App\GraphQL\Mutations\Invitation\Friend;

use App\Models\Invitation\Friend;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class RemoveFriend
{
    public $error = 0;

    public $message = '';

    public $orderId = null;

    public $response;

    public function __invoke($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->__constructor($args);
    }

    protected function __constructor($args)
    {
        $this->error = 0;
        $this->message = '';
        $this->response = [];
        $user = auth()->user();

        $checkExist = Friend::where('id', $args['friend_id'])->first();

        if (empty($checkExist)) {
            $message = __('messages.MemberDoenotExist');
            $error = 1;

            return [
                'result' => [
                    'error' => $error,
                    'message' => $message,
                ],
            ];
        }

        $checkExist->delete();
        $this->message = __('messages.MemberRemovedFromFriend');

        $this->response = $this->getResponse();

        return $this->response;
    }

    public function getResponse()
    {
        $res = [
            'result' => [
                'error' => $this->error,
                'message' => $this->message,
            ],
        ];

        return $res;
    }
}
