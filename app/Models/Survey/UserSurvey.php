<?php

namespace App\Models\Survey;

use App\Models\Ordering\Order;
use App\Models\Survey\UserSurveyAnswer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserSurvey extends Model
{
    use HasFactory;

    protected $fillable = ['order_date', 'order_id', 'user_id', 'is_finished'];
    protected $appends = ['date', 'no_questions'];
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function answers()
    {
        return $this->hasMany(UserSurveyAnswer::class)->with('survey');
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('id', 'desc');
    }

    public function getDateAttribute()
    {
        return $this->created_at
            ? \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('m/d/Y')
            : '';
    }
    public function getNoQuestionsAttribute()
    {
        return count($this->answers);
    }
}