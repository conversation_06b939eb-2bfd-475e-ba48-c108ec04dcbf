<?php

namespace App\Models\Survey\Survey;

use App\Models\Booking\Category\Category;
use App\Models\Brand\Branch;
use App\Models\Brand\Brand;
use App\Models\Model;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Survey extends Model
{
    use HasFactory, Translatable, SoftDeletes;
    public $appends = ['date', 'updated', 'title_en', 'title_ar', 'branch_ids'];
    public $translatedAttributes = ['title'];

    protected $fillable = ['active', 'position', 'brand_id', 'ticket_id', 'category_id'];

    public function getTitleArAttribute()
    {
        return $this->translate('ar')->title ?? '';
    }

    public function getTitleEnAttribute()
    {
        return $this->translate('en')->title ?? '';
    }

    public function delete()
    {
        $this->translations()->delete();

        return parent::delete();
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function branches()
    {
        return $this->hasMany(SurveyBranch::class)->with('branch');
    }

    public function getBranchIdsAttribute()
    {
        return SurveyBranch::where('survey_id', $this->id)
            ->pluck('branch_id')
            ->toArray();
    }
}