<?php

namespace App\Models\Question;

use App\Models\Model;
use Spatie\Activitylog\LogOptions;
use Astrotomic\Translatable\Translatable;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Question extends Model
{
    use HasFactory, Translatable, SoftDeletes, LogsActivity;
    public $appends = ['date', 'updated', 'title_en', 'title_ar', 'description_en', 'description_ar'];
    public $translatedAttributes = ['title', 'description'];

    protected $fillable = ['active', 'position', 'type'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['active', 'position', 'type'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function scopeFaq($query)
    {
        return $query->where('type', 0);
    }

    public function scopeReferral($query)
    {
        return $query->where('type', 1);
    }

    public function getTitleArAttribute()
    {
        return $this->translate('ar')->title ?? '';
    }

    public function getTitleEnAttribute()
    {
        return $this->translate('en')->title ?? '';
    }

    public function getDescriptionArAttribute()
    {
        return $this->translate('ar')->description ?? '';
    }

    public function getDescriptionEnAttribute()
    {
        return $this->translate('en')->description ?? '';
    }

    public function delete()
    {
        $this->translations()->delete();

        return parent::delete();
    }
}