<?php

namespace App\Models\AppSetting\Region;

use App\Models\Model;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Region extends Model
{
    use HasFactory, Translatable;
    public $appends = ['title_en', 'title_ar'];
    public $translatedAttributes = ['title'];

    protected $fillable = ['phone'];

    public function getTitleArAttribute()
    {
        return $this->translate('ar')->title ?? '';
    }

    public function getTitleEnAttribute()
    {
        return $this->translate('en')->title ?? '';
    }

    public function delete()
    {
        $this->translations()->delete();
        return parent::delete();
    }
}