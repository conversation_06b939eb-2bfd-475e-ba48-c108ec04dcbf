<?php

namespace App\Models\Booking\Addon;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class BirthdaySetting extends Model
{
    use HasFactory, LogsActivity;
    //type 0--> off day and 1->> on day
    public $fillable = ['branch_id', 'day_id', 'from_time', 'to_time', 'break_duration', 'type'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['branch.id', 'day_id', 'from_time', 'to_time', 'break_duration'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function branch()
    {
        return $this->belongsTo(\App\Models\Brand\Branch::class);
    }
}