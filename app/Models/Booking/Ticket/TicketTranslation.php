<?php

namespace App\Models\Booking\Ticket;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;

class TicketTranslation extends Model
{
    use HasFactory;
    protected $fillable = ['description', 'title', 'sub_title'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['description', 'title', 'sub_title'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
