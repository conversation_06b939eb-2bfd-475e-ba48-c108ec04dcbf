<?php

namespace App\Models\Booking\Ticket;

use App\Models\Brand\Branch;
use App\Models\Governorate\Governorate;
use App\Models\Model;
use Astrotomic\Translatable\Translatable;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Ticket extends Model
{
    use HasFactory;
    use LogsActivity;
    use SoftDeletes;
    use Translatable;

    public $appends = [
        'branch_ids',
        'date',
        'updated',
        'title_en',
        'title_ar',
        'description_ar',
        'description_en',
        'image_path',
        'sub_title_en',
        'sub_title_ar',
        'created_date',
        'locations',
        'governorate',
    ];

    public $translatedAttributes = ['description', 'title', 'sub_title'];

    protected $fillable = [
        'active',
        'position',
        'price',
        'price_before',
        'brand_id',
        'discount',
        'have_discount',
        'no_coupon',
        'image',
        'enable_extra_info',
        'color',
        'ticket_type', // 0->normal ticket //1->Special ticket
        'can_request_group',
        'min_number',
        'started_at',
        'expired_at',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'active',
                'position',
                'price',
                'brand.id',
                'discount',
                'image',
                'enable_extra_info',
                'color',
                'ticket_type', // 0->normal ticket //1->Special ticket // 2-bulk ticket
                'can_request_group',
                'min_number',
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function scopeNormal($query)
    {
        return $query->where('ticket_type', 0);
    }

    public function scopeSpecial($query)
    {
        return $query->where('ticket_type', 1);
    }

    public function scopeBulk($query)
    {
        return $query->where('ticket_type', 2); // BulkTicket
    }

    public function scopeBirthday($query)
    {
        return $query->where('ticket_type', 3); // Birthday
    }

    public function getDescriptionArAttribute()
    {
        return $this->translate('ar')->description ?? '';
    }

    public function getDescriptionEnAttribute()
    {
        return $this->translate('en')->description ?? '';
    }

    public function brand()
    {
        return $this->belongsTo(\App\Models\Brand\Brand::class);
    }

    public function getImagePathAttribute()
    {
        return $this->image ? \Storage::url($this->image) : '';
    }

    public function getTitleArAttribute()
    {
        return $this->translate('ar')->title ?? '';
    }

    public function getTitleEnAttribute()
    {
        return $this->translate('en')->title ?? '';
    }

    public function getSubTitleArAttribute()
    {
        return $this->translate('ar')->sub_title ?? '';
    }

    public function getSubTitleEnAttribute()
    {
        return $this->translate('en')->sub_title ?? '';
    }

    public function delete()
    {
        $this->translations()->delete();

        return parent::delete();
    }

    public function branches()
    {
        return $this->hasMany(TicketBranch::class)->with('branch');
    }

    public function calendars()
    {
        return $this->hasMany(TicketCalendar::class, 'ticket_id', 'id');
    }

    public function getBranchIdsAttribute()
    {
        return TicketBranch::where('ticket_id', $this->id)
            ->pluck('branch_id')
            ->toArray();
    }

    public function extraInfo()
    {
        return $this->hasMany(TicketExtraInfo::class);
    }

    public function bundles()
    {
        return $this->hasMany(\App\Models\Promotion\Bundle\BundleTicket::class)->with('bundle');
    }

    public function getExtraInfo(self $ticket, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        if ($ticket->enable_extra_info === 1) {
            return $ticket->extraInfo;
        } else {
            return [];
        }
    }

    public function getCreatedDateAttribute()
    {
        $value = $this->created_at;

        return $value != null ? date('m/d/Y', strtotime($value)) : '';
    }

    public function getLocationsAttribute()
    {
        $branches = '';

        $branches = Branch::whereIn('id', $this->branch_ids)
            ->get()
            ->pluck('title_en')
            ->toArray();
        $branches = implode(' '.PHP_EOL, $branches);

        return $branches;
    }

    public function getTicketLocationsAttribute()
    {
        $branches = '';
        $ids = explode('/', parse_url(\Request::url())['path']);
        $ids = end($ids);

        $branches = Branch::whereIn('id', explode(',', $ids))
            ->whereIn('id', $this->branch_ids)
            ->get()
            ->pluck('title_en')
            ->toArray();
        $branches = implode(' - ', $branches);

        return '('.$branches.')';
    }

    public function getGovernorateAttribute()
    {
        $branches = '';
        $branches = Branch::whereIn('id', $this->branch_ids)
            ->pluck('governorate_id')
            ->toArray();

        $governorate = Governorate::whereIn('id', $branches)
            ->get()
            ->pluck('title_en')
            ->toArray();
        $governorate = implode(' '.PHP_EOL, $governorate);

        return $governorate;
    }

    public function getTicketTypeTextAttribute()
    {
        $value = '';
        if ($this->ticket_type == 0) {
            $value = __('Normal');
        } elseif ($this->ticket_type == 1) {
            $value = __('Special');
        } elseif ($this->ticket_type == 2) {
            $value = __('BulkTicket');
        } elseif ($this->ticket_type == 3) {
            $value = __('Birthday');
        }

        return $value;
    }
}
