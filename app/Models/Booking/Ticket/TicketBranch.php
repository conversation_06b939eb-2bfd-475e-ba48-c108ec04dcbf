<?php

namespace App\Models\Booking\Ticket;

use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TicketBranch extends Model
{
    use HasFactory, LogsActivity;
    protected $fillable = ['ticket_id', 'branch_id', 'brand_id'];
    protected static $recordEvents = ['created', 'deleted'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logOnly(['ticket_id', 'branch_id', 'brand_id']);
    }

    public function branch()
    {
        return $this->belongsTo(\App\Models\Brand\Branch::class);
    }
}