<?php

namespace App\Models\Booking\Ticket;

use App\Models\Ordering\Order;
use Spatie\Activitylog\LogOptions;
use App\Models\Booking\Ticket\Ticket;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TicketCalendar extends Model
{
    use HasFactory, LogsActivity;

    public $fillable = ['ticket_id', 'date', 'start', 'end', 'date_type'];

    public $appends = ['formate_date', 'no_orders'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['ticket.id', 'date', 'start', 'end', 'date_type'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function ticket()
    {
        return $this->belongsTo(Ticket::class);
    }

    public function getFormateDateAttribute()
    {
        return date('Y-n-j', strtotime($this->date));
    }

    public function getNoOrdersAttribute()
    {
        $ids = TicketBranch::where('ticket_id', $this->ticket_id)
            ->pluck('branch_id')
            ->toArray();
        //check if date have orders already exist out dates

        return Order::whereDate('order_date', date('Y-m-d', strtotime($this->date)))
            ->whereIn('branch_id', $ids)
            ->count();
    }
}