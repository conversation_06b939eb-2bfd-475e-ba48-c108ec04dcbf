<?php

namespace App\Models\Promotion\Coupon;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CouponBranch extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $fillable = ['coupon_id', 'branch_id', 'brand_id'];

    protected static $recordEvents = ['created', 'deleted'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logOnly(['coupon_id', 'branch_id', 'brand_id']);
    }

    public function branch()
    {
        return $this->belongsTo(\App\Models\Brand\Branch::class);
    }
}
