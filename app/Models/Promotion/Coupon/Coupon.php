<?php

namespace App\Models\Promotion\Coupon;

use App\Helpers\Constant;
use App\Models\Model;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Coupon extends Model
{
    use HasFactory;
    use LogsActivity;
    use Translatable;

    public $appends = [
        'date',
        'updated',
        'started',
        'image_en',
        'image_ar',
        'expired',
        'type_span',
        'title_en',
        'title_ar',
        'description_ar',
        'description_en',
        'no_used',
        'used_span',
    ];

    public $fillable = [
        'brand_id',
        'order_id',
        'user_id',
        'position',
        'started_at',
        'expired_at',
        'no_active_days',
        'validate_type', // days = 0 or no_active_days = 1
        'active',
        'type',
        'discount',
        'maximum_usage',
        'code',
        'bulk_coupon_id',
        'is_bulk',
        'max_value_discount',
        'total_order_amount',
        'condition',
        'coupon_type', // 0 => normal ,1 => register,invitation
        'notify_type', // 1 send notification only & 0 send notification with coupon
        'notify_days', // no of days before sending notification
        'group_id',
        'bank_offer_id',
    ];

    public $translatedAttributes = ['image', 'title', 'description'];

    public $with = ['brand'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'brand.id',
                'position',
                'started_at',
                'expired_at',
                'active',
                'type',
                'discount',
                'maximum_usage',
                'code',
                'bulk_coupon_id',
                'is_bulk',
                'no_active_days',
                'group_id',
                'bank_offer_id',
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function brand()
    {
        return $this->belongsTo(\App\Models\Brand\Brand::class);
    }

    public function group()
    {
        return $this->belongsTo(\App\Models\Group\Group::class);
    }

    public function custom_group()
    {
        return $this->belongsTo(\App\Models\CustomGroup\CustomGroup::class, 'group_id');
    }

    public function bank_offer()
    {
        return $this->belongsTo(\App\Models\BankOffer\BankOffer::class, 'bank_offer_id');
    }

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class)->select('first_name', 'last_name', 'id', 'name');
    }

    public function getImageArAttribute()
    {
        return $this->translate('ar')->image_path ?? '';
    }

    public function getImageEnAttribute()
    {
        return $this->translate('en')->image_path ?? '';
    }

    public function getNoUsedAttribute()
    {
        return \App\Models\Ordering\Order::where('coupon_id', $this->id)->count();
    }

    public function scopeBulk($query)
    {
        return $query->whereNotNull('bulk_coupon_id');
    }

    public function scopeSingle($query)
    {
        return $query->where('is_bulk', 0)->where('coupon_type', 0);
    }

    public function scopeOffer($query)
    {
        return $query->where('is_bulk', 0)->where('coupon_type', '>', 0);
    }

    public function scopeRegister($query)
    {
        return $query->where('coupon_type', Constant::RegisterCouponType);
    }

    public function scopeBirthDay($query)
    {
        return $query->where('coupon_type', Constant::BirthDateCouponType);
    }

    public function scopeOrder($query)
    {
        return $query->where('coupon_type', Constant::CouponOrderType);
    }

    public function scopeOneTime($query)
    {
        return $query->where('coupon_type', Constant::CouponOneTimeType);
    }

    public function scopeBankOffer($query)
    {
        return $query->where('coupon_type', Constant::CouponBankOfferType);
    }

    public function scopeReferral($query)
    {
        return $query->where('coupon_type', Constant::ReferralCouponType);
    }

    public function scopeUserType($query)
    {
        return $query->whereNotNull('user_id');
    }

    public function scopeNotUser($query)
    {
        return $query->whereNull('user_id');
    }

    public function scopeGroupType($query)
    {
        return $query->where('coupon_type', Constant::GroupCouponType);
    }

    public function scopeUnlimitedUsage($query)
    {
        return $query->where('maximum_usage', '-1');
    }

    public function scopeValid($query)
    {
        return $query
            ->where(function ($q) {
                $q->whereDate('expired_at', '>=', now());
                $q->whereDate('started_at', '<=', now());
            })
            ->orWhere(function ($q) {
                $q->whereNull('expired_at');
                $q->whereNull('started_at');
            });
    }

    public function scopeAvailable($query, $user)
    {
        $orders = \App\Models\Ordering\Order::where('coupon_id', $this->id)
            ->where('user_id', $user)
            ->count();

        return $query->where('maximum_usage', '<', 0)->orWhere('maximum_usage', '>', $orders);
    }

    public function getImagePathAttribute()
    {
        return app()->getLocale() == 'ar' ? $this->image_ar : $this->image_en;
    }

    public function getExpiredAttribute()
    {
        return $this->expired_at ? date('m/d/Y', strtotime($this->expired_at)) : '';
    }

    public function getDescriptionArAttribute()
    {
        return $this->translate('ar')->description ?? '';
    }

    public function getDescriptionEnAttribute()
    {
        return $this->translate('en')->description ?? '';
    }

    public function getTitleArAttribute()
    {
        return $this->translate('ar')->title ?? '';
    }

    public function getTitleEnAttribute()
    {
        return $this->translate('en')->title ?? '';
    }

    public function getStartedAttribute()
    {
        return $this->started_at ? date('m/d/Y', strtotime($this->started_at)) : '';
    }

    public function getTypeSpanAttribute()
    {
        if ($this->type == 0) {
            return "<span class='badge badge-pill badge-warning'>".__('Percent').'</span>';
        } elseif ($this->type == 1) {
            return "<span class='badge badge-pill badge-info'>".__('Value').'</span>';
        }
    }

    public function getUsedSpanAttribute()
    {
        if (
            \App\Models\Ordering\Order::where('coupon_id', $this->id)
                ->where('payment_status', Constant::PaidOrder)
                ->count() > 0
        ) {
            return "<span class='badge badge-pill badge-success'>".__('Used').'</span>';
        }

        return "<span class='badge badge-pill badge-default'>".__('NotUsed').'</span>';
    }

    public function getBranchIdsAttribute()
    {
        return CouponBranch::where('coupon_id', $this->id)
            ->pluck('branch_id')
            ->toArray();
    }

    public function branches()
    {
        return $this->hasMany(CouponBranch::class)->with('branch');
    }
}
