<?php

namespace App\Models\Promotion\Coupon;

use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BulkCoupon extends Model
{
    use HasFactory, LogsActivity;
    public $fillable = ['no_coupons'];
    // public $with = ['coupons'];
    public $appends = ['first_coupons'];

    public function scopeRecent($query)
    {
        return $query->orderBy('id', 'desc');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['no_coupons'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }
    public function getFirstCouponsAttribute()
    {
        return $this->coupons()->first();
    }
}