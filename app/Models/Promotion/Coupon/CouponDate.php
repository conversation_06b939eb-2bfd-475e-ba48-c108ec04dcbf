<?php

namespace App\Models\Promotion\Coupon;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CouponDate extends Model
{
    use HasFactory;
    public $fillable = ['coupon_id', 'date', 'day_id'];
    public $appends = ['formate_date'];

    public function coupon()
    {
        return $this->belongsTo(coupon::class);
    }

    public function getFormateDateAttribute()
    {
        return date('Y-n-j', strtotime($this->date));
    }
}