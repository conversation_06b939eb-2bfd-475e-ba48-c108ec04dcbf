<?php

namespace App\Models\Brand;

use App\Helpers\Constant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class WorkingDay extends Model
{
    use HasFactory, LogsActivity;
    public $appends = ['title'];
    public $fillable = ['brand_id', 'day_id'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['brand_id', 'day_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function brand()
    {
        return $this->belongsTo(\App\Models\Brand\Brand::class);
    }

    public function getTitleAttribute()
    {
        $index = array_search($this->day_id, array_column(Constant::Days, 'id'));

        return app()->getLocale() == 'ar' ? Constant::Days[$index]['title_ar'] : Constant::Days[$index]['title_en'];
    }
}
