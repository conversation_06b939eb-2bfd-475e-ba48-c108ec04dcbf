<?php

namespace App\Models\Brand;

use App\Helpers\Constant;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class BranchOffDay extends Model
{
    use HasFactory, LogsActivity;
    public $fillable = ['branch_id', 'day_id'];
    public $appends = ['title_en', 'title_ar'];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['branch.id', 'day_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function resolveTitleArFromConstants(
        self $day,
        array $args,
        GraphQLContext $context,
        ResolveInfo $resolveInfo
    ) {
        $index = array_search($day->day_id, array_column(Constant::Days, 'id'));

        return Constant::Days[$index]['title_ar'];
    }

    public function resolveTitleEnFromConstants(
        self $day,
        array $args,
        GraphQLContext $context,
        ResolveInfo $resolveInfo
    ) {
        $index = array_search($day->day_id, array_column(Constant::Days, 'id'));

        return Constant::Days[$index]['title_en'];
    }
}
