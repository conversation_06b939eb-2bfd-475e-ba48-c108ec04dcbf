<?php

namespace App\Models\Brand;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class WorkHour extends Model
{
    use HasFactory, LogsActivity;
    //type 0--> off day and 1->> on day
    public $fillable = ['branch_id', 'day_id', 'date', 'from_time', 'to_time', 'capacity', 'type', 'bundle_capacity'];

    public $appends = ['formate_date'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['branch.id', 'day_id', 'date', 'from_time', 'to_time', 'capacity', 'type', 'bundle_capacity'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function getFormateDateAttribute()
    {
        return date('Y-n-j', strtotime($this->date));
    }

    //OffDates
    public function scopeOffDates($query)
    {
        return $query
            ->where('type', 0)
            ->whereNotNull('date')
            ->whereDate('date', '>=', date('Y-m-d'));
    }

    //WorkHours
    public function scopeWorkingHours($query)
    {
        return $query->where('type', 1)->whereNotNull('day_id');
    }
}
