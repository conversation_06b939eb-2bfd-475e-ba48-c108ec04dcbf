<?php

namespace App\Models\Brand;

use App\Helpers\Constant;
use App\Helpers\DateRange;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Booking\Ticket\TicketBranch;
use App\Models\Model;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Branch extends Model
{
    use Translatable, HasFactory, SoftDeletes, LogsActivity;

    public $appends = [
        'all_off_dates',
        'date',
        'updated',
        'title_en',
        'title_ar',
        'image_en',
        'image_ar',
        'images',
        'OffDays',
        'workHours',
        'working_hour_en',
        'working_hour_ar',
    ];

    public $fillable = [
        'brand_id',
        'governorate_id',
        'position',
        'active',
        'latitude',
        'longitude',
        'foodics_branch_id',
        'no_day_to_reschedule',
        'foodics_group_id',
        'ramadan',
    ];

    public $translatedAttributes = ['title', 'working_hour'];
    // public $with = ['brand'];
    protected $casts = [
        'ramadan' => 'array',
    ];
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'brand.id',
                'governorate.id',
                'position',
                'active',
                'latitude',
                'longitude',
                'foodics_branch_id',
                'no_day_to_reschedule',
                'foodics_group_id',
                'ramadan',
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function brand()
    {
        return $this->belongsTo(\App\Models\Brand\Brand::class);
    }

    public function governorate()
    {
        return $this->belongsTo(\App\Models\Governorate\Governorate::class);
    }

    public function getTicketsAttribute()
    {
        $ids = TicketBranch::where('branch_id', $this->id)

            ->pluck('ticket_id')
            ->toArray();

        return Ticket::whereIn('id', $ids)
            ->normal()
            ->orderBy('position', 'asc')
            ->get();
    }

    public function getSpecialTicketsAttribute()
    {
        $ids = TicketBranch::where('branch_id', $this->id)

            ->pluck('ticket_id')
            ->toArray();

        return Ticket::whereIn('id', $ids)
            ->special()
            ->orderBy('position', 'asc')
            ->get();
    }

    public function getTitleArAttribute()
    {
        return $this->translate('ar')->title ?? '';
    }

    public function getTitleEnAttribute()
    {
        return $this->translate('en')->title ?? '';
    }

    public function branchWorkHours()
    {
        return $this->hasMany(WorkHour::class)->WorkingHours();
    }

    public function getWorkingHourArAttribute()
    {
        return $this->translate('ar')->working_hour ?? '';
    }

    public function getWorkingHourEnAttribute()
    {
        return $this->translate('en')->working_hour ?? '';
    }

    public function getWorkHoursAttribute()
    {
        $brandWorkingDay = WorkingDay::where('brand_id', $this->brand_id)
            ->pluck('day_id')
            ->toArray();

        return WorkHour::WorkingHours()
            ->where('branch_id', $this->id)
            ->whereIn('day_id', $brandWorkingDay)
            ->get();
    }

    public function getOffDaysAttribute()
    {
        $items = [];
        $daysId = [1, 2, 3, 4, 5, 6, 7];
        $brandWorkingDay = WorkingDay::where('brand_id', $this->brand_id)
            ->pluck('day_id')
            ->toArray();
        $branchOffDay = WorkHour::WorkingHours()
            ->where('branch_id', $this->id)
            ->whereIn('day_id', $brandWorkingDay)
            ->pluck('day_id')
            ->toArray();

        $disableWorkingDay = array_diff($daysId, $branchOffDay);
        if (count($disableWorkingDay)) {
            foreach ($disableWorkingDay as $disabledDay) {
                $index = array_search($disabledDay, array_column(Constant::Days, 'id'));
                $item = [
                    'day_id' => $disabledDay,
                    'title_ar' => Constant::Days[$index]['title_ar'],
                    'title_en' => Constant::Days[$index]['title_en'],
                ];
                array_push($items, (object) $item);
            }
        }

        return $items;

        // return $this->hasMany(BranchOffDay::class);
    }

    public function offDates()
    {
        return $this->hasMany(WorkHour::class)->OffDates();
    }

    public function offDatesOnly()
    {
        return $this->hasMany(WorkHour::class)
            ->OffDates()
            ->whereNull('from_time')
            ->whereNull('to_time');
    }

    public function delete()
    {
        $this->deleteTranslations();

        return parent::delete();
    }

    public function getImageArAttribute()
    {
        $ar_images = [];
        $translations = BranchTranslation::where('locale', 'ar')
            ->where('branch_id', $this->id)
            ->get();

        if (count($translations) > 0) {
            foreach ($translations as $item) {
                if ($item->image1 != '' && $item->image1 != null) {
                    $path = \Storage::url($item->image1);
                    if ($path) {
                        array_push($ar_images, $path);
                    }
                }
                if ($item->image2 != '' && $item->image2 != null) {
                    $path = \Storage::url($item->image2);
                    if ($path) {
                        array_push($ar_images, $path);
                    }
                }
                if ($item->image3 != '' && $item->image3 != null) {
                    $path = \Storage::url($item->image3);
                    if ($path) {
                        array_push($ar_images, $path);
                    }
                }
            }
        }

        return $ar_images;
    }

    public function getImageEnAttribute()
    {
        $en_images = [];
        $translations = BranchTranslation::where('locale', 'en')
            ->where('branch_id', $this->id)
            ->get();

        if (count($translations) > 0) {
            foreach ($translations as $item) {
                if ($item->image1 != '' && $item->image1 != null) {
                    $path = \Storage::url($item->image1);
                    if ($path) {
                        array_push($en_images, $path);
                    }
                }
                if ($item->image2 != '' && $item->image2 != null) {
                    $path = \Storage::url($item->image2);
                    if ($path) {
                        array_push($en_images, $path);
                    }
                }
                if ($item->image3 != '' && $item->image3 != null) {
                    $path = \Storage::url($item->image3);
                    if ($path) {
                        array_push($en_images, $path);
                    }
                }
            }
        }

        return $en_images;
    }

    public function getImagesAttribute()
    {
        return app()->getLocale() == 'en' ? $this->image_en : $this->image_ar;
    }

    public function getAllOffDatesAttribute()
    {
        $branch = $this;
        $datesObjects = WorkHour::where('branch_id', $branch->id)
            ->OffDates()
            ->select('date', 'id');
        $disabledDates = $datesObjects
            ->get()
            ->pluck('formate_date')
            ->toArray();
        $brandWorkingDay = WorkingDay::where('brand_id', $branch->brand_id)
            ->pluck('day_id')
            ->toArray();
        $branchOffDay = WorkHour::WorkingHours()
            ->where('branch_id', $branch->id)
            ->whereIn('day_id', $brandWorkingDay)
            ->pluck('day_id')
            ->toArray();

        $daysId = [1, 2, 3, 4, 5, 6, 7];
        $list = [];
        $day_ids = [];

        $disableWorkingDay = array_diff($daysId, $branchOffDay);

        if (count($disableWorkingDay) > 0) {
            $day_arr = [];
            foreach ($disableWorkingDay as $key => $day_id) {
                $index = array_search($day_id, array_column(Constant::Days, 'id'));
                $title = Constant::Days[$index]['title_en'];
                array_push($day_arr, $title);
                array_push($day_ids, (string) $day_id);
            }
            $yearDates = DateRange::getDateRange(date('Y-m-d'), date('Y-m-d', strtotime(date('Y-m-d') . ' +1 year')));
            foreach ($yearDates as $time) {
                $date = date('Y-m-d', strtotime($time));
                if (in_array(date('l', strtotime($date)), $day_arr)) {
                    $list[] = date('Y-m-d', strtotime($time));
                    array_push($disabledDates, date('Y-n-j', strtotime($time)));
                }
            }
        }

        return $disabledDates;
    }

    //birthdaySettings

    public function birthdaySettings()
    {
        return $this->hasMany(\App\Models\Booking\Addon\BirthdaySetting::class);
    }
}