<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RefundOrder
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order_id;
    public $amount;
    public $payment_method;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($order_id, $amount,$payment_method)
    {
        //
        $this->order_id = $order_id;
        $this->amount = $amount;
        $this->payment_method = $payment_method;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('refund-order');
    }
}
