<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LoyaltyPointCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;
    public $type; //order or referral
    public $wallet;

    /**
     * Create a new event instance.
     */

    public function __construct($wallet, $data, $type)
    {
        //
        $this->data = $data; //order data or referral user data
        $this->type = $type;
        $this->wallet = $wallet;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [new PrivateChannel('channel-name')];
    }
}
