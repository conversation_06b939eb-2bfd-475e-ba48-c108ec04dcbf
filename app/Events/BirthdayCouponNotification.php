<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BirthdayCouponNotification
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;
    public $coupon;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($user,$coupon)
    {
        //
        $this->user = $user;
        $this->coupon = $coupon;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('notify-birthday-coupon');
    }
}