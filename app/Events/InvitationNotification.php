<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InvitationNotification
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;

    public $invitation;

    public $type; //invite , accept , decline,

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($user, $invitation, $type)
    {
        //
        $this->user = $user;
        $this->invitation = $invitation;
        $this->type = $type;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('invitations-updates');
    }
}
