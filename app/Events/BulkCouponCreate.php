<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BulkCouponCreate
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public $no_coupons;

    public $prefix;

    public $data;

    public $bulkCoupon;

    public $email;

    public $branches;

    /**
     * Create a new event instance.
     */
    public function __construct($no_coupons, $prefix, $data, $bulkCoupon, $email, $branches = [])
    {
        $this->no_coupons = $no_coupons;
        $this->prefix = $prefix;
        $this->data = $data;
        $this->bulkCoupon = $bulkCoupon;
        $this->email = $email;
        $this->branches = $branches;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [new PrivateChannel('bulk-coupon-create')];
    }
}
