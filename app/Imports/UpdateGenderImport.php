<?php

namespace App\Imports;

use App\Models\User;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithStartRow;

class UpdateGenderImport implements ToCollection, WithStartRow
{
    /**
     * ShouldQueue.
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            if ($row[5] != '' && $row[2] != '') {
                $item = User::where('email', trim(str_replace("\n", '', $row[5])))->update([
                    'gender' => strtolower(trim($row[2])),
                ]);
            }
        }
    }

    // public function batchSize(): int
    // {
    //     return 1000;
    // }

    // public function chunkSize(): int
    // {
    //     return 1000;
    // }
}
