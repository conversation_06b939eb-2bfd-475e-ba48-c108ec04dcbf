<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BirthdayEventRequestMail extends Mailable
{
    use Queueable, SerializesModels;

    public $branchTitle;

    public $brandTitle;

    public $event;

    public $setting;

    /**
     * Create a new content instance.
     *
     * @return void
     */
    public function __construct($event)
    {
        //
        $setting = AppSetting::first();
        $this->branchTitle = ! empty($event->branch) ? $event->branch->title_en : __('messages.all_branches');
        $this->brandTitle = ! empty($event->brand) ? $event->brand->title_en : __('messages.all_over_sala_app');
        $this->event = $event ?? null;
        $this->setting = $setting;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.birthday_request')->subject('Sala Birthday Event Request');
    }
}
