<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CouponNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $title;
    public $content;
    public $user;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($title, $content, $user)
    {
        //
        $setting = AppSetting::first();
        $this->user = $user;
        $this->setting = $setting;
        $this->title = $title;
        $this->content = $content;
        $this->locale = $user->lang ?? 'en';
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $user = $this->user;
        $setting = $this->setting;
        $title = $this->title;
        $content = $this->content;
        $locale = $this->locale;

        return $this->view('emails.coupon', compact('user', 'title', 'content', 'setting', 'locale'))->subject(
            $this->title
        );
    }
}
