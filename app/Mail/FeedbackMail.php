<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class FeedbackMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;
    public $branchTitle;
    public $brandTitle;
    public $reference_number;

    public $content;
    public $name;
    public $email;
    public $mobile;
    public $files;
    public $setting;
    public $subjectTitle;

    /**
     * Create a new content instance.
     *
     * @return void
     */
    public function __construct($feedback, $files)
    {
        //
        $setting = AppSetting::first();
        $this->subject = $feedback->subject->title_en ?? '';
        $this->branchTitle =
            $feedback->branch_id === 0 ? __('messages.all_branches') : $feedback->branch->title_en ?? '';
        $this->brandTitle =
            $feedback->brand_id === 0 ? __('messages.all_over_sala_app') : $feedback->brand->title_en ?? '';
        $this->content = $feedback->message ?? '';
        $this->name = $feedback->name;
        $this->email = $feedback->email;
        $this->mobile = $feedback->mobile;
        $this->reference_number = $feedback->reference_number;
        $this->subjectTitle = $feedback->subject->title_en ?? '';
        $this->files = $files ?? [];
        $this->setting = $setting;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $email = $this->view('emails.feedback')->subject('Sala Feedback');

        foreach ($this->files as $filePath) {
            $email->attach($filePath);
        }

        return $email;
    }
}
