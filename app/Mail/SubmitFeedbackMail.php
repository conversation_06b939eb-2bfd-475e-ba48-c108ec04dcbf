<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SubmitFeedbackMail extends Mailable
{
    use Queueable, SerializesModels;
    public $title;
    public $setting;
    public $content;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($title, $content)
    {
        //
        $setting = AppSetting::first();
        $this->title = $title;
        $this->content = $content;
        $this->setting = $setting;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $title = $this->title;
        $content = $this->content;
        $setting = $this->setting;

        return $this->view('emails.submitFeedback', compact('content', 'title', 'setting'))->subject($title);
    }
}