<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrganizeEmailVerifyMail extends Mailable
{
    use Queueable, SerializesModels;

    public $verificationUrl;

    public $user;

    public $setting;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($verificationUrl, $user)
    {
        //
        $setting = AppSetting::first();
        $this->verificationUrl = $verificationUrl;
        $this->user = $user;
        $this->setting = $setting;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $verificationUrl = $this->verificationUrl;
        $user = $this->user;
        $setting = $this->setting;

        return $this->view('emails.organizeEmail', compact('verificationUrl', 'user', 'setting'))->subject(
            __('messages.OrganizeEmailVerifyMailTitle')
        );
    }
}
