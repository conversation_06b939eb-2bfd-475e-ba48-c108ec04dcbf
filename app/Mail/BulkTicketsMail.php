<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BulkTicketsMail extends Mailable
{
    use Queueable, SerializesModels;

    public $tickets;
    public $order_number;
    public $corporate_name;
    public $content;
    public $link;
    public $setting;

    /**
     * Create a new content instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        //
        $this->tickets = $data['tickets'];
        $this->order_number = $data['order_number'];
        $this->corporate_name = $data['corporate_name'];
        $this->content = $data['content'];
        $this->link = $data['link'];
        $this->setting = AppSetting::first();
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $order_number = $this->order_number;
        $corporate_name = $this->corporate_name;
        $link = $this->link;
        $setting = $this->setting;

        return $this->view('emails.bulkTickets', compact('order_number', 'corporate_name', 'link', 'setting'))->subject(
            __('messages.BulkTicketsEmailTitle')
        );
    }
}
