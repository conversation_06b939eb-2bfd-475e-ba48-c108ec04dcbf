<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ExportMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public $setting;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $setting = AppSetting::first();
        $this->data = $data;
        $this->setting = $setting;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $data = $this->data;
        $setting = $this->setting;

        return $this->view('emails.export', compact('data', 'setting'))->subject($data['subject']);

        // ->attachFromStorageDisk('s3', $data['file'])
    }
}
