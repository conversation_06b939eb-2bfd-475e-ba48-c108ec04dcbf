<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ExpireOrganizeEmailMail extends Mailable
{
    use Queueable, SerializesModels;
    public $verificationUrl;
    public $user;
    public $setting;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($verificationUrl, $user, $title, $content)
    {
        //
        $setting = AppSetting::first();
        $this->verificationUrl = $verificationUrl;
        $this->user = $user;
        $this->title = $title;
        $this->content = $content;
        $this->locale = $user->lang ?? 'en';
        $this->setting = $setting;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $verificationUrl = $this->verificationUrl;
        $user = $this->user;
        $setting = $this->setting;
        $title = $this->title;
        $content = $this->content;
        $locale = $this->locale;

        return $this->view(
            'emails.expireOrganizeEmail',
            compact('verificationUrl', 'user', 'setting', 'title', 'content', 'locale')
        )->subject(__('messages.OrganizeEmailVerifyMailTitle'));
    }
}
