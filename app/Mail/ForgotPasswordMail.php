<?php

namespace App\Mail;

use App\Models\AppSetting\AppSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ForgotPasswordMail extends Mailable
{
    use Queueable, SerializesModels;
    public $verifyCode;
    public $user;
    public $link;
    public $setting;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($verifyCode, $link, $user)
    {
        //
        $setting = AppSetting::first();
        $this->verifyCode = $verifyCode;
        $this->user = $user;
        $this->setting = $setting;
        $this->link = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $verifyCode = $this->verifyCode;
        $user = $this->user;
        $link = $this->link;
        $setting = $this->setting;

        return $this->view('emails.auth', compact('verifyCode', 'link', 'user', 'setting'))->subject(
            __('messages.ForgotPasswordEmailTitle')
        );
    }
}
