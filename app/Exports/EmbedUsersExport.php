<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EmbedUsersExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 20],
                'fill' => ['color' => ['argb' => 'red']],
            ],
        ];
    }

    /**
     * @var User
     */
    public function map($user): array
    {
        return [
            $user->id,
            $user->first_name,
            $user->last_name,
            $user->mobile,
            $user->email,
            $user->embedCards->date ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'User ID',
            'First Name',
            'Last Name',
            'Mobile',
            'Email',
            'Embed Creation Date',
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function query()
    {
        $data = $this->data;
        $users = User::whereHas('embedCards')->whereNull('deleted_at');

        if (isset($data['userIds'])) {
            $users = $users->whereIn('id', $data['userIds']);
        }

        $search = $data['search'] ?? '';
        $active = $data['active'] ?? '';

        if (isset($search) && $search != '') {
            $users = $users->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%')
                    ->orWhere('name', 'like', '%'.$search.'%')
                    ->orWhere('mobile', 'like', '%'.$search.'%')
                    ->orWhere('nationality', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhere('date_of_birth', 'like', '%'.$search.'%')
                    ->orWhere('country', 'like', '%'.$search.'%')
                    ->orWhere('city', 'like', '%'.$search.'%');
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $users = $users->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $users = $users->whereDate('created_at', '<=', $data['toDate']);
        }
        if (isset($active) && $active != '') {
            $users = $users->where('active', $active);
        }

        return $users;
    }
}
