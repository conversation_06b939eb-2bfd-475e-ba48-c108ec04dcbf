<?php

namespace App\Exports;

use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TicketsExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithCustomValueBinder, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $data;

    protected $brand;

    public function __construct($data, $brand)
    {
        $this->data = $data;
        $this->brand = $brand;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 20],
                'fill' => ['color' => ['argb' => 'red']],
            ],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'W' => \PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @var Order
     */
    public function map($item): array
    {
        return [
            $item->order->order_number,
            ! empty($item->order->brand) ? $item->order->brand->title_en : '',
            ! empty($item->order->branch) ? $item->order->branch->title_en : '',
            ! empty($item->order->branch) && ! empty($item->order->branch->governorate)
                ? $item->order->branch->governorate->title_en
                : '',
            $item->order->name ? $item->order->name : ($item->order->corporate ? $item->order->corporate->name : ''),
            $item->order->mobile
                ? $item->order->mobile
                : ($item->order->corporate
                    ? $item->order->corporate->mobile
                    : ''),
            $item->order->email ? $item->order->email : ($item->order->corporate ? $item->order->corporate->email : ''),
            $item->title_en,
            $item->quantity,
            $item->price,

            $item->order->status_text,
            $item->order->order_date,
            $item->order->order_time,
            $item->order->payment_method_text,
            $item->order->payment_status_text,
            $item->order->created_date,
            $item->order->claimed_date,
            ! empty($item->order->claimed_user) ? $item->order->claimed_user->name : '',
            'App',
            $item->order->fort_id.' ',
            $item->order->foodics_id.' ',
        ];
    }

    public function headings(): array
    {
        return [
            'Order Reference ID',
            'Brand',
            'Branch',
            'Governorate',
            'Customer Name',
            'Customer Mobile',
            'Customer Email',
            'Product',
            'Quantity',
            'Price',
            'Order Status',
            'Order Date',
            'Order Time',
            'Payment Method',
            'Payment Status',
            'Creation Date',
            'Claim Date',
            'Claim By',
            'Booked By',
            'Payfort Referance',
            'Foodices Referance',
        ];
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);

        return true;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function query()
    {
        $data = $this->data;
        $brand = $this->brand;
        $orders = Order::whereHas('branch')
            ->with('branch', function ($q) {
                $q->with('governorate');
            })
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');

        $orders = $orders->where('is_parent', 0);
        $orders = $orders->with('corporate');
        $city = $data['city'];

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if ($city != '') {
            $orders = $orders->whereHas('branch', function ($q) use ($city) {
                $q->where('governorate_id', $city);
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }
        $ids = $orders->pluck('id')->toArray();

        $orders = OrderTicket::whereIn('order_id', $ids)->with('order');

        return $orders;
    }
}
