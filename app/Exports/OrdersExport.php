<?php

namespace App\Exports;

use App\Models\Ordering\Order;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Cell\Cell;

class OrdersExport implements FromQuery, ShouldAutoSize, WithCustomValueBinder, WithHeadings, WithMapping
{
    use Exportable;

    protected $data;

    protected $brand;

    public function __construct($data, $brand)
    {
        $this->data = $data;
        $this->brand = $brand;
    }

    /**
     * @var Order
     */
    public function map($order): array
    {
        $paymentStatus = $order->payment_status_text;
        $offer = $order->offer_id ? 'Offer' : '-';
        $transferred =
            $order->is_parent_order_transfer > 0
                ? 'Parent Order Transfer'
                : ($order->parent_order_transfer > 0
                    ? 'Child Order Transfer - ' . $order->parentOrderTransferObj->order_number
                    : '-');

        if ($order->parentOfferOrder) {
            $totalPrice = $order->parentOfferOrder->total_price;
            $price = $order->parentOfferOrder->price;
            $paymentStatus = $order->parentOfferOrder->payment_status_text;
        } else {
            $order->price = 0 ? '0.00' : $order->total_price;
            $totalPrice = $order->price;
            $price = $order->price;
        }

        if ($order->parent_order_transfer) {
            $paymentStatus = $order->parentOrderTransferObj->payment_status_text;
            $price = 0;
            $totalPrice = 0;
        }

        return [
            $order->order_number,
            ($order->brand ? $order->brand->title_en : '') . ' / ' . ($order->branch ? $order->branch->title_en : ''),
            $order->name ? $order->name : ($order->corporate ? $order->corporate->name : ''),
            $order->mobile ? $order->mobile : ($order->corporate ? $order->corporate->mobile : ''),
            $order->email ? $order->email : ($order->corporate ? $order->corporate->email : ''),
            $price,
            $order->discount + ($order->coupon_discount > $order->price ? $order->price : $order->coupon_discount) ??
            '0.00',
            $totalPrice,
            $offer,
            $transferred,
            $order->status_text,
            $order->order_date,
            $order->order_time,
            $order->parentOfferOrder ? $order->parentOfferOrder->payment_method_text : $order->payment_method_text,
            $paymentStatus,
            $order->created_date,
            $order->coupon ? $order->coupon->code : '',
        ];
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);

        return true;
    }

    public function headings(): array
    {
        return [
            'Order Reference ID',
            'Branch',
            'Customer Name',
            'Customer Mobile',
            'Customer Email',
            'Order amount',
            'Discount',
            'Order Amount After Discount',
            'Is Offer',
            'Is Transfered',
            'Order Status',
            'Order Date',
            'Order Time',
            'Payment Method',
            'Payment Status',
            'Creation Date',
            'Coupon Code',
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function query()
    {
        $data = $this->data;
        $brand = $this->brand;
        $orders = Order::select(
            'id',
            'order_number',
            'brand_id',
            'branch_id',
            'name',
            'email',
            'mobile',
            'price',
            'total_price',
            'discount',
            'offer_id',
            'order_date',
            'order_time',
            'parent_offer_order_id',
            'created_at',
            'coupon_id',
            'parent_order_transfer',
            'is_parent_order_transfer',
            'payment_status'
        )
            ->whereHas('brand')
            ->whereDoesntHave('offer_sub_orders')
            ->orderBy('order_date', 'desc');

        // $orders = $orders->with([
        //     'corporate' => function ($query) {
        //         $query->without('brand', 'branch', 'tickets');
        //     },
        //     'brand' => function ($query) {
        //         $query->without('social_links');
        //     },
        //     'branch',
        //     'coupon' => function ($query) {
        //         $query->without('brand');
        //     },
        // ]);
        $orders = $orders->without(
            'brand',
            'branch',
            'coupon',
            'corporate',
            'user',
            'claimed_user',
            'tickets',
            'bundles'
        );

        $search = $data['search'];
        $orders = $orders->where('is_parent', 0);

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if (isset($search) && $search != '') {
            $orders = $orders->where(function ($q) use ($search) {
                $q->where('order_number', 'like', '%' . $search . '%');
                $q->orWhere('qr_code', 'like', '%' . $search . '%');
                $q->orWhere('id', 'like', '%' . $search . '%');
                $q->orWhere('name', 'like', '%' . $search . '%');
                $q->orWhere('email', 'like', '%' . $search . '%');
                $q->orWhere('mobile', 'like', '%' . $search . '%');
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }
        if (isset($data['bookingDate']) && $data['bookingDate'] != '') {
            $orders = $orders->whereDate('order_date', '=', $data['bookingDate']);
        }

        if (isset($data['status']) && $data['status'] != '') {
            $orders = $orders->where('status', $data['status']);
        }

        if (isset($data['payment_method']) && $data['payment_method'] != '') {
            $orders = $orders->where('payment_method', $data['payment_method']);
        }
        if (isset($data['payment_status']) && $data['payment_status'] != '') {
            $orders = $orders->where('payment_status', $data['payment_status']);
        }
        if (isset($data['corporate'])) {
            $orders = $orders->where('corporate_request', $data['corporate']);
        }

        if (isset($data['haveOffer']) && $data['haveOffer'] == '1') {
            //parent_offer_order_id or offer_id
            $orders = $orders->whereNotNull('offer_id');
        } elseif (isset($data['haveOffer']) && $data['haveOffer'] == '0') {
            //parent_offer_order_id or offer_id
            $orders = $orders->whereNull('offer_id');
        }

        $orders = $orders->orderBy('order_date', 'desc');
        // dd(strlen(json_encode($orders->get())));
        return $orders;
    }
}