<?php

namespace App\Exports;

use App\Models\Promotion\Coupon\Coupon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BulkCouponsExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $id;

    public function __construct($id)
    {
        $this->id = $id;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 20],
                'fill' => ['color' => ['argb' => 'red']],
            ],
        ];
    }

    /**
     * @var Coupon
     */
    public function map($coupon): array
    {
        return [$coupon->id, $coupon->code, $coupon->no_used > 0 ? 'used' : 'not used', $coupon->date];
    }

    public function headings(): array
    {
        return ['Code ID', 'Code', 'Usage Status', 'Creation Date'];
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function query()
    {
        $id = $this->id;
        $coupons = Coupon::where('bulk_coupon_id', $id);

        return $coupons;
    }
}
