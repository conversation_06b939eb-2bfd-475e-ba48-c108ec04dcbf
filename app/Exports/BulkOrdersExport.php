<?php

namespace App\Exports;

use App\Models\Ordering\Order;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BulkOrdersExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize, WithStyles
{
    use Exportable;
    protected $id;
    protected $type;

    public function __construct($id, $type = 'order')
    {
        $this->id = $id;
        $this->type = $type; //order or corporate
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 20],
                'fill' => ['color' => ['argb' => 'red']],
            ],
        ];
    }

    /**
     * @var Order
     */
    public function map($order): array
    {
        return [
            $order->order_number,
            $order->payment_status_text,
            $order->branch ? $order->branch->title_en : '',
            $order->price,
            date('Y-m-d', strtotime($order->expire_date)) < date('Y-m-d') ? 'Expired' : $order->status_text,
            $order->booking_date,
            $order->expire_date != null ? date('m/d/Y', strtotime($order->expire_date)) : '',
            $order->created_date,
        ];
    }

    public function headings(): array
    {
        return [
            'Order Reference ID',
            'Payment Status',
            'Branch',
            'Price',
            'Order Status',
            'Booking Date',
            'Expired Date',
            'Creation Date',
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function query()
    {
        $id = $this->id;
        if ($this->type == 'corporate') {
            return Order::where([
                'is_bulk' => 1,
                'is_parent' => 0,
                'corporate_request' => $id,
            ])
                ->whereHas('branch')
                ->whereHas('brand')
                ->orderBy('order_date', 'desc');
        }

        return Order::where('parent', $id)
            ->whereHas('branch')
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');
    }
}
