<?php

namespace App\Exports;

use App\Models\Ordering\Order;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SaleExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithCustomQuerySize, WithCustomValueBinder, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $data;

    protected $brand;

    public function __construct($data, $brand)
    {
        $this->data = $data;
        $this->brand = $brand;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 20],
                'fill' => ['color' => ['argb' => 'red']],
            ],
        ];
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);

        return true;
    }

    public function columnFormats(): array
    {
        return [
            'T' => \PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @var Order
     */
    public function map($order): array
    {
        return [
            $order->order_number,
            !empty($order->brand) ? $order->brand->title_en : '',
            !empty($order->branch) ? $order->branch->title_en : '',
            !empty($order->branch) && !empty($order->branch->governorate) ? $order->branch->governorate->title_en : '',
            $order->name ? $order->name : ($order->corporate ? $order->corporate->name : ''),
            $order->mobile ? $order->mobile : ($order->corporate ? $order->corporate->mobile : ''),
            $order->email ? $order->email : ($order->corporate ? $order->corporate->email : ''),
            $order->no_tickets,
            $order->no_addons,
            $order->price,
            ($order->coupon_discount > $order->price ? $order->price : $order->coupon_discount) ?? '0.00',
            $order->total_price == 0 ? '0.00' : $order->total_price,
            $order->coupon ? $order->coupon->title_en : '',
            $order->coupon ? $order->coupon->code : '',
            $order->payWithExternal,
            $order->payWithSalaCredit,
            $order->payWithLoyality,
            $order->refundPaymentText,
            $order->status_text,
            $order->order_date,
            $order->order_time,
            $order->payment_method_text,
            $order->payment_status_text,
            $order->created_date,
            $order->claimed_date,
            !empty($order->claimed_user) ? $order->claimed_user->name : '',
            'App',
            $order->fort_id.' ',
            $order->foodics_id.' ',
        ];
    }

    public function headings(): array
    {
        return [
            'Order Reference ID',
            'Brand',
            'Branch',
            'Governorate',
            'Customer Name',
            'Customer Mobile',
            'Customer Email',
            'No.Tickets',
            'No.Products',
            'Order amount',
            'Discount Value',
            'Order amount after discount',
            'Coupon Name',
            'Coupon Code',
            'Payfort amount',
            'Sala credit amount',
            'Loyality amount',
            'Refund Details',

            'Order Status',
            'Order Date',
            'Order Time',
            'Payment Method',
            'Payment Status',
            'Creation Date',
            'Claim Date',
            'Claim By',
            'Booked By',
            'Payfort Referance',
            'Foodices Referance',
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function query()
    {
        $data = $this->data;
        $brand = $this->brand;
        $orders = Order::whereHas('branch')
            ->with('branch', function ($q) {
                $q->with('governorate');
            })
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');

        $orders = $orders->where('is_parent', 0);
        $orders = $orders->with('corporate');
        $city = $data['city'];

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if (isset($city) && $city != '') {
            $orders = $orders->whereHas('branch', function ($q) use ($city) {
                $q->where('governorate_id', $city);
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }

        return $orders;
    }

    public function querySize(): int
    {
        $data = $this->data;
        $brand = $this->brand;
        $orders = Order::whereHas('branch')
            ->with('branch', function ($q) {
                $q->with('governorate');
            })
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');

        $orders = $orders->where('is_parent', 0);
        $orders = $orders->with('corporate');
        $city = $data['city'];

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if (isset($city) && $city != '') {
            $orders = $orders->whereHas('branch', function ($q) use ($city) {
                $q->where('governorate_id', $city);
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }

        return $orders->count();
    }
}
