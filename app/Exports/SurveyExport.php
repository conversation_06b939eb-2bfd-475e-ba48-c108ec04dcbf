<?php

namespace App\Exports;

use App\Models\Survey\UserSurvey;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;

class SurveyExport implements FromView
{
    use Exportable;

    protected $data;

    protected $brand;

    public function __construct($data, $brand)
    {
        $this->data = $data;
        $this->brand = $brand;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function view(): View
    {
        $data = $this->data;
        $brand = $this->brand;
        $surveys = UserSurvey::Recent()->with('user', 'order', 'answers');

        $surveys = $surveys->whereHas('order', function ($q) use ($brand) {
            $q->where('brand_id', $brand);
        });

        // $search = $data['search'];
        // if (isset($search) && $search != '') {
        //     $surveys = $surveys->where(function ($q) use ($search) {
        //         $q->where('id', 'like', '%' . $search . '%')
        //             ->orWhere('name', 'like', '%' . $search . '%')
        //     });
        // }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $surveys = $surveys->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $surveys = $surveys->whereDate('created_at', '<=', $data['toDate']);
        }
        if (isset($data['category']) && $data['category'] != '') {
            $surveys = $surveys->where('category_id', $data['category']);
        }
        $surveys = $surveys->get();

        return view('exports.surveys', compact('surveys'));
    }
}
