<?php

namespace App\Exports;

use App\Models\ActivityLog;
use App\Models\Booking\Ticket\Ticket;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TicketCreatedExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $data;

    protected $brand;

    public function __construct($data, $brand)
    {
        $this->data = $data;
        $this->brand = $brand;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 20],
                'fill' => ['color' => ['argb' => 'red']],
            ],
        ];
    }

    /**
     * @var ActivityLog
     */
    public function map($activity): array
    {
        return [
            $activity->object->brand ? $activity->object->brand->title_en : '',
            $activity->object->locations,
            $activity->object->governorate,
            $activity->object->title_en,
            $activity->changes['attributes']['price'] ?? '',
            $activity->date,
            $activity->time,
            $activity->user ? $activity->user->name : '',
        ];
    }

    public function headings(): array
    {
        return ['Brand', 'Location', 'Governorate', 'Item', 'Price', 'Create Date', 'Create Time', 'Create User'];
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function query()
    {
        $data = $this->data;
        $brand = $this->brand;
        $city = $data['city'];
        $tickets = Ticket::whereHas('brand')->orderBy('id', 'asc');

        if ($brand != '') {
            $tickets = $tickets->where('brand_id', $brand);
        }
        if ($city != '') {
            $tickets = $tickets->whereHas('branches', function ($q) use ($city) {
                $q->whereHas('branch', function ($q1) use ($city) {
                    $q1->where('governorate_id', $city);
                });
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $tickets = $tickets->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $tickets = $tickets->whereDate('created_at', '<=', $data['toDate']);
        }
        $ids = $tickets->pluck('id')->toArray();

        $activities = ActivityLog::where('event', 'created')
            ->whereIn('subject_id', $ids)
            ->where('subject_type', 'App\Models\Booking\Ticket\Ticket');

        return $activities;
    }
}
