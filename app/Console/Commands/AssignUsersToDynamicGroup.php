<?php

namespace App\Console\Commands;

use App\Models\Group\Group;
use App\Models\User;
use Illuminate\Console\Command;

class AssignUsersToDynamicGroup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:assign-users-to-dynamic-group';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Old Users should be assigned to dynamic group ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $firstGroup = Group::where('type', 'dynamic')
            ->where('active', '1')
            ->whereNull('deleted_at')
            ->orderBy('start_spending_thresholds', 'asc')
            ->first();
        if (! empty($firstGroup)) {
            $affectedUsersCount = 0;

            User::whereDoesntHave('groups')->chunk(1000, function ($users) use ($firstGroup, &$affectedUsersCount) {
                foreach ($users as $user) {
                    $user->groups()->attach($firstGroup->id);
                    $affectedUsersCount++;
                }
                $this->info("Processed chunk. Total affected users so far: {$affectedUsersCount}");
            });

            $this->info(
                "Old Users assigned to dynamic group successfully. Total affected users: {$affectedUsersCount}"
            );
        } else {
            $this->info('No dynamic group found or no users to assign.');
        }
    }
}
