<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Group\Group;

class GroupExpiration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'groups:group-expiration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Group::whereDate('account_reactivation', '<', now())->update(['active' => 0]);

        $this->info('the expired Groups  have been deactivated.');
    }
}
