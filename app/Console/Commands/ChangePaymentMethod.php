<?php

namespace App\Console\Commands;

use App\Models\Ordering\Order;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ChangePaymentMethod extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:changePaymentMethod';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'The order will be created with the status “Upcoming” and the
     payment status will be updated after 1 hour (After no action from the user) to Cash';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Order::where('created_at', '<=', Carbon::now()->subHour())
            ->where('status', 0)
            ->where('payment_method', 3)
            ->where('payment_status', 0)
            ->update(['payment_method' => 1]);
        // $this->info('Update has been send successfully');
    }
}
