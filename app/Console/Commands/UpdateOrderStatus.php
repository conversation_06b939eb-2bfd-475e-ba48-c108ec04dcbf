<?php

namespace App\Console\Commands;

use App\Events\LoyaltyPointCreated;
use App\Models\Ordering\Order;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateOrderStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:changeStatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cronjob daily 7 am, change the status for un-claimed tickets until yesterday, to No Show';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //handle the offers orders case
        /*
        $offerSubOrders = Order::whereNotNull('parent_offer_order_id')
            ->whereDate('order_date', '<', date('Y-m-d'))
            ->where('status', 0);

        $offerSubOrdersIDs = $offerSubOrders->get()->pluck('parent_offer_order_id');

        $offerSubOrders = $offerSubOrders->update(['status' => 3]);
        $offerOrders = Order::Paid()
            ->whereIn('id', $offerSubOrdersIDs)
            ->get();
        foreach ($offerOrders as $offerOrder) {
            if ($offerOrder->status == 0) {
                event(new LoyaltyPointCreated(null, $offerOrder, 'order'));
            }
            if (!empty($offerOrder->offer_sub_orders) && count($offerOrder->offer_sub_orders) > 0) {
                $offerSubOrdersStatuses = [];
                foreach ($offerOrder->offer_sub_orders as $offerSubOrder) {
                    $offerSubOrdersStatuses[$offerSubOrder->status] = 1;
                }
                if (count($offerSubOrdersStatuses) == 1) {
                    $offerOrder->status = array_key_first($offerSubOrdersStatuses);
                } elseif (count($offerSubOrdersStatuses) > 1) {
                    $offerOrder->status = 4;
                }
                $offerOrder->save();
            }
        }

        $orders = Order::Paid()
            ->whereDate('order_date', '<', date('Y-m-d'))
            ->where('status', 0)
            ->get();
        Order::whereDate('order_date', '<', date('Y-m-d'))
            ->where('status', 0)
            ->update(['status' => 3]);
        foreach ($orders as $order) {
            event(new LoyaltyPointCreated(null, $order, 'order'));
        }
*/
        // $this->info('Update has been send successfully');
    }
}
