<?php

namespace App\Console\Commands;

use App\Models\AppSetting\AppSetting;
use App\Models\User;
use App\Models\Wallet\WalletTransaction;
use Illuminate\Console\Command;

class LoyaltyPointsExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'loyalty-points:expiry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Checking Loyalty Points Balance and expiry info';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $setting = AppSetting::first();

        if (empty($setting)) {
            return false;
        }

        $checkDateForBrand = strtotime('-'.$setting->points_expiry_month.' months');
        // $oneWeekAgoDate = strtotime('+7 day', $checkDateForBrand);

        $users = User::whereHas('walletTransactions', function ($q) use ($checkDateForBrand) {
            $q->Credit()
                ->whereNull('expired_at')
                ->Loyalty()
                ->where('created_at', '<=', date('Y-m-d', $checkDateForBrand));
        })
            ->get()
            ->where('points', '>', 0);

        if (! empty($users)) {
            foreach ($users as $user) {
                //
                $pointWillExpired = $user
                    ->walletTransactions()
                    ->where('created_at', '<=', date('Y-m-d', $checkDateForBrand))
                    ->Loyalty()
                    ->Credit()
                    ->sum('transaction_amount');

                $pointSpending = $user
                    ->walletTransactions()
                    ->Loyalty()
                    ->Deduct()
                    ->sum('transaction_amount');

                //$pointWillExpired > spending
                if ($pointWillExpired > $pointSpending) {
                    //will expired diffrence
                    $arr = [];
                    $arr['transaction_type'] = '0';
                    $arr['transaction_amount'] = $pointWillExpired - $pointSpending;
                    $arr['transaction_reason'] = 'Expired points';
                    $arr['user_id'] = $user->id;
                    $arr['currency_id'] = 2;

                    WalletTransaction::create($arr);
                }
                // \Log::info($pointWillExpired . '-' . $pointSpending);
            }
        }

        $this->info('Expired Loyalty Points Balance');
    }
}
