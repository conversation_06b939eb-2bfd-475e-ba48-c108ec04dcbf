<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class ReferralUserCoupons extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:generateReferralUserCoupons';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Referral User Coupons';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        do {
            $users = User::whereNull('referral_code')
                ->take(5000)
                ->get();

            foreach ($users as $row) {
                $row->update(['referral_code' => uniqueStringCode('App\Models\User', 'referral_code', 3)]);
            }
        } while (count($users) > 0);
        // $this->info('Referral User Coupons');
    }
}