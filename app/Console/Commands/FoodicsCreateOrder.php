<?php

namespace App\Console\Commands;

use App\Events\CreateFoodicsOrder;
use App\Models\Ordering\Order;
use Illuminate\Console\Command;

class FoodicsCreateOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'foodics:create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'We should create foodics order on the same day';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        date_default_timezone_set('Asia/Riyadh');
        $today = date('Y-m-d');
        $tomorrow = date('Y-m-d', strtotime('tomorrow'));

        // $todayAtMidnight = date('Y-m-d H:i:s', strtotime('today 00:00:00'));
        // $tomorrowAt2Am = date('Y-m-d H:i:s', strtotime('tomorrow 02:00:00'));
        $orders = Order::where('status', 0)
            ->whereNull('foodics_id')
            ->whereHas('addons')
            ->where(function ($q) use ($today, $tomorrow) {
                $q->where(function ($q1) use ($today) {
                    $q1->whereDate('order_date', '=', $today)->whereTime('order_time', '>', '02:00:00');
                });
                $q->orWhere(function ($q2) use ($tomorrow) {
                    $q2->whereDate('order_date', '=', $tomorrow)->whereTime('order_time', '<=', '02:00:00');
                });
            })
            ->pluck('id')
            ->toArray();
        foreach ($orders as $id) {
            // reminder order
            event(new CreateFoodicsOrder($id));
        }
        info('Create foodics order created successfully.');
    }
}
