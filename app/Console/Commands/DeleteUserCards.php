<?php

namespace App\Console\Commands;

use App\Models\UserCard;
use Illuminate\Console\Command;

class DeleteUserCards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cards:deleteOldCard';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cronjob to delete old cards';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UserCard::whereDate('created_at', '<', date('Y-m-d', strtotime('-3 months')))->delete();

        // $this->info('Update has been send successfully');
    }
}