<?php

namespace App\Console\Commands;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckOrganizationalEmailExpirationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:check-expiration {argument}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check expiration of organizational emails';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // for test only
        $argumentValue = $this->argument('argument');
        User::where('organizational_email', $argumentValue)->update([
            'expire_email_date' => Carbon::now(),
        ]);

        $this->info('Organizational email expiration check dispatched successfully.');
    }
}
