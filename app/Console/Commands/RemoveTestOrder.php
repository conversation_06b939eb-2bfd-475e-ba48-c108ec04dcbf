<?php

namespace App\Console\Commands;

use App\Models\Ordering\Order;
use Illuminate\Console\Command;

class RemoveTestOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:delete-test-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'We should delete test order ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return Order::whereIn('email', ['<EMAIL>', '<EMAIL>'])->delete();
    }
}
