<?php

namespace App\Console\Commands;

use App\Events\ZatcaCreateInvoice;
use App\Helpers\Constant;
use App\Models\Ordering\Order;
use Illuminate\Console\Command;

class ZatcaCreateOldInvoice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'zatca:create-invoice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'We should create zatca invoices from 01-02-2024 till now that doesn\'t have successfully invoice ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //get orders
        $orders = Order::whereDate('created_at', '>=', date('2024-02-01'))
            ->where('payment_status', Constant::PaidOrder)
            ->doesntHave('invoices')
            ->pluck('id')
            ->take(100)
            ->toArray();

        foreach ($orders as $order) {
            event(new ZatcaCreateInvoice($order));
        }

        // info('create zatca invoices');
        return true;
    }
}
