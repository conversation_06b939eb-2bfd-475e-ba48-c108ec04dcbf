<?php

namespace App\Console\Commands;

use App\Events\OrganizationalEmailExpired;
use Illuminate\Console\Command;

class OrganizationalEmailExpiration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:organizational-email-expiration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        event(new OrganizationalEmailExpired());
        info('fetch all users that have organizational email expired ');
    }
}
