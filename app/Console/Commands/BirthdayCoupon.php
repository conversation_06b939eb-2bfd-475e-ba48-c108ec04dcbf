<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use App\Models\Promotion\Coupon\Coupon;
use App\Events\BirthdayCouponNotification;

class BirthdayCoupon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coupons:birthday';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'User should receive a coupon notification on his birthday';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $coupon = Coupon::Publish()
            ->BirthDay()
            ->NotUser()
            ->first();

        if (!empty($coupon)) {
            $reminderDay = Carbon::now()
                ->addDays($coupon->notify_days)
                ->format('Y-m-d');

            $users = User::whereMonth('date_of_birth', date('m', strtotime($reminderDay)))
                ->WhereDay('date_of_birth', date('d', strtotime($reminderDay)))
                ->get();

            if (!empty($users)) {
                foreach ($users as $user) {
                    event(new BirthdayCouponNotification($user, $coupon));
                }
            }
        }

        $this->info('coupon notification on his birthday has been send successfully');
    }
}