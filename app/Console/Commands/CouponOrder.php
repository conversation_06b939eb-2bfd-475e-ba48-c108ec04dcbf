<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Helpers\Constant;
use App\Models\Ordering\Order;
use Illuminate\Console\Command;
use App\Events\OrderNotification;
use App\Models\Promotion\Coupon\Coupon;
use App\Events\CouponOrderNotifications;
use App\Models\Promotion\Coupon\CouponBrand;

class CouponOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coupons:order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'User should receive a coupon notification 1 days after claim order or after no show';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $coupon = Coupon::publish()
            ->Order()
            ->NotUser()
            ->first();

        if (!empty($coupon) && $coupon->active == 1) {
            $reminderDay = Carbon::now()
                ->subDays($coupon->notify_days)
                ->format('Y-m-d');

            //claimed or no show and must paid
            $orders = Order::whereIn('status', [1, 3])
                ->whereDate('claimed_at', $reminderDay)
                ->where('payment_status', 1)
                ->get();
            $brand_ids = CouponBrand::where('coupon_id', $coupon->id)
                ->pluck('brand_id')
                ->toArray();

            foreach ($orders as $order) {
                // send notification for coupon order
                if (in_array($order->brand_id, $brand_ids)) {
                    event(new CouponOrderNotifications($order->user, $order, $coupon));
                }
            }
        }

        $this->info('Command has been run successfully');
    }
}
