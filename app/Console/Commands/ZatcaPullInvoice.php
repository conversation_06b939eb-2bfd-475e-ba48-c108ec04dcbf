<?php

namespace App\Console\Commands;

use App\Events\ZatcaPullAndCleanInvoice;
use Illuminate\Console\Command;

class ZatcaPullInvoice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'zatca:pull-invoice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'We should pull zatca invoices and cleanup from api every 1 minute';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        event(new ZatcaPullAndCleanInvoice());

        // info('pull zatca invoices and cleanup');

        return true;
    }
}
