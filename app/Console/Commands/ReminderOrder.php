<?php

namespace App\Console\Commands;

use App\Events\OrderNotification;
use App\Models\Ordering\Order;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ReminderOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'User should receive a reminder notification 3 days before the booking date';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $reminderDay = Carbon::now()->addDays(3);
        $today = Carbon::now();
        $orders = Order::where(function ($q) use ($today) {
            $q->where('status', 0);
            $q->whereDate('order_date', '=', $today);
            $q->where(function ($q1) {
                $q1->whereTime('order_time', '>=', date('h:i'));
                $q1->orWhereNull('order_time');
            });
        })
            ->orWhere(function ($q) use ($today, $reminderDay) {
                $q->where('status', 0);
                $q->whereDate('order_date', '>', $today);
                $q->whereDate('order_date', '<=', $reminderDay);
            })

            ->get();
        foreach ($orders as $order) {
            // reminder order

            event(new OrderNotification($order, 3));
        }
    }
}
