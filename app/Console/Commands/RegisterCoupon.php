<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use App\Models\Promotion\Coupon\Coupon;
use App\Events\RegisterCouponNotification;

class RegisterCoupon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coupons:register';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'User should receive a coupon notification after register on app';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $coupon = Coupon::Publish()
            ->Register()
            ->NotUser()
            ->first();

        if (!empty($coupon) && $coupon->notify_days > 0) {
            $reminderDay = Carbon::now()
                ->subDays($coupon->notify_days)
                ->format('Y-m-d');

            $users = User::whereDate('created_at', $reminderDay)->get();

            if (!empty($users)) {
                foreach ($users as $user) {
                    if (
                        empty(
                            Coupon::where('user_id', $user->id)
                                ->Register()
                                ->first()
                        )
                    ) {
                        event(new RegisterCouponNotification($user, $coupon));
                    }
                }
            }
        }

        $this->info('coupon notification on his register has been send successfully');
    }
}