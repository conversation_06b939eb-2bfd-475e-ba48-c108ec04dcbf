<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
       // Commands\UpdateOrderStatus::class,
        Commands\ReminderOrder::class,
        Commands\ChangePaymentMethod::class,
        Commands\FoodicsCreateOrder::class,
        Commands\BirthdayCoupon::class,
        Commands\RegisterCoupon::class,
        Commands\CouponOrder::class,
        Commands\ZatcaPullInvoice::class,
        Commands\OrganizationalEmailExpiration::class,
        Commands\ZatcaCreateOldInvoice::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        if (!env('APP_ENV') === 'production' && !env('APP_ENV') === 'development') {
            return;
        }
        /*
        $schedule
            ->command('order:changeStatus')
            ->dailyAt('07:00')
            ->sentryMonitor();
        */
        $schedule
            ->command('order:reminder')
            ->dailyAt('07:00')
            ->sentryMonitor();
        $schedule
            ->command('foodics:create')
            ->dailyAt('00:00')
            ->sentryMonitor();
        $schedule
            ->command('coupons:birthday')
            ->dailyAt('07:15')
            ->sentryMonitor();
        $schedule
            ->command('coupons:order')
            ->dailyAt('07:15')
            ->sentryMonitor();
        $schedule
            ->command('coupons:register')
            ->dailyAt('07:15')
            ->sentryMonitor();
        $schedule
            ->command('emails:organizational-email-expiration')
            ->dailyAt('07:15')
            ->sentryMonitor();

        $schedule
            ->command('zatca:pull-invoice')
            ->everyFiveMinutes()
            ->sentryMonitor();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
