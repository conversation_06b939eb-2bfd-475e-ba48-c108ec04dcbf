<?php
## usage example ##
//to encrypt a string use the encryptThis method with only the original string parameter
//then the returned variable will be array of data "the encrypted string" and "iv" the token
//$enc = encryptThis("are you there");

//to decrypt an encrypted data use the decryptThis method with the 2 parameters
//param 1 the encrypted string and param 2 is the iv "token" which was used to encrypt same data
//$dec = decryptThis($enc['data'],$enc['iv']);

function encryptThis($original_string)
{
    // get the cipher method for encrypting
    $ciphering_value = config('app.cipher');
    // get the encryption key
    $encryption_key = config('app.encryption_key');
    // Use openssl_encrypt() function for encrypting the data
    $wasItSecure = false;
    $iv = openssl_random_pseudo_bytes(16, $wasItSecure);
    if ($wasItSecure) {
        return ['data' => openssl_encrypt($original_string, $ciphering_value, $encryption_key, 0, $iv), 'iv' => $iv];
    } else {
        return null;
    }
}

function decryptThis($encryption_value, $iv)
{
    // get the cipher method for encrypting
    $ciphering_value = config('app.cipher');
    // get the encryption key
    $encryption_key = config('app.encryption_key');
    // Use openssl_encrypt() function for decrypting the data
    return openssl_decrypt($encryption_value, $ciphering_value, $encryption_key, 0, $iv);
}