<?php

namespace App\Helpers;

final class Constant
{
    public const VAT = 15;

    public const FaqQuestion = 0;

    public const ReferralQuestion = 1;

    public const BRAND_LOYALTY_TYPE = 0;

    public const REFERRAL_LOYALTY_TYPE = 1;

    public const SingleCouponType = 0;

    public const RegisterCouponType = 1;

    public const CouponOrderType = 2;

    public const BirthDateCouponType = 3;

    public const CouponOneTimeType = 4;

    public const CouponBankOfferType = 5;

    public const GroupCouponType = 6;

    public const ReferralCouponType = 7;

    public const CustomGroupCouponType = 8;

    public const NotifyCoupon = 0;

    public const NotifyOnly = 1;

    public const FoodArrival = [
        [
            'id' => '1',
            'title_en' => 'Get the meal immediately',
            'title_ar' => 'الحصول على الطعام فور الوصول',
        ],
        [
            'id' => '2',

            'title_en' => 'Inform the waiter on reaching the branch',
            'title_ar' => 'إبلغ المطعم فور الوصول',
        ],
    ];

    public const BannerRedirects = [
        [
            'id' => '1',
            'title' => 'Brand promotions',
        ],
        [
            'id' => '2',
            'title' => 'Branches',
        ],
        [
            'id' => '3',
            'title' => 'Start bookings',
        ],
        [
            'id' => '4',
            'title' => 'About brand page',
        ],
        [
            'id' => '5',
            'title' => 'None',
        ],
    ];

    public const BookingSteps = [
        [
            'id' => '1',
            'title_en' => 'Calendar',
            'title_ar' => 'التقويم',
        ],
        [
            'id' => '2',
            'title_en' => 'Ticket Types',
            'title_ar' => 'نوع التذكرة',
        ],
        [
            'id' => '3',
            'title_en' => 'Products',
            'title_ar' => 'المنتجات',
        ],
        [
            'id' => '4',
            'title_en' => 'Checkout',
            'title_ar' => 'الدفع',
        ],
        [
            'id' => '5',
            'title_en' => 'Payment Integration',
            'title_ar' => 'تكملة الدفع',
        ],
    ];

    public const Days = [
        [
            'id' => '1',
            'title_en' => 'Saturday',
            'title_ar' => 'السبت',
        ],
        [
            'id' => '2',
            'title_en' => 'Sunday',
            'title_ar' => 'الأحد',
        ],
        [
            'id' => '3',
            'title_en' => 'Monday',
            'title_ar' => 'الإثنين',
        ],
        [
            'id' => '4',
            'title_en' => 'Tuesday',
            'title_ar' => 'الثلاثاء',
        ],
        [
            'id' => '5',
            'title_en' => 'Wednesday',
            'title_ar' => 'الأربعاء',
        ],
        [
            'id' => '6',
            'title_en' => 'Thursday',
            'title_ar' => 'الخميس',
        ],
        [
            'id' => '7',
            'title_en' => 'Friday',
            'title_ar' => 'الجمعة',
        ],
    ];

    public const PaymentMethod = [
        [
            'id' => '1',
            'title_en' => 'Cash',
            'title_ar' => 'نقدا',
        ],
        [
            'id' => '2',
            'title_en' => 'Coupon',
            'title_ar' => 'قسيمة خصم',
        ],
        [
            'id' => '3',
            'title_en' => 'PayFort',
            'title_ar' => 'PayFort',
        ],
        [
            'id' => '4',
            'title_en' => 'Bank Transfer',
            'title_ar' => 'تحويل بنكي',
        ],
        [
            'id' => '5',
            'title_en' => 'SalaCredit',
            'title_ar' => 'رصيد سالا',
        ],
        [
            'id' => '6',
            'title_en' => 'LoyaltyPoints',
            'title_ar' => 'نقاط الولاء',
        ],
        [
            'id' => '7',
            'title_en' => 'tamara',
            'title_ar' => 'تمارا',
        ],

        [
            'id' => '8',
            'title_en' => 'checkout.com',
            'title_ar' => 'checkout.com',
        ],
    ];

    public const PaymentStatus = [
        [
            'id' => '0',
            'title_en' => 'Waiting',
            'title_ar' => ' انتظار',
        ],
        [
            'id' => '1',
            'title_en' => 'Paid',
            'title_ar' => 'تم الدفع',
        ],

        [
            'id' => '2',
            'title_en' => 'Failed',
            'title_ar' => 'فشل',
        ],
        [
            'id' => '3',
            'title_en' => 'Refunded',
            'title_ar' => 'استرد',
        ],
        [
            'id' => '4',
            'title_en' => 'Request Refund',
            'title_ar' => 'طلب استرداد',
        ],
        [
            'id' => '5',
            'title_en' => 'Cancel',
            'title_ar' => 'ملغي',
        ],
        [
            'id' => '6',
            'title_en' => 'Processing',
            'title_ar' => 'جاري المعالجة',
        ],
    ];

    public const OrderStatus = [
        [
            'id' => '0',
            'title_en' => 'Upcoming',
            'title_ar' => 'جديد',
        ],
        [
            'id' => '1',
            'title_en' => 'Confirmed',
            'title_ar' => 'تم الاستخدام',
        ],
        [
            'id' => '2',
            'title_en' => 'Cancelled',
            'title_ar' => 'إلغاء',
        ],
        [
            'id' => '3',
            'title_en' => 'No show',
            'title_ar' => 'لم يحضر',
        ],
        [
            'id' => '4',
            'title_en' => 'Partially Used',
            'title_ar' => 'إستخدام جزئي',
        ],
    ];

    public const ExportTypes = [
        [
            'id' => '0',
            'title_en' => 'CSV',
            'title_ar' => 'CSV',
            'type' => 'csv',
        ],
        [
            'id' => '1',
            'title_en' => 'Json',
            'title_ar' => 'Json',
            'type' => 'json',
        ],
    ];

    // ReportTypes
    public const ReportTypes = [
        [
            'id' => '0',
            'title_en' => 'Tickets Created',
            'title_ar' => ' تقرير إضافة التذاكر',
            'permission' => 'create-ticket-reports.export',
        ],
        [
            'id' => '1',
            'title_en' => 'Tickets Updated',
            'title_ar' => ' تقرير تعديل التذاكر',
            'permission' => 'update-ticket-reports.export',
        ],
        [
            'id' => '2',
            'title_en' => 'Sales reports',
            'title_ar' => 'تقرير المبيعات',
            'permission' => 'sale-reports.export',
        ],
        [
            'id' => '3',
            'title_en' => 'Foodics reports',
            'title_ar' => 'تقرير فوديكس',
            'permission' => 'foodics-reports.export',
        ],
        [
            'id' => '4',
            'title_en' => 'Ticket orders Reports',
            'title_ar' => 'تقرير طلبات التذاكر',
            'permission' => 'tickets-reports.export',
        ],
        [
            'id' => '5',
            'title_en' => 'Users Reports',
            'title_ar' => 'تقرير المستخدمين ',
            'permission' => 'users.export',
        ],
        [
            'id' => '6',
            'title_en' => 'Embed Cards Reports',
            'title_ar' => 'تقرير مستخدمين امبيد ',
            'permission' => 'embed-cards-reports.export',
        ],
    ];

    public const QrCodeStatus = [
        [
            'id' => '0',
            'title_en' => 'pending',
            'title_ar' => ' قادم',
        ],
        [
            'id' => '1',
            'title_en' => 'activated',
            'title_ar' => 'تم الاستخدام',
        ],
        [
            'id' => '2',
            'title_en' => 'expired',
            'title_ar' => 'منتهية الصلاحية',
        ],
    ];

    public const GroupTicketStatus = [
        [
            'id' => '0',
            'title_en' => 'Pending',
            'title_ar' => ' انتظار',
        ],
        [
            'id' => '1',
            'title_en' => 'Confirmed',
            'title_ar' => 'تم القبول ',
        ],

        [
            'id' => '2',
            'title_en' => 'Declined',
            'title_ar' => 'فشل',
        ],
    ];

    public const BulkTicketStatus = [
        [
            'id' => '0',
            'title_en' => 'Pending',
            'title_ar' => ' انتظار',
        ],
        [
            'id' => '1',
            'title_en' => 'Paid & Upcoming',
            'title_ar' => 'تم الدفع',
        ],

        [
            'id' => '2',
            'title_en' => 'Expired',
            'title_ar' => 'منتهية الصلاحية',
        ],
    ];

    public const UpcomingOrder = 0;

    public const CancelOrder = 2;

    public const ClaimOrder = 1;

    public const NoShowOrder = 3;

    public const PartiallyUsedOrder = 4;

    public const PaidOrder = 1;

    public const RefundOrder = 3;

    public function get_constant($const_name)
    {
        return constant('self::' . $const_name);
    }
}
