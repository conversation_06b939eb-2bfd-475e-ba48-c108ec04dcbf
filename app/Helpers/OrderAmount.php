<?php

namespace App\Helpers;

use App\Models\Booking\Ticket\Ticket;
use App\Models\Promotion\Bundle\Bundle;
use App\Models\Promotion\Coupon\ToCouponBrand;

class OrderAmount
{
    public static function calculate($brand, $branchObj, $coupon, $orderItems)
    {
        $totalItemsPrice = 0;
        $couponDiscount = 0;
        $itemBundles = [];
        $itemTickets = [];
        $itemAddons = [];
        $itemBundleIds = [];
        $itemTicketIds = [];
        $itemAddonIds = [];
        foreach ($orderItems as $item) {
            if ($item['type'] === 'ticket') {
                array_push($itemTickets, $item);
                array_push($itemTicketIds, $item['id']);
            } elseif ($item['type'] === 'addon') {
                array_push($itemAddons, $item);
                array_push($itemAddonIds, $item['id']);
            } elseif ($item['type'] === 'bundle') {
                array_push($itemBundles, $item);
                array_push($itemBundleIds, $item['id']);
            }
        }
        if (count($itemAddonIds) > 0) {
            if ($brand->foodics_token) {
                $itemAddons = FoodicsApi::sendRequest(
                    'products?include=branches&filter[id]=' . implode(',', $itemAddonIds),
                    [],
                    'GET',
                    $brand->foodics_token
                );
                $itemAddons = collect($itemAddons);
            }
        }
        if (count($itemBundleIds) > 0) {
            $itemBundles = Bundle::whereIn('id', $itemBundleIds)->get();
        }
        if (count($itemTicketIds) > 0) {
            $itemTickets = Ticket::whereIn('id', $itemTicketIds)->get();
        }

        foreach ($orderItems as $item) {
            $bookingItem = null;
            if ($item['type'] === 'bundle' && count($itemBundles) > 0) {
                $bookingItem = $itemBundles->first(function ($search) use ($item) {
                    return $search->id == $item['id'];
                });
            } elseif ($item['type'] === 'addon' && count($itemAddons) > 0) {
                $bookingItem = $itemAddons->first(function ($search) use ($item) {
                    return $search->id == $item['id'];
                });
                $bookingItem = (array) $bookingItem;
                if (!empty($bookingItem['branches'])) {
                    foreach ($bookingItem['branches'] as $branch) {
                        if ($branch->id == $branchObj->foodics_branch_id && !empty($branch->pivot)) {
                            $bookingItem['price'] = $branch->pivot->price;
                        }
                    }
                }
            } elseif ($item['type'] === 'ticket' && count($itemTickets) > 0) {
                $bookingItem = $itemTickets->first(function ($search) use ($item) {
                    return $search->id == $item['id'];
                });
            }

            if ($item['type'] == 'ticket' && !empty($coupon) && $bookingItem['have_discount'] == 1) {
                $bookingItem['price'] = $bookingItem['price_before'] ?? 0;
            }

            $totalItemsPrice += $item['quantity'] * $bookingItem['price'];
        }
        //calculate discount coupon
        if (!empty($coupon)) {
            if ($coupon->coupon_type == Constant::CouponOrderType) {
                //get coupon data from brand id
                $toCouponBrand = ToCouponBrand::where('brand_id', $brand->id)->where('coupon_id', $coupon->id)->first();
                if (!empty($toCouponBrand)) {
                    $coupon = $toCouponBrand;
                }
            }
            $couponDiscount = $coupon->discount; //value
            if ($coupon->type != 1) {
                // 0 percent
                $couponDiscount = round($totalItemsPrice * $coupon->discount) / 100;
            }
            //check conditions
            if ($coupon->condition != 0) {
                // 1 max discount value
                if ($coupon->condition == 1) {
                    $couponDiscount =
                        $couponDiscount > $coupon->max_value_discount ? $coupon->max_value_discount : $couponDiscount;
                }
                if ($coupon->condition == 2) {
                    //min total order amount total_order_amount
                    $couponDiscount = $totalItemsPrice >= $coupon->total_order_amount ? $couponDiscount : 0;
                }
                if ($coupon->condition == 3) {
                    // both (min total order amount and  max discount value)
                    $couponDiscount =
                        $totalItemsPrice >= $coupon->total_order_amount
                            ? ($couponDiscount >= $coupon->max_value_discount
                                ? $coupon->max_value_discount
                                : $couponDiscount)
                            : 0;
                }
            }
        }
        // calculate vat

        return [
            'couponDiscount' => $couponDiscount, //coupon discount value
            'totalItemsPrice' => $totalItemsPrice,
            'totalAfterDiscount' => $totalItemsPrice - $couponDiscount,
        ];
    }
}
