<?php

namespace App\Helpers;

class DynamicLink
{
    public static function generate($longUrl)
    {
        $API_KEY = 'AIzaSyAXLkW11xu1OrcVb2QXBKNxLXfXQ7JKQgI';
        $body = [
            'longDynamicLink' => 'https://salahub.page.link/?link=' . $longUrl,
            'suffix' => [
                'option' => 'SHORT',
            ],
        ];

        $url = 'https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=' . $API_KEY;
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($body),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
        ]);

        $result = curl_exec($curl);
        $result = json_decode($result, true);
        curl_close($curl);

        return $result['shortLink'] ?? '';
    }
}
