<?php

namespace App\Helpers\Promotion;

use App\Models\Promotion\Coupon\Coupon;
use App\Models\User;

class ReferralValidation
{
    public static function validate($code, $brand_id, $user)
    {
        $checkReferralCode = User::where('id', '!=', $user->id)
            ->where('referral_code', $code)
            ->first();

        if (!empty($checkReferralCode)) {
            //get coupon code
            return [
                'referralUser' => $checkReferralCode,
                'coupon' => Coupon::publish()
                    ->where(function ($q) use ($brand_id) {
                        $q->whereNull('brand_id');
                        if (!empty($brand_id)) {
                            $q->orWhere('brand_id', $brand_id);
                        }
                    })
                    ->Referral()
                    ->first(),
            ];
        }

        return null;
    }
}