<?php

namespace App\Helpers\Promotion;

use App\Helpers\Constant;
use App\Helpers\General;
use App\Models\Ordering\Order;
use App\Models\Promotion\Coupon\CouponDate;
use App\Models\Promotion\Coupon\ToCouponBrand;

class ValidateUserCoupon
{
    public static function validate($coupon, $brand_id, $device_uuid, $orderDate, $isReferral = false)
    {
        $user = auth()->user();
        $result = [];
        $valid = 0;
        //validate if Referral code or not

        $ordersByCouponAndUser = Order::where('coupon_id', $coupon->id)
            ->where('payment_status', Constant::PaidOrder)
            ->where('user_id', $user->id)
            ->count();

        $day = date('l', strtotime($orderDate));

        $dayId = (new General)->getConstantItemByTitle('Days', $day);
        if ($isReferral && $ordersByCouponAndUser < 1) {
            //get user order count
            $ordersByCouponAndUser = Order::where('user_id', $user->id)->count();
            if ($ordersByCouponAndUser > 0) {
                //mean that this user apply another oner before
                $result = ['error' => 1, 'message' => __('messages.UserAlreadyHasOrder')];

                return ['result' => $result, 'valid' => $valid];
            }
        }
        if ($ordersByCouponAndUser >= $coupon->maximum_usage && $coupon->maximum_usage != -1) {
            if ($isReferral) {
                // check if user have order
                $result = ['error' => 1, 'message' => __('messages.CouponUsageReachMaxReferral')];
            } else {
                $result = ['error' => 1, 'message' => __('messages.CouponUsageReachMax')];
            }
        } elseif ($coupon->active == 0) {
            $result = ['error' => 1, 'message' => __('messages.CouponNotActive')];
        } elseif ($coupon->started_at != null && date('Y-m-d') < $coupon->started_at) {
            $result = ['error' => 1, 'message' => __('messages.CouponNotStarted')];
        } elseif ($coupon->expired_at != null && date('Y-m-d') > $coupon->expired_at) {
            $result = ['error' => 1, 'message' => __('messages.CouponExpired')];
        } elseif (
            $coupon->coupon_type == Constant::RegisterCouponType &&
            (auth()->user()->id != $coupon->user_id ||
                Order::where('payment_status', Constant::PaidOrder)
                    ->where('device_uuid', $device_uuid)
                    ->orWhere('user_id', auth()->user()->id)
                    ->count() > 0)
        ) {
            //check if register coupon is valid
            $result = ['error' => 1, 'message' => __('messages.CouponExpired')];
        } elseif (
            $coupon->coupon_type == Constant::CouponOrderType &&
            (auth()->user()->id != $coupon->user_id ||
                ! in_array(
                    $brand_id,
                    ToCouponBrand::where('coupon_id', $coupon->id)
                        ->pluck('brand_id')
                        ->toArray()
                ) ||
                Order::where('coupon_id', $coupon->id)
                    ->where('payment_status', Constant::PaidOrder)
                    ->where('device_uuid', $device_uuid)
                    ->count() > 0 ||
                ($coupon->validate_type == 0 &&
                    ! in_array(
                        $dayId['id'],
                        CouponDate::where('coupon_id', $coupon->id)
                            ->pluck('day_id')
                            ->toArray()
                    )))
        ) {
            //order coupon check if it valid to no days or on days
            $result = ['error' => 1, 'message' => __('messages.CouponInvalid')];
        } elseif (
            $coupon->coupon_type == Constant::BirthDateCouponType &&
            (auth()->user()->id != $coupon->user_id ||
                Order::where('coupon_id', $coupon->id)
                    ->where('payment_status', Constant::PaidOrder)
                    ->where('device_uuid', $device_uuid)
                    ->count() > 0)
        ) {
            //order coupon
            $result = ['error' => 1, 'message' => __('messages.CouponInvalid')];
        } elseif (
            $coupon->coupon_type == Constant::CouponOneTimeType &&
            Order::where('coupon_id', $coupon->id)
                ->where('payment_status', Constant::PaidOrder)
                ->count() > 0
        ) {
            $result = ['error' => 1, 'message' => __('messages.CouponUsageReachMax')];
        } elseif (
            ($coupon->coupon_type == Constant::SingleCouponType ||
                $coupon->coupon_type == Constant::CouponOneTimeType) &&
            CouponDate::where('coupon_id', $coupon->id)->count() > 0 &&
            ! in_array(
                date('Y-m-d', strtotime($orderDate)),
                CouponDate::where('coupon_id', $coupon->id)
                    ->pluck('date')
                    ->toArray()
            )
        ) {
            $result = ['error' => 1, 'message' => __('messages.CouponNotAppliedOnDate')];
        } else {
            $valid = 1;
        }

        return ['result' => $result, 'valid' => $valid];
    }
}
