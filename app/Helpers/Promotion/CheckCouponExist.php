<?php

namespace App\Helpers\Promotion;

use App\Helpers\Constant;
use App\Models\Promotion\Coupon\Coupon;

class CheckCouponExist
{
    public static function validate($coupon_code, $user, $brand_id, $card_digits)
    {
        return Coupon::where('code', $coupon_code)
            ->where(function ($q) use ($brand_id, $card_digits) {
                $q->where(function ($query) use ($brand_id) {
                    // $query->where(function ($q1) use ($brand_id) {
                    //     $q1->where('brand_id', $brand_id)
                    //         ->NotUser()
                    //         ->where('coupon_type', '!=', Constant::CouponBankOfferType);
                    // });
                    $query->where(function ($q1) use ($brand_id) {
                        if (isset($brand_id)) {
                            $q1->where(function ($q2) use ($brand_id) {
                                $q2->where('brand_id', $brand_id);
                                $q2->orWhereNull('brand_id');
                            });
                        }
                        $q1->NotUser()->where('coupon_type', 0);
                    });

                    $query->orWhere(function ($q1) {
                        $q1->whereNull('brand_id')
                            ->UserType()
                            ->where('coupon_type', '>', 0)
                            ->where('coupon_type', '!=', Constant::CouponBankOfferType);
                    });
                });
                if (! empty($card_digits)) {
                    $q->orWhere(function ($query) use ($brand_id, $card_digits) {
                        $query->BankOffer()->whereHas('bank_offer', function ($q2) use ($card_digits, $brand_id) {
                            $q2->whereJsonContains('card_inits', $card_digits);
                            $q2->publish();
                            if (isset($brand_id)) {
                                $q2->where(function ($q3) use ($brand_id) {
                                    $q3->where('brand_id', $brand_id)->orWhereNull('brand_id');
                                });
                            }
                        });
                    });
                }
            })
            ->publish()
            ->first();
    }
}
