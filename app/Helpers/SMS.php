<?php

namespace App\Helpers;

class SMS
{
    public static function send($mobile, $message, $countryCode = '+966')
    {
        $profileId = config('app.sms_profile_id');
        $password = config('app.sms_password');
        $senderId = config('app.sms_sender_id');
        $url =
            'http://mshastra.com/sendurlcomma.aspx?user=' .
            $profileId .
            '&pwd=' .
            $password .
            '&senderid=' .
            $senderId .
            '&language=Unicode' .
            '&CountryCode=' .
            $countryCode .
            '&mobileno=' .
            $mobile .
            '&msgtext=' .
            urlencode($message);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $curl_scraped_page = curl_exec($ch);
        curl_close($ch);

        return $curl_scraped_page;
    }
}
