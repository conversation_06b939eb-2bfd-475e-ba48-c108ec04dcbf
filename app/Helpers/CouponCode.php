<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Config;
use App\Models\Promotion\Coupon\Coupon;

class CouponCode
{
    public static function generate()
    {
        $uniqueStr = '';
        $uniqueStr = Str::random(5);
        while (Coupon::where('code', $uniqueStr)->exists()) {
            $uniqueStr = Str::random(5);
        }

        return $uniqueStr;
    }

}

