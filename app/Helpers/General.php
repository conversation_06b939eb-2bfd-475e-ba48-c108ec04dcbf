<?php

namespace App\Helpers;

class General
{
    public function getConstantItemByTitle($arr, $title)
    {
        $item = null;
        $constant = new Constant();
        foreach ($constant->get_constant($arr) as $insideItem) {
            if (strtolower($insideItem['title_en']) === strtolower($title)) {
                $item = $insideItem;
                break;
            }
        }

        return $item;
    }

    public function getConstantItemById($arr, $id)
    {
        $item = null;
        $constant = new Constant();
        foreach ($constant->get_constant($arr) as $insideItem) {
            if (intval($insideItem['id']) == $id) {
                $item = $insideItem;
                break;
            }
        }

        return $item;
    }
}
