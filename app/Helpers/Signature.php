<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Config;

class Signature
{
    public function calculateSignature($order, $merchant_reference, $fort_id, $order_description, $command, $amount)
    {
        $shaString = '';
        $access_code = Config::get('app.payFort_access_code');
        $merchant_identifier = Config::get('app.payFort_merchant');
        $payFort_SHARequest = Config::get('app.payFort_SHARequest');

        if ($order->digital_wallet == 'APPLE_PAY') {
            $access_code = Config::get('app.apple_payFort_access_code');
            $payFort_SHARequest = Config::get('app.apple_payFort_SHARequest');
        }
        $arrData = [
            'command' => $command,
            'access_code' => $access_code,
            'merchant_identifier' => $merchant_identifier,
            'merchant_reference' => $merchant_reference,
            'amount' => (string) ($amount * 100),
            'currency' => 'SAR',
            'language' => 'en',
            'fort_id' => $fort_id,
            // 'order_description' => $order_description,
            'maintenance_reference' => (string) $order->id,
        ];
        ksort($arrData);
        foreach ($arrData as $key => $value) {
            $shaString .= "$key=$value";
        }
        $shaString = $payFort_SHARequest.$shaString.$payFort_SHARequest;
        $signature = hash('sha256', $shaString);
        $arrData['signature'] = $signature;

        return $arrData;
    }

    public function calculateSignatureSDKToken($command, $device_id)
    {
        $shaString = '';

        $arrData = [
            'access_code' => Config::get('app.payFort_access_code'),
            'merchant_identifier' => Config::get('app.payFort_merchant'),
            'language' => 'en',
            'device_id' => $device_id,
            'service_command' => $command,
        ];
        ksort($arrData);
        foreach ($arrData as $key => $value) {
            $shaString .= "$key=$value";
        }
        $shaString = Config::get('app.payFort_SHARequest').$shaString.Config::get('app.payFort_SHARequest');
        $signature = hash('sha256', $shaString);
        $arrData['signature'] = $signature;

        return $arrData;
    }
}
