<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;

class FCM
{
    public static function send($tokens, $title, $message, $other)
    {
        $data = [];
        if (! empty($tokens) && count($tokens) > 0) {
            if (! empty($other) && count($other) > 0) {
                foreach ($other as $key => $value) {
                    $data[$key] = $value.'';
                }
            }

            foreach ($tokens as $token) {
                (new FCM())->callApi($token, $title, $message, $data);
            }
        }

        return 'done';
    }

    public function callApi($tokens, $title, $message, $other)
    {
        $apiurl = config('app.fcm_url_v1');
        $fcmToken = $this->getGoogleAccessToken();

        $notification = [
            'title' => $title,
            'body' => $message,
        ];

        $payload = [
            'message' => [
                'token' => $tokens,
                'notification' => $notification,
                'data' => $other,
            ],
        ];

        $headers = ['Authorization: Bearer '.$fcmToken, 'Content-Type: application/json'];

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $apiurl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        $response = curl_exec($ch);

        if ($response === false) {
            Log::error($response);
        }
        curl_close($ch);

        $response = json_decode($response, true);

        return $response;
    }

    private function getGoogleAccessToken()
    {
        $credentialsFilePath = 'sala-hub-firebase.json';
        $client = new \Google_Client();
        $client->setAuthConfig(base_path($credentialsFilePath));
        $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
        $client->refreshTokenWithAssertion();
        $token = $client->getAccessToken();

        return $token['access_token'];
    }
}
