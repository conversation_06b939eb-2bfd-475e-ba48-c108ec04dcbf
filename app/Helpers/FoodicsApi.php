<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Config;

class FoodicsApi
{
    public static function sendRequest($name, $data, $type, $token)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => Config::get('app.foodics_url') . $name,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $type,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $token,
            ],
            CURLOPT_POSTFIELDS => json_encode($data),
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        $response = json_decode($response);
        if ($type == 'GET') {
            return $response->data ?? [];
        }

        return $response->data ?? $response;
    }
}
