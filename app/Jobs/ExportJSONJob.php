<?php

namespace App\Jobs;

use App\Helpers\ExportJSON;
use App\Http\Resources\EmbedUsersReportResource;
use App\Http\Resources\FoodicsReportResource;
use App\Http\Resources\OrderReportResource;
use App\Http\Resources\PriceHistoryResource;
use App\Http\Resources\TicketReportResource;
use App\Http\Resources\TicketsReportResource;
use App\Http\Resources\UsersReportResource;
use App\Mail\ExportMail;
use App\Models\ActivityLog;
use App\Models\Booking\Ticket\Ticket;
use App\Models\Ordering\Addon\OrderAddon;
use App\Models\Ordering\Order;
use App\Models\Ordering\Ticket\OrderTicket;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Mail;

class ExportJSONJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $data;

    public $user;

    public $filePath;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($filePath, $data, $user)
    {
        $this->user = $user;
        $this->data = $data;
        $this->filePath = $filePath;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $exportType = 'json';
        $data = $this->data;
        $user = $this->user;
        $type = $data['type'];
        $filename = $this->filePath;
        $dataEmail['subject'] = '';
        $brand = Cookie::get('brandId') !== null && Cookie::get('brandId') !== '' ? Cookie::get('brandId') : '';
        $dataEmail = [];
        if ($type == 0) {
            $dataEmail['subject'] = 'Export/Item-Master-Reports.json';
            $filename = 'exports/reports/json/'.time().'-item-master.'.$exportType;
            $items = $this->exportTicketCreatedReportJSON($data, $brand);
        } elseif ($type == 1) {
            $filename = 'exports/reports/json/'.time().'-Price-history.'.$exportType;
            $dataEmail['subject'] = 'Export/Price-History-Reports.json';
            $items = $this->exportTicketUpdatedReportJSON($data, $brand);
        } elseif ($type == 2) {
            $filename = 'exports/reports/json/'.time().'-sale-reports.'.$exportType;
            $dataEmail['subject'] = 'Export/Sale-Reports.json';
            $items = $this->exportSaleReportJSON($data, $brand);
        } elseif ($type == 3) {
            $filename = 'exports/reports/json/'.time().'-Foodics-Sale-Reports.'.$exportType;
            $dataEmail['subject'] = 'Export/Foodics-Sale-Reports.json';
            $items = $this->exportFoodicsReportJSON($data, $brand);
        } elseif ($type == 4) {
            $filename = 'exports/reports/json/'.time().'-Tickets-Sale-Reports.'.$exportType;
            $dataEmail['subject'] = 'Export/Tickets-Sale-Reports.json';
            $items = $this->exportTicketsReportJSON($data, $brand);
        } elseif ($type == 5) {
            $filename = 'exports/reports/json/'.time().'-users.'.$exportType;
            $dataEmail['subject'] = 'Export/Users-Sale-Reports.json';
            $items = $this->exportUsersReportJSON($data);
        } elseif ($type == 6) {
            $filename = 'exports/reports/json/'.time().'-embed-card-users.'.$exportType;
            $dataEmail['subject'] = 'Export/Embed-Card-Reports.json';
            $items = $this->exportEmbedCardsReportJSON($data);
        }
        $pathLink = ExportJSON::generateLink($items, $filename);
        $dataEmail['user'] = $user;
        $dataEmail['file'] = $pathLink;

        Mail::to($user->email)->send(new ExportMail($dataEmail));
    }

    public function exportSaleReportJSON($data, $brand)
    {
        $orders = Order::whereHas('branch')
            ->with('branch', function ($q) {
                $q->with('governorate');
            })
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');

        $orders = $orders->where('is_parent', 0);
        $orders = $orders->with('corporate');
        $city = $data['city'];

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if (isset($city) && $city != '') {
            $orders = $orders->whereHas('branch', function ($q) use ($city) {
                $q->where('governorate_id', $city);
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }

        return OrderReportResource::collection($orders->get());
    }

    public function exportTicketCreatedReportJSON($data, $brand)
    {
        $city = $data['city'];
        $tickets = Ticket::whereHas('brand')->orderBy('id', 'asc');
        if ($brand != '') {
            $tickets = $tickets->where('brand_id', $brand);
        }
        if ($city != '') {
            $tickets = $tickets->whereHas('branches', function ($q) use ($city) {
                $q->whereHas('branch', function ($q1) use ($city) {
                    $q1->where('governorate_id', $city);
                });
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $tickets = $tickets->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $tickets = $tickets->whereDate('created_at', '<=', $data['toDate']);
        }
        $ids = $tickets->pluck('id')->toArray();

        $activities = ActivityLog::where('event', 'created')
            ->whereIn('subject_id', $ids)
            ->where('subject_type', 'App\Models\Booking\Ticket\Ticket');

        return TicketReportResource::Collection($activities->get());
    }

    public function exportTicketUpdatedReportJSON($data, $brand)
    {
        $tickets = Ticket::whereHas('brand')->orderBy('id', 'asc');
        $city = $data['city'];

        if ($brand != '') {
            $tickets = $tickets->where('brand_id', $brand);
        }

        if ($city != '') {
            $tickets = $tickets->whereHas('branches', function ($q) use ($city) {
                $q->whereHas('branch', function ($q1) use ($city) {
                    $q1->where('governorate_id', $city);
                });
            });
        }

        $ids = $tickets->pluck('id')->toArray();

        $activities = ActivityLog::where('event', 'updated')
            ->whereIn('subject_id', $ids)
            ->where('subject_type', 'App\Models\Booking\Ticket\Ticket');

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $activities = $activities->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $activities = $activities->whereDate('created_at', '<=', $data['toDate']);
        }

        return PriceHistoryResource::collection($activities->get());
    }

    public function exportFoodicsReportJSON($data, $brand)
    {
        $orders = Order::whereHas('branch')
            ->with('branch', function ($q) {
                $q->with('governorate');
            })
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');

        $orders = $orders->where('is_parent', 0);
        $orders = $orders->with('corporate');
        $city = $data['city'];

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if ($city != '') {
            $orders = $orders->whereHas('branch', function ($q) use ($city) {
                $q->where('governorate_id', $city);
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }
        $ids = $orders->pluck('id')->toArray();

        $orders = OrderAddon::whereIn('order_id', $ids)->with('order');

        return FoodicsReportResource::collection($orders->get());
    }

    public function exportTicketsReportJSON($data, $brand)
    {
        $orders = Order::whereHas('branch')
            ->with('branch', function ($q) {
                $q->with('governorate');
            })
            ->whereHas('brand')
            ->orderBy('order_date', 'desc');

        $orders = $orders->where('is_parent', 0);
        $orders = $orders->with('corporate');
        $city = $data['city'];

        if ($brand != '') {
            $orders = $orders->where('brand_id', $brand);
        }
        if ($city != '') {
            $orders = $orders->whereHas('branch', function ($q) use ($city) {
                $q->where('governorate_id', $city);
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $orders = $orders->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $orders = $orders->whereDate('created_at', '<=', $data['toDate']);
        }
        $ids = $orders->pluck('id')->toArray();

        $orders = OrderTicket::whereIn('order_id', $ids)->with('order');

        return TicketsReportResource::collection($orders->get());
    }

    // exportEmbedCardsReportJSON
    public function exportEmbedCardsReportJSON($data)
    {
        $users = User::whereHas('embedCards')->whereNull('deleted_at');

        $search = $data['search'] ?? '';
        $active = $data['active'] ?? '';

        if (isset($search) && $search != '') {
            $users = $users->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%')
                    ->orWhere('name', 'like', '%'.$search.'%')
                    ->orWhere('mobile', 'like', '%'.$search.'%')
                    ->orWhere('nationality', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhere('date_of_birth', 'like', '%'.$search.'%')
                    ->orWhere('country', 'like', '%'.$search.'%')
                    ->orWhere('city', 'like', '%'.$search.'%');
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $users = $users->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $users = $users->whereDate('created_at', '<=', $data['toDate']);
        }
        if (isset($active) && $active != '') {
            $users = $users->where('active', $active);
        }

        return EmbedUsersReportResource::collection($users->get());
    }

    public function exportUsersReportJSON($data)
    {
        $users = User::whereNull('deleted_at');

        $search = $data['search'] ?? '';
        $active = $data['active'] ?? '';

        if (isset($search) && $search != '') {
            $users = $users->where(function ($q) use ($search) {
                $q->where('id', 'like', '%'.$search.'%')
                    ->orWhere('name', 'like', '%'.$search.'%')
                    ->orWhere('mobile', 'like', '%'.$search.'%')
                    ->orWhere('nationality', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhere('date_of_birth', 'like', '%'.$search.'%')
                    ->orWhere('country', 'like', '%'.$search.'%')
                    ->orWhere('city', 'like', '%'.$search.'%');
            });
        }

        if (isset($data['fromDate']) && $data['fromDate'] != '') {
            $users = $users->whereDate('created_at', '>=', $data['fromDate']);
        }
        if (isset($data['toDate']) && $data['toDate'] != '') {
            $users = $users->whereDate('created_at', '<=', $data['toDate']);
        }
        if (isset($active) && $active != '') {
            $users = $users->where('active', $active);
        }

        return UsersReportResource::collection($users->get());
    }
}
