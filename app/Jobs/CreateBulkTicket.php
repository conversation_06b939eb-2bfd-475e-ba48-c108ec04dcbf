<?php

namespace App\Jobs;

use App\Helpers\SMS;
use Illuminate\Support\Str;
use App\Mail\BulkTicketsMail;
use Illuminate\Bus\Queueable;
use App\Models\Ordering\Order;
use App\Models\Corporate\Corporate;
use App\Models\Ordering\OrderBranch;
use Illuminate\Support\Facades\Mail;
use App\Models\Booking\Ticket\Ticket;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Ordering\Ticket\OrderTicket;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class CreateBulkTicket implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $order;
    public $generalHelper;
    public $orderItems;
    public $branches;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderItems, $order, $generalHelper, $branches)
    {
        $this->order = $order;
        $this->orderItems = $orderItems;
        $this->generalHelper = $generalHelper;
        $this->branches = $branches;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //
        foreach ($this->orderItems as $item) {
            $quantity = $item['quantity'];
            // $orderItems = array_chunk($this->orderItems, 100);
            for ($i = 0; $i < $quantity; $i++) {
                $childOrder = [];
                $childOrder['is_bulk'] = 1;
                $childOrder['payment_method'] = $this->generalHelper->getConstantItemByTitle(
                    'PaymentMethod',
                    'Bank Transfer'
                )['id'];
                $childOrder['status'] = $this->generalHelper->getConstantItemByTitle('OrderStatus', 'Upcoming')['id'];
                $childOrder['payment_status'] = $this->generalHelper->getConstantItemByTitle(
                    'PaymentStatus',
                    'waiting'
                )['id'];
                $totalItemsPrice = $item['price'];
                $childOrder['order_uuid'] = Str::orderedUuid();
                $childOrder['price'] = $totalItemsPrice;
                $childOrder['total_price'] = $totalItemsPrice;
                $childOrder['order_date'] = date('Y-m-d', strtotime($this->order->order_date));
                $childOrder['expire_date'] = date('Y-m-d', strtotime($this->order->expire_date));
                $childOrder['order_number'] = $this->order->id . uniqid('Sala');
                $childOrder['parent'] = $this->order->id;
                $childOrder['brand_id'] = $this->order->brand_id;
                $childOrder['branch_id'] = $this->order->branch_id;
                $childOrder['corporate_request'] = $this->order->corporate_request;
                $childOrder = Order::create($childOrder);

                $ticket = Ticket::where('id', $item['id'])->first();
                $orderTicket = [];
                $orderTicket['price'] = $item['price'];
                $orderTicket['quantity'] = 1;
                $orderTicket['total_price'] = $item['price'];
                $orderTicket['ticket_id'] = $item['id'];
                $orderTicket['order_id'] = $childOrder->id;
                $orderTicket = OrderTicket::create($orderTicket);
                foreach ($this->branches as $branchId) {
                    OrderBranch::create([
                        'order_id' => $childOrder->id,
                        'branch_id' => $branchId,
                    ]);
                }
                // if (!$orderTicket) {
                //     DB::rollBack();
                //     break;
                // }

                $this->updateExtraInfo($orderTicket);
            }
        }

        $this->sendBulkOrderUpdateEmail($this->order);
    }

    public function updateExtraInfo($orderTicket)
    {
        $info = [];
        $bookingItem = Ticket::where('id', $orderTicket['ticket_id'])->first();
        $info['ar']['title'] = $bookingItem->getTitleArAttribute();
        $info['en']['title'] = $bookingItem->getTitleEnAttribute();
        $info['ar']['sub_title'] = $bookingItem->getSubTitleArAttribute();
        $info['en']['sub_title'] = $bookingItem->getSubTitleEnAttribute();
        $orderTicket->update($info);
    }

    public function sendBulkOrderUpdateEmail($order)
    {
        $corporate = Corporate::find($order->corporate_request);
        $childeOrders = $order->children;
        $emailData = [];
        $emailData['tickets'] = $childeOrders;
        $emailData['order_number'] = $order->order_number;
        $emailData['corporate_name'] = $corporate->name;
        $emailData['content'] = 'Order created Successful, you can check order status on the following link:';
        $emailData['link'] = route('orders.preview', $order->order_uuid);

        $mailDataView = new BulkTicketsMail($emailData);
        Mail::to($corporate['email'])->send($mailDataView);
        SMS::send($corporate['mobile'], __('messages.bulkOrderSMS', ['link' => $emailData['link']]));
    }
}