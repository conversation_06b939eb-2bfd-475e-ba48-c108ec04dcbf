<?php

namespace App\Jobs;

use App\Mail\ExportMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class ExportEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $user;

    public $file;

    public $subject;

    public function __construct($file, $subject)
    {
        $this->user = auth()->user();
        $this->file = $file;
        $this->subject = $subject;
    }

    public function handle()
    {
        //send email and notify in admin
        $data = [];
        $data['user'] = $this->user;
        $data['file'] = $this->file;
        $data['subject'] = $this->subject;
        // $this->user->notify(new ExportReady());

        Mail::to($this->user->email)->send(new ExportMail($data));
        //email now
    }
}
